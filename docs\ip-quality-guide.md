# دليل نظام جودة IP

## نظرة عامة
نظام جودة IP هو أداة متقدمة لتقييم وفحص عناوين IP للكشف عن التهديدات والاحتيال، مما يساعد في تحسين جودة الزيارات وحماية النظام من الأنشطة المشبوهة.

## الخدمات المدعومة

### 1. IPQualityScore
- **الموقع**: https://ipqualityscore.com
- **المزايا**: دقة عالية، تحليل شامل، قاعدة بيانات كبيرة
- **الحد المجاني**: 5,000 استعلام شهرياً
- **التكلفة**: من $25/شهر للخطط المدفوعة

### 2. ProxyCheck.io
- **الموقع**: https://proxycheck.io
- **المزايا**: سرعة عالية، واجهة بسيطة
- **الحد المجاني**: 1,000 استعلام يومياً
- **التكلفة**: من $10/شهر للخطط المدفوعة

## الميزات الرئيسية

### 1. فحص شامل للـ IP
- **درجة الجودة**: من 0 إلى 100
- **درجة الاحتيال**: مؤشر المخاطر
- **الموقع الجغرافي**: البلد، المدينة، المنطقة
- **مزود الخدمة**: ISP والمؤسسة

### 2. كشف التهديدات
- **VPN**: شبكات خاصة افتراضية
- **Proxy**: خوادم وكيلة
- **Tor**: شبكة Tor المجهولة
- **Bots**: برامج الزحف والروبوتات

### 3. نظام تخزين مؤقت ذكي
- **تخزين النتائج**: تجنب الاستعلامات المتكررة
- **مدة قابلة للتخصيص**: من 1 إلى 168 ساعة
- **تنظيف تلقائي**: حذف البيانات المنتهية الصلاحية

## إعداد النظام

### 1. الحصول على مفتاح API

#### IPQualityScore:
1. سجل في https://ipqualityscore.com
2. اذهب إلى Dashboard → API Keys
3. انسخ Private Key

#### ProxyCheck.io:
1. سجل في https://proxycheck.io
2. اذهب إلى Dashboard → API
3. انسخ API Key

### 2. إعداد النظام
1. اذهب إلى **الإدارة → جودة IP**
2. فعّل **تفعيل فحص جودة IP**
3. اختر **خدمة الفحص**
4. أدخل **مفتاح API**
5. اضبط **الإعدادات المتقدمة**

## الإعدادات المتاحة

### 1. الإعدادات الأساسية

#### تفعيل فحص جودة IP
```
القيمة: تشغيل/إيقاف
الوصف: تفعيل أو إلغاء تفعيل النظام بالكامل
الافتراضي: مفعل
```

#### خدمة الفحص
```
الخيارات: IPQualityScore, ProxyCheck.io
الوصف: اختيار مزود خدمة فحص IP
الافتراضي: IPQualityScore
```

#### مفتاح API
```
النوع: نص
الوصف: مفتاح API من مزود الخدمة
مطلوب: نعم
```

### 2. إعدادات الفلترة

#### الحد الأدنى لدرجة الجودة
```
النطاق: 0-100
الوصف: أقل درجة مقبولة للـ IP
الافتراضي: 75
التوصية: 75-85 للاستخدام العادي
```

#### حظر VPN والبروكسي
```
القيمة: تشغيل/إيقاف
الوصف: منع الوصول من VPN والبروكسي
الافتراضي: مفعل
```

#### حظر شبكة Tor
```
القيمة: تشغيل/إيقاف
الوصف: منع الوصول من شبكة Tor
الافتراضي: مفعل
```

### 3. إعدادات الكاش

#### مدة تخزين الكاش
```
النطاق: 1-168 ساعة
الوصف: مدة الاحتفاظ بنتائج الفحص
الافتراضي: 24 ساعة
التوصية: 24-72 ساعة
```

## كيفية عمل النظام

### 1. عند زيارة المستخدم
```
مستخدم يزور الموقع
    ↓
استخراج عنوان IP
    ↓
فحص الكاش المحلي
    ↓
إذا موجود → استخدام النتيجة المخزنة
إذا غير موجود → استعلام API
    ↓
تقييم النتيجة حسب الإعدادات
    ↓
السماح أو الحظر
```

### 2. تقييم جودة IP
```php
// فحص جودة IP
$ip_info = $ipQuality->checkIPQuality($user_ip);

// التحقق من السماح
$is_allowed = $ipQuality->isIPAllowed($user_ip);

if (!$is_allowed) {
    // حظر المستخدم
    die('IP غير مسموح');
}
```

## استخدام النظام

### 1. في نظام التتبع
```php
// فحص جودة IP قبل تسجيل النقرة
if (!$ipQuality->isIPAllowed($user_ip)) {
    $ip_info = $ipQuality->checkIPQuality($user_ip);
    
    $reasons = [];
    if ($ip_info['quality_score'] < 75) {
        $reasons[] = "جودة منخفضة ({$ip_info['quality_score']}/100)";
    }
    if ($ip_info['is_vpn']) $reasons[] = 'VPN';
    if ($ip_info['is_tor']) $reasons[] = 'Tor';
    
    $reason_text = implode(', ', $reasons);
    die("عذراً، لا يمكن الوصول من هذا IP. السبب: {$reason_text}");
}
```

### 2. في صفحات العروض
```php
// فلترة العروض حسب جودة IP
$ip_quality_check = $ipQuality->quickIPQualityCheck($user_ip);

if (!$ip_quality_check['allowed']) {
    // إخفاء العروض عالية القيمة
    $offers = filterLowValueOffers($offers);
}
```

### 3. عرض معلومات IP
```php
// عرض widget جودة IP
echo renderIPQualityWidget($user_ip, $database, true);

// عرض badge بسيط
echo renderSimpleIPQualityBadge($user_ip, $database);
```

## قاعدة البيانات

### جدول ip_quality_cache
```sql
CREATE TABLE ip_quality_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL UNIQUE,
    quality_score INT DEFAULT 0,
    fraud_score INT DEFAULT 0,
    country_code VARCHAR(2),
    country_name VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    isp VARCHAR(255),
    organization VARCHAR(255),
    is_vpn BOOLEAN DEFAULT FALSE,
    is_proxy BOOLEAN DEFAULT FALSE,
    is_tor BOOLEAN DEFAULT FALSE,
    is_crawler BOOLEAN DEFAULT FALSE,
    is_mobile BOOLEAN DEFAULT FALSE,
    connection_type VARCHAR(50),
    abuse_velocity VARCHAR(20),
    timezone VARCHAR(50),
    api_response JSON,
    last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### الفهارس المحسنة
```sql
INDEX idx_ip_address (ip_address)
INDEX idx_expires_at (expires_at)
INDEX idx_quality_score (quality_score)
INDEX idx_last_checked (last_checked)
```

## الإحصائيات والمراقبة

### 1. إحصائيات الكاش
- **إجمالي IPs مفحوصة**: العدد الكلي للسجلات
- **كاش نشط**: السجلات غير منتهية الصلاحية
- **متوسط درجة الجودة**: متوسط درجات الجودة
- **أنواع التهديدات**: عدد VPN, Proxy, Tor

### 2. مراقبة الأداء
```php
// إحصائيات الكاش
$stats = $ipQuality->getCacheStats();

echo "إجمالي السجلات: " . $stats['total_cached'];
echo "متوسط الجودة: " . $stats['avg_quality_score'];
echo "VPN مكتشفة: " . $stats['vpn_count'];
```

### 3. تتبع الاستخدام
- **استهلاك API**: مراقبة عدد الاستعلامات
- **معدل الكاش**: نسبة الاستعلامات المخزنة مؤقتاً
- **أنماط التهديدات**: تحليل أنواع التهديدات

## المهام التلقائية

### 1. تنظيف الكاش
```bash
# يومياً في الساعة 2:00 صباحاً
0 2 * * * /usr/bin/php /path/to/site/cron/cleanup-ip-quality-cache.php
```

### 2. تقارير دورية
```bash
# أسبوعياً يوم الأحد
0 8 * * 0 /usr/bin/php /path/to/site/cron/ip-quality-report.php
```

## أفضل الممارسات

### 1. اختيار الخدمة المناسبة
- **IPQualityScore**: للدقة العالية والتحليل المتقدم
- **ProxyCheck.io**: للسرعة والبساطة

### 2. ضبط الإعدادات
```
الاستخدام العادي:
- الحد الأدنى: 75
- حظر VPN: مفعل
- حظر Tor: مفعل
- مدة الكاش: 24 ساعة

الاستخدام الصارم:
- الحد الأدنى: 85
- حظر VPN: مفعل
- حظر Tor: مفعل
- مدة الكاش: 12 ساعة

الاستخدام المرن:
- الحد الأدنى: 60
- حظر VPN: معطل
- حظر Tor: مفعل
- مدة الكاش: 48 ساعة
```

### 3. مراقبة الأداء
- راجع الإحصائيات أسبوعياً
- راقب استهلاك API شهرياً
- نظف الكاش بانتظام
- اختبر الإعدادات دورياً

### 4. التوازن بين الأمان والتجربة
- لا تجعل الإعدادات صارمة جداً
- اختبر تأثير الإعدادات على المستخدمين
- وفر رسائل واضحة للمستخدمين المحظورين

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. API لا يعمل
**الأسباب:**
- مفتاح API خاطئ
- انتهاء الحد المجاني
- مشاكل في الشبكة

**الحلول:**
- تحقق من صحة المفتاح
- راجع استهلاك API
- اختبر الاتصال

#### 2. نتائج غير دقيقة
**الأسباب:**
- كاش قديم
- إعدادات خاطئة
- تغيير في خدمة API

**الحلول:**
- نظف الكاش
- راجع الإعدادات
- اختبر مع IPs معروفة

#### 3. أداء بطيء
**الأسباب:**
- كثرة استعلامات API
- كاش غير فعال
- قاعدة بيانات بطيئة

**الحلول:**
- زيد مدة الكاش
- حسن فهارس قاعدة البيانات
- استخدم CDN

### أدوات التشخيص

#### اختبار API
```php
// اختبار مفتاح API
$result = $ipQuality->checkIPQuality('*******');
print_r($result);
```

#### فحص الكاش
```php
// إحصائيات الكاش
$stats = $ipQuality->getCacheStats();
echo "معدل الإصابة: " . ($stats['active_cached'] / $stats['total_cached'] * 100) . "%";
```

## الأمان والخصوصية

### 1. حماية البيانات
- تشفير مفاتيح API
- حماية قاعدة البيانات
- تسجيل محدود للأنشطة

### 2. الامتثال للقوانين
- احترام قوانين الخصوصية
- عدم تخزين بيانات حساسة
- شفافية في الاستخدام

### 3. أفضل الممارسات الأمنية
- تحديث مفاتيح API دورياً
- مراقبة الأنشطة المشبوهة
- نسخ احتياطية منتظمة

---

**ملاحظة**: تأكد من اختبار النظام بعناية قبل التطبيق في الإنتاج، وراقب تأثيره على تجربة المستخدم ومعدلات التحويل.
