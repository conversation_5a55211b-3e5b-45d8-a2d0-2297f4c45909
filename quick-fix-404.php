<?php
/**
 * إصلاح سريع لأخطاء 404 في القائمة الجانبية
 */

echo "<h2>⚡ إصلاح سريع لأخطاء 404</h2>";

// إنشاء المجلدات الأساسية
$dirs = ['offers', 'tracking', 'reports', 'payments', 'offerwall', 'tools', 'help', 'admin', 'admin/users', 'admin/offers', 'admin/networks', 'admin/settings', 'admin/logs', 'admin/notifications', 'admin/reports'];

echo "<h3>1. إنشاء المجلدات:</h3>";
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ $dir<br>";
    }
}

echo "<h3>2. إنشاء الصفحات الأساسية:</h3>";

// قالب صفحة بسيط
function quickPage($title, $icon, $back = "../index.php") {
    return '<?php
require_once "' . ($back == "../index.php" ? "../" : "../../") . 'config/config.php";
if (!isLoggedIn()) { header("Location: ' . ($back == "../index.php" ? "../" : "../../") . 'auth/login.php"); exit(); }
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>' . $title . '</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body text-center p-5">
                        <i class="fas fa-' . $icon . ' fa-4x text-primary mb-4"></i>
                        <h3>' . $title . '</h3>
                        <p class="text-muted mb-4">هذه الصفحة قيد التطوير وستكون متاحة قريباً</p>
                        <a href="' . $back . '" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
}

// الصفحات الأساسية
$pages = [
    'offers/index.php' => quickPage('العروض', 'bullhorn'),
    'tracking/index.php' => quickPage('التتبع', 'link'),
    'reports/index.php' => quickPage('التقارير', 'chart-bar'),
    'payments/index.php' => quickPage('المدفوعات', 'money-bill-wave'),
    'offerwall/index.php' => quickPage('Offerwall', 'gift'),
    'temp-mail.php' => quickPage('البريد المؤقت', 'envelope', 'index.php'),
    
    // الأدوات
    'tools/link-generator.php' => quickPage('مولد الروابط', 'magic'),
    'tools/postback.php' => quickPage('Postback URLs', 'exchange-alt'),
    'tools/api.php' => quickPage('API', 'code'),
    
    // المساعدة
    'help/documentation.php' => quickPage('الدليل', 'book'),
    'help/support.php' => quickPage('الدعم الفني', 'life-ring'),
    'help/faq.php' => quickPage('الأسئلة الشائعة', 'question-circle'),
    
    // الإدارة
    'admin/users/index.php' => quickPage('إدارة المستخدمين', 'users', '../../index.php'),
    'admin/offers/index.php' => quickPage('إدارة العروض', 'plus-circle', '../../index.php'),
    'admin/networks/index.php' => quickPage('إدارة الشبكات', 'network-wired', '../../index.php'),
    'admin/settings/index.php' => quickPage('إعدادات النظام', 'cogs', '../../index.php'),
    'admin/logs/index.php' => quickPage('سجل النشاطات', 'file-alt', '../../index.php'),
    'admin/notifications/index.php' => quickPage('إدارة الإشعارات', 'bell', '../../index.php'),
    'admin/reports/username-stats.php' => quickPage('إحصائيات أسماء المستخدمين', 'chart-line', '../../index.php'),
    'admin/users/username-generator.php' => quickPage('مولد أسماء المستخدمين', 'magic', '../../index.php'),
    'admin/settings/ip-protection.php' => quickPage('حماية IP', 'shield-alt', '../../index.php'),
    'admin/settings/ip-quality.php' => quickPage('جودة IP', 'search-location', '../../index.php'),
    'admin/settings/notifications.php' => quickPage('إعدادات الإشعارات', 'bell-slash', '../../index.php')
];

foreach ($pages as $file => $content) {
    if (!file_exists($file)) {
        file_put_contents($file, $content);
        echo "✅ $file<br>";
    }
}

echo "<hr>";
echo "<h3>🎉 تم الإصلاح!</h3>";
echo "<div class='alert alert-success'>";
echo "<h5>✅ جميع روابط القائمة الجانبية تعمل الآن!</h5>";
echo "<ul>";
echo "<li>✅ لا توجد أخطاء 404 بعد الآن</li>";
echo "<li>✅ جميع الصفحات تحتوي على رسالة 'قريباً'</li>";
echo "<li>✅ أزرار العودة للرئيسية متاحة</li>";
echo "<li>✅ تصميم بسيط وجميل</li>";
echo "</ul>";
echo "</div>";

echo "<h4>🧪 اختبار الروابط:</h4>";
echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<h6>الأساسية:</h6>";
echo "<ul>";
echo "<li><a href='offers/'>العروض</a></li>";
echo "<li><a href='tracking/'>التتبع</a></li>";
echo "<li><a href='reports/'>التقارير</a></li>";
echo "<li><a href='payments/'>المدفوعات</a></li>";
echo "<li><a href='offerwall/'>Offerwall</a></li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<h6>الأدوات:</h6>";
echo "<ul>";
echo "<li><a href='tools/link-generator.php'>مولد الروابط</a></li>";
echo "<li><a href='tools/postback.php'>Postback URLs</a></li>";
echo "<li><a href='tools/api.php'>API</a></li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<h6>المساعدة:</h6>";
echo "<ul>";
echo "<li><a href='help/documentation.php'>الدليل</a></li>";
echo "<li><a href='help/support.php'>الدعم الفني</a></li>";
echo "<li><a href='help/faq.php'>الأسئلة الشائعة</a></li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='index.php' class='btn btn-success btn-lg'>🏠 العودة للصفحة الرئيسية</a>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3, h4, h5, h6 {
    color: #333;
}
.row {
    display: flex;
    flex-wrap: wrap;
}
.col-md-4 {
    flex: 0 0 33.333333%;
    padding: 0 15px;
}
.alert {
    padding: 15px;
    margin: 20px 0;
    border: 1px solid transparent;
    border-radius: 4px;
}
.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}
.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
}
.btn-success {
    background: #28a745;
}
.btn-lg {
    padding: 15px 30px;
    font-size: 18px;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
