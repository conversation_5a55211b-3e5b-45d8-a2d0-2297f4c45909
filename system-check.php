<?php
/**
 * فحص النظام للتأكد من جاهزية الاستضافة
 */

$checks = [];
$overall_status = true;

// فحص إصدار PHP
$php_version = phpversion();
$php_required = '7.4.0';
$php_ok = version_compare($php_version, $php_required, '>=');
$checks['PHP Version'] = [
    'status' => $php_ok,
    'current' => $php_version,
    'required' => $php_required . '+',
    'message' => $php_ok ? 'متوافق' : 'يتطلب PHP 7.4 أو أحدث'
];
if (!$php_ok) $overall_status = false;

// فحص المكتبات المطلوبة
$extensions = ['pdo', 'pdo_mysql', 'json', 'curl', 'mbstring'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    $checks["Extension: $ext"] = [
        'status' => $loaded,
        'current' => $loaded ? 'مثبت' : 'غير مثبت',
        'required' => 'مطلوب',
        'message' => $loaded ? 'متوفر' : 'غير متوفر - يرجى تفعيله'
    ];
    if (!$loaded) $overall_status = false;
}

// فحص الاتصال بقاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host=sql303.infinityfree.com;dbname=if0_39395085_q12;charset=utf8",
        "if0_39395085",
        "Qweeee12"
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $checks['Database Connection'] = [
        'status' => true,
        'current' => 'متصل',
        'required' => 'اتصال صحيح',
        'message' => 'الاتصال بقاعدة البيانات ناجح'
    ];
} catch (PDOException $e) {
    $checks['Database Connection'] = [
        'status' => false,
        'current' => 'فشل الاتصال',
        'required' => 'اتصال صحيح',
        'message' => 'خطأ: ' . $e->getMessage()
    ];
    $overall_status = false;
}

// فحص صلاحيات المجلدات
$directories = ['uploads', 'logs'];
foreach ($directories as $dir) {
    $writable = is_writable($dir) || (!file_exists($dir) && mkdir($dir, 0755, true));
    $checks["Directory: $dir"] = [
        'status' => $writable,
        'current' => $writable ? 'قابل للكتابة' : 'غير قابل للكتابة',
        'required' => 'قابل للكتابة',
        'message' => $writable ? 'الصلاحيات صحيحة' : 'يحتاج صلاحيات كتابة'
    ];
    if (!$writable) $overall_status = false;
}

// فحص ملفات الإعدادات
$config_files = ['config/config.php', 'config/database.php'];
foreach ($config_files as $file) {
    $exists = file_exists($file);
    $checks["Config: $file"] = [
        'status' => $exists,
        'current' => $exists ? 'موجود' : 'غير موجود',
        'required' => 'مطلوب',
        'message' => $exists ? 'ملف الإعدادات موجود' : 'ملف الإعدادات مفقود'
    ];
    if (!$exists) $overall_status = false;
}

// فحص HTTPS
$is_https = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
$checks['HTTPS'] = [
    'status' => $is_https,
    'current' => $is_https ? 'مفعل' : 'غير مفعل',
    'required' => 'مُوصى به',
    'message' => $is_https ? 'الاتصال آمن' : 'يُنصح بتفعيل HTTPS'
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام - CPA Marketing System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .check-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }
        .check-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="check-card">
            <div class="check-header">
                <i class="fas fa-cogs fa-3x mb-3"></i>
                <h3>فحص النظام</h3>
                <p class="mb-0">التحقق من جاهزية الاستضافة لنظام CPA</p>
            </div>
            
            <div class="p-4">
                <!-- الحالة العامة -->
                <div class="alert alert-<?php echo $overall_status ? 'success' : 'danger'; ?> mb-4">
                    <h5 class="alert-heading">
                        <i class="fas fa-<?php echo $overall_status ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        الحالة العامة
                    </h5>
                    <p class="mb-0">
                        <?php if ($overall_status): ?>
                            النظام جاهز للتثبيت! جميع المتطلبات متوفرة.
                        <?php else: ?>
                            يوجد مشاكل تحتاج إلى حل قبل التثبيت.
                        <?php endif; ?>
                    </p>
                </div>

                <!-- تفاصيل الفحص -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>المكون</th>
                                <th>الحالة الحالية</th>
                                <th>المطلوب</th>
                                <th>الرسالة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($checks as $name => $check): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($name); ?></strong></td>
                                <td><?php echo htmlspecialchars($check['current']); ?></td>
                                <td><?php echo htmlspecialchars($check['required']); ?></td>
                                <td><?php echo htmlspecialchars($check['message']); ?></td>
                                <td>
                                    <i class="fas fa-<?php echo $check['status'] ? 'check-circle status-ok' : 'times-circle status-error'; ?>"></i>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- معلومات الخادم -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">معلومات الخادم</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>نظام التشغيل:</strong> <?php echo php_uname('s'); ?></p>
                                <p><strong>إصدار PHP:</strong> <?php echo phpversion(); ?></p>
                                <p><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف'; ?></p>
                                <p><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">إعدادات قاعدة البيانات</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>الخادم:</strong> sql303.infinityfree.com</p>
                                <p><strong>قاعدة البيانات:</strong> if0_39395085_q12</p>
                                <p><strong>المستخدم:</strong> if0_39395085</p>
                                <p><strong>الحالة:</strong> 
                                    <span class="<?php echo isset($checks['Database Connection']) && $checks['Database Connection']['status'] ? 'status-ok' : 'status-error'; ?>">
                                        <?php echo isset($checks['Database Connection']) && $checks['Database Connection']['status'] ? 'متصل' : 'غير متصل'; ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الخطوات التالية -->
                <div class="mt-4">
                    <?php if ($overall_status): ?>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>الخطوات التالية:</h6>
                            <ol class="mb-0">
                                <li>اذهب إلى <a href="install.php">صفحة التثبيت</a></li>
                                <li>أكمل عملية التثبيت</li>
                                <li>سجل دخول كمدير</li>
                                <li>ابدأ في إضافة العروض والشبكات</li>
                            </ol>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>يجب حل المشاكل التالية:</h6>
                            <ul class="mb-0">
                                <?php foreach ($checks as $name => $check): ?>
                                    <?php if (!$check['status']): ?>
                                        <li><?php echo htmlspecialchars($name . ': ' . $check['message']); ?></li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="text-center mt-4">
                    <a href="install.php" class="btn btn-primary btn-lg <?php echo !$overall_status ? 'disabled' : ''; ?>">
                        <i class="fas fa-download me-2"></i>
                        متابعة التثبيت
                    </a>
                    <button onclick="location.reload()" class="btn btn-outline-secondary btn-lg ms-2">
                        <i class="fas fa-sync me-2"></i>
                        إعادة الفحص
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
