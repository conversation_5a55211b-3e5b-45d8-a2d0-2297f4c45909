<?php
/**
 * مهمة تنظيف الإشعارات المنتهية الصلاحية
 * يجب تشغيلها يومياً عبر Cron Job
 * 
 * إضافة إلى Cron:
 * 0 3 * * * /usr/bin/php /path/to/your/site/cron/cleanup-notifications.php
 */

// منع الوصول المباشر من المتصفح
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    die('Access denied. This script can only be run from command line.');
}

require_once __DIR__ . '/../config/config.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    $notificationManager = new NotificationManager($database);
    
    echo "[" . date('Y-m-d H:i:s') . "] بدء تنظيف الإشعارات المنتهية الصلاحية...\n";
    
    // تنظيف الإشعارات المنتهية الصلاحية
    $cleaned_count = $notificationManager->cleanExpiredNotifications();
    
    echo "[" . date('Y-m-d H:i:s') . "] تم حذف {$cleaned_count} إشعار منتهي الصلاحية\n";
    
    // تنظيف سجلات المستخدمين للإشعارات المحذوفة
    $cleanup_user_notifications_query = "DELETE un FROM user_notifications un
                                         LEFT JOIN notifications n ON un.notification_id = n.id
                                         WHERE n.id IS NULL";
    
    $cleanup_stmt = $db->prepare($cleanup_user_notifications_query);
    $cleanup_stmt->execute();
    $cleaned_user_records = $cleanup_stmt->rowCount();
    
    echo "[" . date('Y-m-d H:i:s') . "] تم حذف {$cleaned_user_records} سجل مستخدم للإشعارات المحذوفة\n";
    
    // تنظيف الإشعارات المخفية القديمة (أكثر من 30 يوم)
    $cleanup_dismissed_query = "DELETE un FROM user_notifications un
                                INNER JOIN notifications n ON un.notification_id = n.id
                                WHERE un.is_dismissed = 1 
                                AND un.dismissed_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
    
    $dismissed_stmt = $db->prepare($cleanup_dismissed_query);
    $dismissed_stmt->execute();
    $cleaned_dismissed = $dismissed_stmt->rowCount();
    
    echo "[" . date('Y-m-d H:i:s') . "] تم حذف {$cleaned_dismissed} سجل إشعار مخفي قديم\n";
    
    // الحصول على إحصائيات بعد التنظيف
    $stats = $notificationManager->getNotificationStats();
    $total_notifications = $stats['total_notifications'] ?? 0;
    $active_notifications = $stats['active_notifications'] ?? 0;
    $total_user_notifications = $stats['total_user_notifications'] ?? 0;
    
    echo "[" . date('Y-m-d H:i:s') . "] الإحصائيات الحالية:\n";
    echo "  - إجمالي الإشعارات: {$total_notifications}\n";
    echo "  - الإشعارات النشطة: {$active_notifications}\n";
    echo "  - سجلات المستخدمين: {$total_user_notifications}\n";
    
    // تسجيل النشاط في قاعدة البيانات
    if ($cleaned_count > 0 || $cleaned_user_records > 0 || $cleaned_dismissed > 0) {
        $log_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                      VALUES (NULL, 'notifications_cleanup', 'تنظيف تلقائي للإشعارات', :data)";
        
        $log_data = json_encode([
            'cleaned_notifications' => $cleaned_count,
            'cleaned_user_records' => $cleaned_user_records,
            'cleaned_dismissed' => $cleaned_dismissed,
            'remaining_total' => $total_notifications,
            'remaining_active' => $active_notifications,
            'cleanup_time' => date('Y-m-d H:i:s')
        ]);
        
        $log_stmt = $db->prepare($log_query);
        $log_stmt->bindParam(':data', $log_data);
        $log_stmt->execute();
    }
    
    // تحذير إذا كان عدد الإشعارات كبير جداً
    if ($total_notifications > 50000) {
        echo "[" . date('Y-m-d H:i:s') . "] تحذير: عدد الإشعارات كبير جداً ({$total_notifications})\n";
        echo "  يُنصح بمراجعة إعدادات الإشعارات وتقليل مدة الاحتفاظ\n";
        
        // يمكن إضافة إشعار للمدير هنا
        // sendAdminAlert("عدد الإشعارات كبير جداً: {$total_notifications}");
    }
    
    // تحسين جداول قاعدة البيانات
    echo "[" . date('Y-m-d H:i:s') . "] تحسين جداول قاعدة البيانات...\n";
    
    $optimize_queries = [
        "OPTIMIZE TABLE notifications",
        "OPTIMIZE TABLE user_notifications",
        "OPTIMIZE TABLE notification_settings"
    ];
    
    foreach ($optimize_queries as $query) {
        try {
            $db->exec($query);
        } catch (PDOException $e) {
            echo "[" . date('Y-m-d H:i:s') . "] تحذير: فشل في تحسين الجدول: " . $e->getMessage() . "\n";
        }
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] تم الانتهاء من التنظيف والتحسين بنجاح\n";
    
    // إحصائيات الأداء
    $memory_usage = memory_get_peak_usage(true);
    $memory_mb = round($memory_usage / 1024 / 1024, 2);
    echo "[" . date('Y-m-d H:i:s') . "] استهلاك الذاكرة: {$memory_mb} MB\n";
    
} catch (Exception $e) {
    $error_message = "خطأ في تنظيف الإشعارات: " . $e->getMessage();
    echo "[" . date('Y-m-d H:i:s') . "] {$error_message}\n";
    
    // تسجيل الخطأ
    logError($error_message);
    
    exit(1);
}

exit(0);
?>
