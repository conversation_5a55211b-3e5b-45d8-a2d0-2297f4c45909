<?php
/**
 * ملف التثبيت المحسن لنظام CPA Marketing
 * الإصدار: 2.0 - محسن ومطور
 */

// بدء الجلسة
session_start();

// منع الوصول إذا كان النظام مثبت بالفعل
if (file_exists('config/installed.lock')) {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>النظام مثبت بالفعل</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card mt-5">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                            <h2>🔒 النظام مثبت بالفعل</h2>
                            <p class="text-muted">النظام تم تثبيته وتكوينه بنجاح</p>
                            <div class="alert alert-info">
                                لإعادة التثبيت، احذف ملف <code>config/installed.lock</code>
                            </div>
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>الذهاب للموقع
                            </a>
                            <a href="auth/login.php" class="btn btn-success">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit();
}

$step = isset($_GET['step']) ? intval($_GET['step']) : 1;
$errors = [];
$success = [];

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // معالجة اختبار الاتصال
    if (isset($_POST['action']) && $_POST['action'] === 'test_connection') {
        $db_host = $_POST['db_host'] ?? 'localhost';
        $db_username = $_POST['db_username'] ?? 'root';
        $db_password = $_POST['db_password'] ?? '';
        $db_name = $_POST['db_name'] ?? 'cpa_system';

        try {
            $pdo = new PDO("mysql:host=$db_host", $db_username, $db_password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // اختبار الاتصال بقاعدة البيانات
            $pdo->exec("USE `$db_name`");
            echo "success";
        } catch (PDOException $e) {
            echo "error: " . $e->getMessage();
        }
        exit();
    }

    switch ($step) {
        case 1:
            // فحص المتطلبات
            $requirements_passed = checkRequirements();
            if ($requirements_passed) {
                header('Location: installer.php?step=2');
                exit();
            }
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $db_result = setupDatabase();
            if ($db_result['success']) {
                header('Location: installer.php?step=3');
                exit();
            } else {
                $errors = $db_result['errors'];
            }
            break;
            
        case 3:
            // إنشاء حساب المدير
            $admin_result = createAdminAccount();
            if ($admin_result['success']) {
                header('Location: installer.php?step=4');
                exit();
            } else {
                $errors = $admin_result['errors'];
            }
            break;
            
        case 4:
            // إنهاء التثبيت
            finishInstallation();
            break;
    }
}

// فحص المتطلبات
function checkRequirements() {
    global $errors;
    
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'cURL Extension' => extension_loaded('curl'),
        'JSON Extension' => extension_loaded('json'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'Config Directory Writable' => is_writable('config/'),
        'Logs Directory Writable' => is_writable('logs/') || mkdir('logs/', 0755, true),
        'Cache Directory Writable' => is_writable('cache/') || mkdir('cache/', 0755, true),
        'Backups Directory Writable' => is_writable('backups/') || mkdir('backups/', 0755, true),
    ];
    
    $all_passed = true;
    foreach ($requirements as $requirement => $passed) {
        if (!$passed) {
            $errors[] = "المتطلب غير متوفر: $requirement";
            $all_passed = false;
        }
    }
    
    return $all_passed;
}

// إعداد قاعدة البيانات
function setupDatabase() {
    global $errors;

    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_username = $_POST['db_username'] ?? 'root';
    $db_password = $_POST['db_password'] ?? '';
    $db_name = $_POST['db_name'] ?? 'cpa_system';

    try {
        // الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=$db_host", $db_username, $db_password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$db_name`");

        // قراءة وتنفيذ ملف SQL
        $sql_file = 'database/schema.sql';
        if (file_exists($sql_file)) {
            $sql_content = file_get_contents($sql_file);

            // إزالة تعليقات SQL والأسطر الفارغة
            $sql_content = preg_replace('/--.*$/m', '', $sql_content);
            $sql_content = preg_replace('/\/\*.*?\*\//s', '', $sql_content);
            $sql_content = preg_replace('/USE\s+\w+;/', '', $sql_content);

            // تقسيم الاستعلامات
            $queries = array_filter(array_map('trim', explode(';', $sql_content)));

            foreach ($queries as $query) {
                if (!empty($query) && strlen($query) > 5) {
                    try {
                        $pdo->exec($query);
                    } catch (PDOException $e) {
                        // تجاهل أخطاء الجداول الموجودة بالفعل
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            throw $e;
                        }
                    }
                }
            }
        } else {
            // إنشاء الجداول الأساسية يدوياً إذا لم يوجد ملف SQL
            createBasicTables($pdo);
        }

        // تحديث ملف database.php
        updateDatabaseConfig($db_host, $db_username, $db_password, $db_name);

        // حفظ بيانات قاعدة البيانات في الجلسة
        $_SESSION['db_configured'] = true;

        return ['success' => true];

    } catch (PDOException $e) {
        return ['success' => false, 'errors' => ['خطأ في قاعدة البيانات: ' . $e->getMessage()]];
    }
}

// إنشاء الجداول الأساسية إذا لم يوجد ملف SQL
function createBasicTables($pdo) {
    $tables = [
        "CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `email` varchar(100) NOT NULL,
            `password` varchar(255) NOT NULL,
            `first_name` varchar(50) DEFAULT NULL,
            `last_name` varchar(50) DEFAULT NULL,
            `role` enum('admin','publisher') DEFAULT 'publisher',
            `status` enum('active','inactive','banned') DEFAULT 'active',
            `balance` decimal(10,2) DEFAULT 0.00,
            `api_key` varchar(255) DEFAULT NULL,
            `last_login` timestamp NULL DEFAULT NULL,
            `login_count` int(11) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS `offers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text,
            `offer_link` text NOT NULL,
            `image_link` text,
            `payout` decimal(10,2) NOT NULL,
            `network` varchar(100) DEFAULT NULL,
            `category` varchar(100) DEFAULT NULL,
            `countries` text,
            `status` enum('active','inactive','paused') DEFAULT 'active',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS `clicks` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `offer_id` int(11) NOT NULL,
            `ip_address` varchar(45) NOT NULL,
            `user_agent` text,
            `referrer` text,
            `country` varchar(2) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `offer_id` (`offer_id`),
            KEY `ip_address` (`ip_address`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS `conversions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `click_id` int(11) NOT NULL,
            `user_id` int(11) NOT NULL,
            `offer_id` int(11) NOT NULL,
            `payout` decimal(10,2) NOT NULL,
            `status` enum('pending','approved','rejected','hold') DEFAULT 'pending',
            `transaction_id` varchar(255) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `click_id` (`click_id`),
            KEY `user_id` (`user_id`),
            KEY `offer_id` (`offer_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS `notifications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `message` text NOT NULL,
            `type` enum('info','success','warning','danger','offer','system') DEFAULT 'info',
            `target_audience` enum('all','active','inactive','new','custom') DEFAULT 'all',
            `target_users` text,
            `offer_id` int(11) DEFAULT NULL,
            `priority` enum('low','medium','high') DEFAULT 'medium',
            `show_popup` tinyint(1) DEFAULT 1,
            `show_banner` tinyint(1) DEFAULT 1,
            `show_sidebar` tinyint(1) DEFAULT 1,
            `auto_hide` tinyint(1) DEFAULT 1,
            `hide_after_seconds` int(11) DEFAULT 10,
            `start_date` timestamp DEFAULT CURRENT_TIMESTAMP,
            `end_date` timestamp NULL DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `created_by` int(11) NOT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS `settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(100) NOT NULL,
            `setting_value` text,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];

    foreach ($tables as $table_sql) {
        $pdo->exec($table_sql);
    }
}

// إنشاء حساب المدير
function createAdminAccount() {
    global $errors;

    $admin_username = $_POST['admin_username'] ?? 'admin';
    $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
    $admin_password = $_POST['admin_password'] ?? 'admin123';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $admin_first_name = $_POST['admin_first_name'] ?? 'مدير';
    $admin_last_name = $_POST['admin_last_name'] ?? 'النظام';
    $site_name = $_POST['site_name'] ?? 'CPA Marketing System';
    $site_url = $_POST['site_url'] ?? 'http://localhost';

    // التحقق من البيانات
    if (empty($admin_username) || strlen($admin_username) < 3) {
        return ['success' => false, 'errors' => ['اسم المستخدم يجب أن يكون 3 أحرف على الأقل']];
    }

    if (empty($admin_email) || !filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'errors' => ['البريد الإلكتروني غير صحيح']];
    }

    if (empty($admin_password) || strlen($admin_password) < 6) {
        return ['success' => false, 'errors' => ['كلمة المرور يجب أن تكون 6 أحرف على الأقل']];
    }

    if ($admin_password !== $confirm_password) {
        return ['success' => false, 'errors' => ['كلمات المرور غير متطابقة']];
    }

    try {
        require_once 'config/database.php';
        $database = new Database();
        $pdo = $database->getConnection();

        // التحقق من عدم وجود مدير أو مستخدم بنفس البيانات
        $check_query = "SELECT COUNT(*) FROM users WHERE username = :username OR email = :email";
        $check_stmt = $pdo->prepare($check_query);
        $check_stmt->bindParam(':username', $admin_username);
        $check_stmt->bindParam(':email', $admin_email);
        $check_stmt->execute();

        if ($check_stmt->fetchColumn() > 0) {
            return ['success' => false, 'errors' => ['اسم المستخدم أو البريد الإلكتروني موجود بالفعل']];
        }

        // إنشاء حساب المدير
        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
        $api_key = hash('sha256', uniqid(rand(), true));

        $insert_query = "INSERT INTO users (username, email, password, first_name, last_name, role, status, api_key, created_at)
                        VALUES (:username, :email, :password, :first_name, :last_name, 'admin', 'active', :api_key, NOW())";

        $insert_stmt = $pdo->prepare($insert_query);
        $insert_stmt->bindParam(':username', $admin_username);
        $insert_stmt->bindParam(':email', $admin_email);
        $insert_stmt->bindParam(':password', $hashed_password);
        $insert_stmt->bindParam(':first_name', $admin_first_name);
        $insert_stmt->bindParam(':last_name', $admin_last_name);
        $insert_stmt->bindParam(':api_key', $api_key);

        if (!$insert_stmt->execute()) {
            return ['success' => false, 'errors' => ['فشل في إنشاء حساب المدير']];
        }

        // تحديث ملف config.php
        updateSiteConfig($site_name, $site_url, $admin_email);

        // حفظ بيانات المدير في الجلسة للعرض في الخطوة الأخيرة
        $_SESSION['admin_username'] = $admin_username;
        $_SESSION['admin_email'] = $admin_email;
        $_SESSION['admin_created'] = true;

        return ['success' => true];

    } catch (PDOException $e) {
        return ['success' => false, 'errors' => ['خطأ في إنشاء حساب المدير: ' . $e->getMessage()]];
    }
}

// تحديث ملف database.php
function updateDatabaseConfig($host, $username, $password, $dbname) {
    $config_content = "<?php
class Database {
    private \$host = '$host';
    private \$db_name = '$dbname';
    private \$username = '$username';
    private \$password = '$password';
    private \$conn;

    public function getConnection() {
        \$this->conn = null;
        try {
            \$this->conn = new PDO(\"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\", \$this->username, \$this->password);
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException \$exception) {
            echo \"Connection error: \" . \$exception->getMessage();
        }
        return \$this->conn;
    }
}
?>";
    
    file_put_contents('config/database.php', $config_content);
}

// تحديث ملف config.php
function updateSiteConfig($site_name, $site_url, $admin_email) {
    $config_file = 'config/config.php';
    if (file_exists($config_file)) {
        $config_content = file_get_contents($config_file);
        
        $config_content = preg_replace("/define\('SITE_NAME', '.*?'\);/", "define('SITE_NAME', '$site_name');", $config_content);
        $config_content = preg_replace("/define\('SITE_URL', '.*?'\);/", "define('SITE_URL', '$site_url');", $config_content);
        $config_content = preg_replace("/define\('ADMIN_EMAIL', '.*?'\);/", "define('ADMIN_EMAIL', '$admin_email');", $config_content);
        
        file_put_contents($config_file, $config_content);
    }
}

// إنهاء التثبيت
function finishInstallation() {
    // إنشاء ملف القفل
    file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
    
    // إنشاء ملف .htaccess للحماية
    $htaccess_content = "
# حماية ملفات التكوين
<Files \"*.php\">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>

# حماية ملفات قاعدة البيانات
<Files \"*.sql\">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>

# حماية ملفات السجلات
<Files \"*.log\">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>
";
    
    file_put_contents('config/.htaccess', $htaccess_content);
    file_put_contents('logs/.htaccess', $htaccess_content);
    file_put_contents('database/.htaccess', $htaccess_content);
    
    header('Location: installer.php?step=5');
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام CPA Marketing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-card { background: white; border-radius: 15px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 2rem; }
        .step { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 10px; }
        .step.active { background: #007bff; color: white; }
        .step.completed { background: #28a745; color: white; }
        .step.pending { background: #e9ecef; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="install-card p-4">
                    <div class="text-center mb-4">
                        <h1><i class="fas fa-cog me-2"></i>تثبيت نظام CPA Marketing</h1>
                        <p class="text-muted">الإصدار 2.0 - نظام تسويق CPA متكامل</p>
                    </div>

                    <!-- مؤشر الخطوات -->
                    <div class="step-indicator">
                        <div class="step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : 'pending'; ?>">1</div>
                        <div class="step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : 'pending'; ?>">2</div>
                        <div class="step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : 'pending'; ?>">3</div>
                        <div class="step <?php echo $step >= 4 ? ($step == 4 ? 'active' : 'completed') : 'pending'; ?>">4</div>
                        <div class="step <?php echo $step >= 5 ? 'completed' : 'pending'; ?>">5</div>
                    </div>

                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>أخطاء في التثبيت:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>نجح:</h6>
                            <ul class="mb-0">
                                <?php foreach ($success as $msg): ?>
                                    <li><?php echo htmlspecialchars($msg); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php
                    switch ($step) {
                        case 1:
                            include 'installer-steps/step1-requirements.php';
                            break;
                        case 2:
                            include 'installer-steps/step2-database.php';
                            break;
                        case 3:
                            include 'installer-steps/step3-admin.php';
                            break;
                        case 4:
                            include 'installer-steps/step4-finish.php';
                            break;
                        case 5:
                            include 'installer-steps/step5-complete.php';
                            break;
                        default:
                            echo '<div class="alert alert-danger">خطوة غير صحيحة</div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
