<?php
require_once '../config/config.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

$error_message = '';
if (isset($_SESSION['error'])) {
    $error_message = $_SESSION['error'];
    unset($_SESSION['error']);
}

$success_message = '';
if (isset($_SESSION['success'])) {
    $success_message = $_SESSION['success'];
    unset($_SESSION['success']);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        .register-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
        }
        .input-group .form-control {
            border-right: none;
        }
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        .strength-weak { background-color: #dc3545; }
        .strength-medium { background-color: #ffc107; }
        .strength-strong { background-color: #28a745; }
    </style>
</head>
<body>
    <div class="register-card">
        <div class="register-header">
            <i class="fas fa-user-plus fa-3x mb-3"></i>
            <h3>إنشاء حساب جديد</h3>
            <p class="mb-0">انضم إلى <?php echo SITE_NAME; ?></p>
        </div>
        
        <div class="register-body">
            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form id="registerForm" method="POST" action="register_process.php">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="first_name" class="form-label">الاسم الأول</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="first_name" name="first_name" required 
                                   placeholder="الاسم الأول">
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="last_name" class="form-label">الاسم الأخير</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="last_name" name="last_name" required 
                                   placeholder="الاسم الأخير">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-at"></i>
                        </span>
                        <input type="text" class="form-control" id="username" name="username" required 
                               placeholder="اسم المستخدم" pattern="[a-zA-Z0-9_]{3,20}">
                    </div>
                    <small class="text-muted">3-20 حرف، أحرف إنجليزية وأرقام و _ فقط</small>
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input type="email" class="form-control" id="email" name="email" required 
                               placeholder="البريد الإلكتروني">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="phone" class="form-label">رقم الهاتف (اختياري)</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-phone"></i>
                        </span>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               placeholder="رقم الهاتف">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="country" class="form-label">البلد</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-globe"></i>
                        </span>
                        <select class="form-control" id="country" name="country" required>
                            <option value="">اختر البلد</option>
                            <option value="SA">السعودية</option>
                            <option value="AE">الإمارات</option>
                            <option value="EG">مصر</option>
                            <option value="JO">الأردن</option>
                            <option value="LB">لبنان</option>
                            <option value="SY">سوريا</option>
                            <option value="IQ">العراق</option>
                            <option value="KW">الكويت</option>
                            <option value="QA">قطر</option>
                            <option value="BH">البحرين</option>
                            <option value="OM">عمان</option>
                            <option value="YE">اليمن</option>
                            <option value="MA">المغرب</option>
                            <option value="TN">تونس</option>
                            <option value="DZ">الجزائر</option>
                            <option value="LY">ليبيا</option>
                            <option value="SD">السودان</option>
                            <option value="US">الولايات المتحدة</option>
                            <option value="GB">المملكة المتحدة</option>
                            <option value="CA">كندا</option>
                            <option value="AU">أستراليا</option>
                            <option value="DE">ألمانيا</option>
                            <option value="FR">فرنسا</option>
                            <option value="IT">إيطاليا</option>
                            <option value="ES">إسبانيا</option>
                            <option value="NL">هولندا</option>
                            <option value="OTHER">أخرى</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control" id="password" name="password" required 
                               placeholder="كلمة المرور" minlength="8">
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-strength" id="passwordStrength"></div>
                    <small class="text-muted">8 أحرف على الأقل</small>
                </div>

                <div class="mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required 
                               placeholder="تأكيد كلمة المرور">
                    </div>
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">
                        أوافق على <a href="../terms.php" target="_blank">الشروط والأحكام</a> و <a href="../privacy.php" target="_blank">سياسة الخصوصية</a>
                    </label>
                </div>

                <button type="submit" class="btn btn-register btn-primary w-100 mb-3">
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء الحساب
                </button>

                <div class="text-center">
                    <p class="mb-0">لديك حساب بالفعل؟</p>
                    <a href="login.php" class="btn btn-outline-primary w-100 mt-2">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // فحص قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            strengthBar.style.width = (strength * 20) + '%';
            
            if (strength < 2) {
                strengthBar.className = 'password-strength strength-weak';
            } else if (strength < 4) {
                strengthBar.className = 'password-strength strength-medium';
            } else {
                strengthBar.className = 'password-strength strength-strong';
            }
        });

        // التحقق من النموذج
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return false;
            }
            
            if (password.length < 8) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                return false;
            }

            // تعطيل الزر لمنع الإرسال المتكرر
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إنشاء الحساب...';
        });
    </script>
</body>
</html>
