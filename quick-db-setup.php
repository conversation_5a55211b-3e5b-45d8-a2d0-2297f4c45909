<?php
/**
 * إعداد سريع لقاعدة البيانات - للاستضافة المجانية
 * هذا الملف يساعد في إعداد قاعدة البيانات بسرعة
 */

// بيانات قاعدة البيانات الافتراضية لـ InfinityFree
$default_config = [
    'host' => 'sql303.infinityfree.com',
    'username' => 'if0_39395085',
    'password' => 'Qweeee12',
    'database' => 'if0_39395085_q12'
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_config') {
        $host = $_POST['host'] ?? $default_config['host'];
        $username = $_POST['username'] ?? $default_config['username'];
        $password = $_POST['password'] ?? $default_config['password'];
        $database = $_POST['database'] ?? $default_config['database'];
        
        // إنشاء ملف database.php
        $database_config = '<?php
/**
 * إعدادات قاعدة البيانات
 * تم إنشاؤها تلقائياً بواسطة الإعداد السريع
 */

class Database {
    private $host = "' . $host . '";
    private $db_name = "' . $database . '";
    private $username = "' . $username . '";
    private $password = "' . $password . '";
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        return $this->conn;
    }
}
?>';

        // إنشاء مجلد config إذا لم يكن موجوداً
        if (!is_dir('config')) {
            mkdir('config', 0755, true);
        }
        
        // كتابة ملف database.php
        if (file_put_contents('config/database.php', $database_config)) {
            $success_message = "تم إنشاء ملف database.php بنجاح!";
            
            // اختبار الاتصال
            try {
                require_once 'config/database.php';
                $db = new Database();
                $conn = $db->getConnection();
                if ($conn) {
                    $test_message = "تم اختبار الاتصال بنجاح!";
                    $test_status = "success";
                } else {
                    $test_message = "فشل في اختبار الاتصال";
                    $test_status = "error";
                }
            } catch (Exception $e) {
                $test_message = "خطأ في الاختبار: " . $e->getMessage();
                $test_status = "error";
            }
        } else {
            $error_message = "فشل في إنشاء ملف database.php";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعداد السريع لقاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-rocket me-2"></i>الإعداد السريع لقاعدة البيانات</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($success_message)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check me-2"></i><?php echo $success_message; ?>
                            </div>
                            
                            <?php if (isset($test_message)): ?>
                                <div class="alert alert-<?php echo $test_status === 'success' ? 'success' : 'danger'; ?>">
                                    <i class="fas fa-<?php echo $test_status === 'success' ? 'check' : 'times'; ?> me-2"></i>
                                    <?php echo $test_message; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($test_status === 'success'): ?>
                                <div class="text-center">
                                    <a href="installer.php?step=2" class="btn btn-success btn-lg">
                                        <i class="fas fa-arrow-right me-2"></i>متابعة التثبيت
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                        <?php elseif (isset($error_message)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-times me-2"></i><?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!isset($success_message) || $test_status !== 'success'): ?>
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>هذا الإعداد السريع مخصص لـ InfinityFree</h6>
                                <p class="mb-0">سيتم إنشاء ملف database.php تلقائياً بالبيانات التي تدخلها</p>
                            </div>

                            <form method="POST">
                                <input type="hidden" name="action" value="create_config">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="host" class="form-label">عنوان الخادم</label>
                                            <input type="text" class="form-control" id="host" name="host" 
                                                   value="<?php echo $default_config['host']; ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="database" class="form-label">اسم قاعدة البيانات</label>
                                            <input type="text" class="form-control" id="database" name="database" 
                                                   value="<?php echo $default_config['database']; ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">اسم المستخدم</label>
                                            <input type="text" class="form-control" id="username" name="username" 
                                                   value="<?php echo $default_config['username']; ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">كلمة المرور</label>
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   value="<?php echo $default_config['password']; ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تأكد من:</h6>
                                    <ul class="mb-0">
                                        <li>إنشاء قاعدة البيانات من cPanel أولاً</li>
                                        <li>ربط المستخدم بقاعدة البيانات مع صلاحيات كاملة</li>
                                        <li>استخدام الأسماء الكاملة مع البادئة</li>
                                    </ul>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-cog me-2"></i>إنشاء ملف الإعدادات
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="test-database.php" class="btn btn-outline-info me-2">
                                <i class="fas fa-flask me-1"></i>اختبار قاعدة البيانات
                            </a>
                            <a href="installer.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للتثبيت
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
