<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $postback_password = sanitize($_POST['postback_password'] ?? '');
    $allowed_ips = sanitize($_POST['allowed_ips'] ?? '');
    $enable_ip_whitelist = isset($_POST['enable_ip_whitelist']);
    $enable_password_protection = isset($_POST['enable_password_protection']);
    
    try {
        // حفظ الإعدادات في قاعدة البيانات
        $settings = [
            'postback_password' => $postback_password,
            'postback_allowed_ips' => $allowed_ips,
            'postback_enable_ip_whitelist' => $enable_ip_whitelist ? '1' : '0',
            'postback_enable_password' => $enable_password_protection ? '1' : '0'
        ];
        
        foreach ($settings as $key => $value) {
            $query = "INSERT INTO settings (setting_key, setting_value, type) 
                     VALUES (:key, :value, 'string') 
                     ON DUPLICATE KEY UPDATE setting_value = :value";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->bindParam(':value', $value);
            $stmt->execute();
        }
        
        $success_message = 'تم حفظ إعدادات Postback بنجاح';
        
    } catch (PDOException $e) {
        logError("خطأ في حفظ إعدادات Postback: " . $e->getMessage());
        $error_message = 'حدث خطأ في حفظ الإعدادات';
    }
}

// جلب الإعدادات الحالية
$settings_query = "SELECT setting_key, setting_value FROM settings 
                   WHERE setting_key LIKE 'postback_%'";
$settings_stmt = $db->prepare($settings_query);
$settings_stmt->execute();
$settings_data = $settings_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$postback_password = $settings_data['postback_password'] ?? '';
$allowed_ips = $settings_data['postback_allowed_ips'] ?? "************\n127.0.0.1";
$enable_ip_whitelist = ($settings_data['postback_enable_ip_whitelist'] ?? '1') === '1';
$enable_password_protection = ($settings_data['postback_enable_password'] ?? '1') === '1';

// جلب إحصائيات Postback
$stats_query = "SELECT 
    COUNT(*) as total_conversions,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_conversions,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_conversions,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_conversions,
    SUM(payout) as total_payout
    FROM conversions 
    WHERE converted_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// جلب أحدث التحويلات
$recent_conversions_query = "SELECT c.*, o.title as offer_title, u.username
                            FROM conversions c
                            JOIN offers o ON c.offer_id = o.id
                            JOIN users u ON c.user_id = u.id
                            ORDER BY c.converted_at DESC
                            LIMIT 10";

$recent_stmt = $db->prepare($recent_conversions_query);
$recent_stmt->execute();
$recent_conversions = $recent_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات Postback - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إعدادات Postback</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="../../docs/postback-documentation.md" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-book me-1"></i>الوثائق
                        </a>
                    </div>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات Postback -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي التحويلات (30 يوم)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_conversions']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            التحويلات المعتمدة
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['approved_conversions']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            في الانتظار
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['pending_conversions']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            إجمالي العمولات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo CURRENCY_SYMBOL . number_format($stats['total_payout'], 2); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- إعدادات Postback -->
                    <div class="col-lg-8 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إعدادات الأمان</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable_password_protection" 
                                                   name="enable_password_protection" <?php echo $enable_password_protection ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_password_protection">
                                                تفعيل حماية كلمة المرور
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="postback_password" class="form-label">كلمة مرور Postback</label>
                                        <input type="text" class="form-control" id="postback_password" name="postback_password" 
                                               value="<?php echo htmlspecialchars($postback_password); ?>" 
                                               placeholder="أدخل كلمة مرور قوية">
                                        <small class="text-muted">ستُضاف كمعامل &password= في Postback URL</small>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable_ip_whitelist" 
                                                   name="enable_ip_whitelist" <?php echo $enable_ip_whitelist ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_ip_whitelist">
                                                تفعيل قائمة IPs المسموحة
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="allowed_ips" class="form-label">IPs المسموحة</label>
                                        <textarea class="form-control" id="allowed_ips" name="allowed_ips" rows="5" 
                                                  placeholder="أدخل IP واحد في كل سطر"><?php echo htmlspecialchars($allowed_ips); ?></textarea>
                                        <small class="text-muted">
                                            CPALead IP: ************<br>
                                            أدخل IP واحد في كل سطر
                                        </small>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات Postback URL -->
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Postback URL</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">URL الأساسي</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" 
                                               value="<?php echo SITE_URL; ?>/tracking/postback.php" readonly>
                                        <button class="btn btn-outline-secondary" type="button" 
                                                onclick="copyToClipboard('<?php echo SITE_URL; ?>/tracking/postback.php')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">مثال CPALead</label>
                                    <textarea class="form-control" rows="4" readonly><?php echo SITE_URL; ?>/tracking/postback.php?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&payout={payout}&lead_id={lead_id}&country_iso={country_iso}&password=<?php echo htmlspecialchars($postback_password); ?>&virtual_currency={virtual_currency}</textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">مثال بسيط</label>
                                    <textarea class="form-control" rows="2" readonly><?php echo SITE_URL; ?>/tracking/postback.php?click_id={click_id}&payout={payout}&status=approved</textarea>
                                </div>

                                <a href="test.php" class="btn btn-outline-info btn-sm w-100">
                                    <i class="fas fa-vial me-1"></i>اختبار Postback
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أحدث التحويلات -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أحدث التحويلات</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>العرض</th>
                                        <th>المستخدم</th>
                                        <th>العمولة</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>Lead ID</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_conversions as $conversion): ?>
                                    <tr>
                                        <td><?php echo $conversion['id']; ?></td>
                                        <td><?php echo htmlspecialchars($conversion['offer_title']); ?></td>
                                        <td><?php echo htmlspecialchars($conversion['username']); ?></td>
                                        <td><?php echo CURRENCY_SYMBOL . number_format($conversion['payout'], 2); ?></td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'rejected' => 'danger',
                                                'reversed' => 'secondary'
                                            ];
                                            $status_text = [
                                                'pending' => 'في الانتظار',
                                                'approved' => 'معتمد',
                                                'rejected' => 'مرفوض',
                                                'reversed' => 'مُلغى'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_classes[$conversion['status']]; ?>">
                                                <?php echo $status_text[$conversion['status']]; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($conversion['converted_at'])); ?></td>
                                        <td>
                                            <small><?php echo htmlspecialchars($conversion['lead_id'] ?: '-'); ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
</body>
</html>
