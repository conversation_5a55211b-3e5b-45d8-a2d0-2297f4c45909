<?php
/**
 * مهمة تنظيف سجلات حماية IP المنتهية الصلاحية
 * يجب تشغيلها كل ساعة عبر Cron Job
 * 
 * إضافة إلى Cron:
 * 0 * * * * /usr/bin/php /path/to/your/site/cron/cleanup-ip-protection.php
 */

// منع الوصول المباشر من المتصفح
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    die('Access denied. This script can only be run from command line.');
}

require_once __DIR__ . '/../config/config.php';

try {
    $database = new Database();
    $ipProtection = new IPProtection($database);
    
    echo "[" . date('Y-m-d H:i:s') . "] بدء تنظيف سجلات حماية IP...\n";
    
    // تنظيف السجلات المنتهية الصلاحية
    $cleaned_count = $ipProtection->cleanExpiredProtections();
    
    echo "[" . date('Y-m-d H:i:s') . "] تم حذف {$cleaned_count} سجل منتهي الصلاحية\n";
    
    // الحصول على إحصائيات بعد التنظيف
    $stats = $ipProtection->getProtectionStats();
    $active_ips = $stats['active_ips'] ?? 0;
    $total_protections = $stats['total_protections'] ?? 0;
    
    echo "[" . date('Y-m-d H:i:s') . "] الإحصائيات الحالية: {$active_ips} IP محمي، {$total_protections} سجل إجمالي\n";
    
    // تسجيل النشاط في قاعدة البيانات
    if ($cleaned_count > 0) {
        $db = $database->getConnection();
        $log_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                      VALUES (NULL, 'ip_protection_cleanup', 'تنظيف تلقائي لسجلات حماية IP', :data)";
        
        $log_data = json_encode([
            'cleaned_records' => $cleaned_count,
            'remaining_ips' => $active_ips,
            'remaining_protections' => $total_protections,
            'cleanup_time' => date('Y-m-d H:i:s')
        ]);
        
        $log_stmt = $db->prepare($log_query);
        $log_stmt->bindParam(':data', $log_data);
        $log_stmt->execute();
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] تم الانتهاء من التنظيف بنجاح\n";
    
} catch (Exception $e) {
    $error_message = "خطأ في تنظيف حماية IP: " . $e->getMessage();
    echo "[" . date('Y-m-d H:i:s') . "] {$error_message}\n";
    
    // تسجيل الخطأ
    logError($error_message);
    
    exit(1);
}

exit(0);
?>
