<?php
/**
 * كاشف نوع الاستضافة ومساعد إعداد قاعدة البيانات
 */

function detectHostingType() {
    $hosting_info = [
        'type' => 'unknown',
        'name' => 'غير معروف',
        'db_prefix_required' => false,
        'db_creation_allowed' => true,
        'instructions' => []
    ];
    
    // فحص المتغيرات البيئية والخادم
    $server_name = $_SERVER['SERVER_NAME'] ?? '';
    $document_root = $_SERVER['DOCUMENT_ROOT'] ?? '';
    $server_software = $_SERVER['SERVER_SOFTWARE'] ?? '';
    
    // InfinityFree
    if (strpos($server_name, 'infinityfree') !== false || 
        strpos($document_root, 'infinityfree') !== false ||
        strpos($server_name, 'epizy.com') !== false ||
        strpos($server_name, 'rf.gd') !== false) {
        
        $hosting_info = [
            'type' => 'infinityfree',
            'name' => 'InfinityFree',
            'db_prefix_required' => true,
            'db_creation_allowed' => false,
            'instructions' => [
                'اذهب إلى cPanel → MySQL Databases',
                'أنشئ قاعدة بيانات جديدة',
                'أنشئ مستخدم قاعدة بيانات',
                'اربط المستخدم بقاعدة البيانات',
                'استخدم الأسماء الكاملة مع البادئة'
            ]
        ];
    }
    
    // 000webhost
    elseif (strpos($server_name, '000webhost') !== false ||
            strpos($document_root, '000webhost') !== false) {
        
        $hosting_info = [
            'type' => '000webhost',
            'name' => '000webhost',
            'db_prefix_required' => true,
            'db_creation_allowed' => false,
            'instructions' => [
                'اذهب إلى Control Panel → Database',
                'أنشئ قاعدة بيانات MySQL جديدة',
                'احفظ بيانات الاتصال',
                'استخدم الأسماء الكاملة مع البادئة'
            ]
        ];
    }
    
    // Hostinger
    elseif (strpos($server_name, 'hostinger') !== false) {
        
        $hosting_info = [
            'type' => 'hostinger',
            'name' => 'Hostinger',
            'db_prefix_required' => true,
            'db_creation_allowed' => false,
            'instructions' => [
                'اذهب إلى hPanel → Databases → MySQL',
                'أنشئ قاعدة بيانات جديدة',
                'أنشئ مستخدم قاعدة بيانات',
                'استخدم الأسماء الكاملة'
            ]
        ];
    }
    
    // Localhost (XAMPP, WAMP, etc)
    elseif ($server_name === 'localhost' || $server_name === '127.0.0.1') {
        
        $hosting_info = [
            'type' => 'localhost',
            'name' => 'خادم محلي (XAMPP/WAMP)',
            'db_prefix_required' => false,
            'db_creation_allowed' => true,
            'instructions' => [
                'يمكن إنشاء قاعدة البيانات تلقائياً',
                'أو أنشئها من phpMyAdmin',
                'استخدم root كاسم مستخدم عادة',
                'كلمة المرور فارغة عادة'
            ]
        ];
    }
    
    return $hosting_info;
}

function generateDatabaseSuggestions($username = '') {
    $hosting = detectHostingType();
    $suggestions = [];
    
    if ($hosting['db_prefix_required'] && !empty($username)) {
        $suggestions = [
            'db_name' => $username . '_cpa_system',
            'db_username' => $username . '_cpauser',
            'db_host' => 'localhost'
        ];
    } else {
        $suggestions = [
            'db_name' => 'cpa_system',
            'db_username' => 'root',
            'db_host' => 'localhost'
        ];
    }
    
    return $suggestions;
}

// إذا تم استدعاء الملف مباشرة، اعرض معلومات الاستضافة
if (basename($_SERVER['PHP_SELF']) === 'hosting-detector.php') {
    $hosting = detectHostingType();
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>كاشف نوع الاستضافة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-server me-2"></i>معلومات الاستضافة</h4>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6>نوع الاستضافة:</h6>
                                    <p class="text-primary"><strong><?php echo $hosting['name']; ?></strong></p>
                                </div>
                                <div class="col-md-6">
                                    <h6>إنشاء قاعدة البيانات:</h6>
                                    <p class="<?php echo $hosting['db_creation_allowed'] ? 'text-success' : 'text-warning'; ?>">
                                        <?php echo $hosting['db_creation_allowed'] ? 'تلقائي' : 'يدوي مطلوب'; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <?php if (!empty($hosting['instructions'])): ?>
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>إرشادات الإعداد:</h6>
                                    <ol class="mb-0">
                                        <?php foreach ($hosting['instructions'] as $instruction): ?>
                                            <li><?php echo $instruction; ?></li>
                                        <?php endforeach; ?>
                                    </ol>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>معلومات الخادم:</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>اسم الخادم:</strong> <?php echo $_SERVER['SERVER_NAME'] ?? 'غير محدد'; ?></li>
                                        <li><strong>برنامج الخادم:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></li>
                                        <li><strong>مجلد الجذر:</strong> <code><?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد'; ?></code></li>
                                        <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <a href="installer.php" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-1"></i>العودة للتثبيت
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
