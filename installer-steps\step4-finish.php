<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-cogs me-2"></i>الخطوة 4: إنهاء التثبيت</h4>
    </div>
    <div class="card-body">
        <div class="text-center mb-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التثبيت...</span>
            </div>
            <h5 class="mt-3">جاري إنهاء التثبيت...</h5>
            <p class="text-muted">يرجى الانتظار بينما نقوم بإعداد النظام</p>
        </div>
        
        <div class="progress mb-4">
            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                 role="progressbar" style="width: 0%" id="progress-bar"></div>
        </div>
        
        <div class="installation-steps">
            <div class="step-item" id="step-config">
                <i class="fas fa-cog text-muted"></i>
                <span>إنشاء ملفات التكوين...</span>
                <div class="step-status"></div>
            </div>
            
            <div class="step-item" id="step-security">
                <i class="fas fa-shield-alt text-muted"></i>
                <span>إعداد الحماية والأمان...</span>
                <div class="step-status"></div>
            </div>
            
            <div class="step-item" id="step-directories">
                <i class="fas fa-folder text-muted"></i>
                <span>إنشاء المجلدات المطلوبة...</span>
                <div class="step-status"></div>
            </div>
            
            <div class="step-item" id="step-permissions">
                <i class="fas fa-key text-muted"></i>
                <span>تعيين الصلاحيات...</span>
                <div class="step-status"></div>
            </div>
            
            <div class="step-item" id="step-cleanup">
                <i class="fas fa-broom text-muted"></i>
                <span>تنظيف ملفات التثبيت...</span>
                <div class="step-status"></div>
            </div>
        </div>
        
        <form method="POST" id="finish-form">
            <div class="text-center">
                <button type="submit" class="btn btn-success btn-lg" disabled id="finish-btn">
                    <i class="fas fa-check me-2"></i>إنهاء التثبيت
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.installation-steps {
    max-width: 500px;
    margin: 0 auto;
}

.step-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.step-item:last-child {
    border-bottom: none;
}

.step-item i {
    width: 30px;
    margin-left: 1rem;
    font-size: 1.2rem;
}

.step-item span {
    flex: 1;
    margin: 0 1rem;
}

.step-status {
    width: 20px;
    height: 20px;
}

.step-completed i {
    color: #28a745 !important;
}

.step-processing i {
    color: #007bff !important;
}

.step-error i {
    color: #dc3545 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // بدء عملية التثبيت تلقائياً
    startInstallation();
});

async function startInstallation() {
    const steps = [
        { id: 'step-config', name: 'إنشاء ملفات التكوين', progress: 20 },
        { id: 'step-security', name: 'إعداد الحماية والأمان', progress: 40 },
        { id: 'step-directories', name: 'إنشاء المجلدات المطلوبة', progress: 60 },
        { id: 'step-permissions', name: 'تعيين الصلاحيات', progress: 80 },
        { id: 'step-cleanup', name: 'تنظيف ملفات التثبيت', progress: 100 }
    ];
    
    for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        await processStep(step);
        updateProgress(step.progress);
        await sleep(1000); // انتظار ثانية واحدة بين كل خطوة
    }
    
    // تفعيل زر الإنهاء
    document.getElementById('finish-btn').disabled = false;
    document.getElementById('finish-btn').innerHTML = '<i class="fas fa-rocket me-2"></i>إطلاق النظام!';
}

async function processStep(step) {
    const stepElement = document.getElementById(step.id);
    const statusElement = stepElement.querySelector('.step-status');
    
    // بدء المعالجة
    stepElement.classList.add('step-processing');
    statusElement.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"></div>';
    
    try {
        // محاكاة معالجة الخطوة
        await sleep(2000);
        
        // نجح
        stepElement.classList.remove('step-processing');
        stepElement.classList.add('step-completed');
        statusElement.innerHTML = '<i class="fas fa-check text-success"></i>';
        
    } catch (error) {
        // فشل
        stepElement.classList.remove('step-processing');
        stepElement.classList.add('step-error');
        statusElement.innerHTML = '<i class="fas fa-times text-danger"></i>';
    }
}

function updateProgress(percentage) {
    const progressBar = document.getElementById('progress-bar');
    progressBar.style.width = percentage + '%';
    progressBar.setAttribute('aria-valuenow', percentage);
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// معالجة إرسال النموذج
document.getElementById('finish-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // تغيير النص إلى "جاري الإطلاق..."
    const finishBtn = document.getElementById('finish-btn');
    finishBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>جاري الإطلاق...';
    finishBtn.disabled = true;
    
    // إرسال النموذج بعد ثانيتين
    setTimeout(() => {
        this.submit();
    }, 2000);
});
</script>
