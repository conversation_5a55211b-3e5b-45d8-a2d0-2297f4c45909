<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo '<div class="alert alert-warning">يرجى تسجيل الدخول أولاً</div>';
    exit();
}

$user_id = $_SESSION['user_id'];
$subid = $user_id . '_widget_' . time();

// معاملات Widget
$width = sanitize($_GET['width'] ?? '100%');
$height = sanitize($_GET['height'] ?? '400px');
$theme = sanitize($_GET['theme'] ?? 'light');

// بناء رابط Offerwall
$offerwall_url = "https://fastrsrvr.com/list/191?subid=" . urlencode($subid);
?>

<div class="offerwall-widget" style="width: <?php echo htmlspecialchars($width); ?>; height: <?php echo htmlspecialchars($height); ?>;">
    <div class="widget-header" style="background: <?php echo $theme === 'dark' ? '#343a40' : '#f8f9fa'; ?>; color: <?php echo $theme === 'dark' ? 'white' : 'black'; ?>; padding: 10px; border-bottom: 1px solid #dee2e6;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <strong><i class="fas fa-gift"></i> العروض المتاحة</strong>
                <small style="display: block; opacity: 0.7;">اربح المزيد من المال</small>
            </div>
            <button onclick="refreshWidget()" style="background: none; border: 1px solid; border-radius: 4px; padding: 5px 10px; cursor: pointer;">
                <i class="fas fa-sync"></i>
            </button>
        </div>
    </div>
    
    <div style="position: relative; height: calc(100% - 60px);">
        <div id="widgetLoading" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,255,255,0.9); display: flex; align-items: center; justify-content: center; z-index: 10;">
            <div style="text-align: center;">
                <div style="border: 3px solid #f3f3f3; border-top: 3px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>
                <small>جاري التحميل...</small>
            </div>
        </div>
        
        <iframe 
            id="widgetFrame"
            sandbox="allow-popups allow-same-origin allow-scripts allow-top-navigation-by-user-activation allow-popups-to-escape-sandbox" 
            src="<?php echo htmlspecialchars($offerwall_url); ?>" 
            style="width: 100%; height: 100%; border: none;"
            onload="hideWidgetLoading()">
        </iframe>
    </div>
</div>

<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.offerwall-widget {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.offerwall-widget:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transition: box-shadow 0.3s ease;
}
</style>

<script>
function hideWidgetLoading() {
    document.getElementById('widgetLoading').style.display = 'none';
}

function refreshWidget() {
    document.getElementById('widgetLoading').style.display = 'flex';
    document.getElementById('widgetFrame').src = document.getElementById('widgetFrame').src;
}

// تحديث تلقائي كل 5 دقائق
setInterval(refreshWidget, 300000);
</script>
