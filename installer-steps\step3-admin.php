<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-user-shield me-2"></i>الخطوة 3: إنشاء حساب المدير</h4>
    </div>
    <div class="card-body">
        <p class="text-muted">إنشاء حساب المدير الرئيسي للنظام</p>
        
        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="admin_username" class="form-label">
                            <i class="fas fa-user me-1"></i>اسم المستخدم
                        </label>
                        <input type="text" class="form-control" id="admin_username" name="admin_username" 
                               value="<?php echo $_POST['admin_username'] ?? 'admin'; ?>" required>
                        <div class="form-text">اسم المستخدم لتسجيل الدخول</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="admin_email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>البريد الإلكتروني
                        </label>
                        <input type="email" class="form-control" id="admin_email" name="admin_email" 
                               value="<?php echo $_POST['admin_email'] ?? '<EMAIL>'; ?>" required>
                        <div class="form-text">البريد الإلكتروني للمدير</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="admin_first_name" class="form-label">
                            <i class="fas fa-id-card me-1"></i>الاسم الأول
                        </label>
                        <input type="text" class="form-control" id="admin_first_name" name="admin_first_name" 
                               value="<?php echo $_POST['admin_first_name'] ?? 'مدير'; ?>" required>
                        <div class="form-text">الاسم الأول للمدير</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="admin_last_name" class="form-label">
                            <i class="fas fa-id-card me-1"></i>الاسم الأخير
                        </label>
                        <input type="text" class="form-control" id="admin_last_name" name="admin_last_name" 
                               value="<?php echo $_POST['admin_last_name'] ?? 'النظام'; ?>" required>
                        <div class="form-text">الاسم الأخير للمدير</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="admin_password" class="form-label">
                            <i class="fas fa-lock me-1"></i>كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="admin_password" name="admin_password" 
                               value="<?php echo $_POST['admin_password'] ?? ''; ?>" required minlength="6">
                        <div class="form-text">كلمة مرور قوية (6 أحرف على الأقل)</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-lock me-1"></i>تأكيد كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               required minlength="6">
                        <div class="form-text">إعادة كتابة كلمة المرور</div>
                    </div>
                </div>
            </div>
            
            <hr>
            
            <h5><i class="fas fa-cog me-2"></i>إعدادات الموقع</h5>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">
                            <i class="fas fa-globe me-1"></i>اسم الموقع
                        </label>
                        <input type="text" class="form-control" id="site_name" name="site_name" 
                               value="<?php echo $_POST['site_name'] ?? 'CPA Marketing System'; ?>" required>
                        <div class="form-text">اسم الموقع الذي سيظهر في العنوان</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_url" class="form-label">
                            <i class="fas fa-link me-1"></i>رابط الموقع
                        </label>
                        <input type="url" class="form-control" id="site_url" name="site_url" 
                               value="<?php echo $_POST['site_url'] ?? 'http://' . $_SERVER['HTTP_HOST']; ?>" required>
                        <div class="form-text">الرابط الكامل للموقع</div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير أمني:</h6>
                <ul class="mb-0">
                    <li>استخدم كلمة مرور قوية ومعقدة</li>
                    <li>لا تستخدم "admin" كاسم مستخدم في الإنتاج</li>
                    <li>احفظ بيانات المدير في مكان آمن</li>
                    <li>يمكنك تغيير هذه البيانات لاحقاً من لوحة الإدارة</li>
                </ul>
            </div>
            
            <div class="text-end">
                <a href="installer.php?step=2" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i>السابق
                </a>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-user-plus me-1"></i>إنشاء الحساب والمتابعة
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('admin_password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    }
});

// تحديث رابط الموقع تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const siteUrlInput = document.getElementById('site_url');
    if (siteUrlInput.value === 'http://' + window.location.host) {
        siteUrlInput.value = window.location.protocol + '//' + window.location.host;
    }
});

// التحقق من قوة كلمة المرور
document.getElementById('admin_password').addEventListener('input', function() {
    const password = this.value;
    const strength = checkPasswordStrength(password);
    
    // إزالة الرسائل السابقة
    const existingFeedback = this.parentNode.querySelector('.password-strength');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    if (password.length > 0) {
        const strengthDiv = document.createElement('div');
        strengthDiv.className = 'password-strength mt-1';
        
        let strengthText = '';
        let strengthClass = '';
        
        if (strength < 2) {
            strengthText = 'ضعيفة';
            strengthClass = 'text-danger';
        } else if (strength < 3) {
            strengthText = 'متوسطة';
            strengthClass = 'text-warning';
        } else {
            strengthText = 'قوية';
            strengthClass = 'text-success';
        }
        
        strengthDiv.innerHTML = `<small class="${strengthClass}">قوة كلمة المرور: ${strengthText}</small>`;
        this.parentNode.appendChild(strengthDiv);
    }
});

function checkPasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    return strength;
}
</script>
