<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// جلب إحصائيات شاملة
$stats_query = "SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_auto_generated = 1 THEN 1 END) as auto_generated_users,
    COUNT(CASE WHEN is_auto_generated = 0 THEN 1 END) as manual_users,
    COUNT(CASE WHEN login_count > 0 THEN 1 END) as active_users,
    COUNT(CASE WHEN login_count = 0 THEN 1 END) as inactive_users,
    COUNT(CASE WHEN is_auto_generated = 1 AND login_count > 0 THEN 1 END) as active_auto_users,
    COUNT(CASE WHEN is_auto_generated = 0 AND login_count > 0 THEN 1 END) as active_manual_users,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as users_today,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as users_week,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as users_month,
    AVG(login_count) as avg_login_count
FROM users";

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// إحصائيات يومية للأسبوع الماضي
$daily_stats_query = "SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_registrations,
    COUNT(CASE WHEN is_auto_generated = 1 THEN 1 END) as auto_registrations,
    COUNT(CASE WHEN is_auto_generated = 0 THEN 1 END) as manual_registrations
FROM users 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at)
ORDER BY date DESC";

$daily_stats_stmt = $db->prepare($daily_stats_query);
$daily_stats_stmt->execute();
$daily_stats = $daily_stats_stmt->fetchAll(PDO::FETCH_ASSOC);

// أكثر أسماء المستخدمين نشاطاً
$top_users_query = "SELECT username, login_count, last_login, is_auto_generated, created_at
FROM users 
ORDER BY login_count DESC 
LIMIT 10";

$top_users_stmt = $db->prepare($top_users_query);
$top_users_stmt->execute();
$top_users = $top_users_stmt->fetchAll(PDO::FETCH_ASSOC);

// أحدث التسجيلات
$recent_users_query = "SELECT username, is_auto_generated, login_count, created_at, last_login
FROM users 
ORDER BY created_at DESC 
LIMIT 15";

$recent_users_stmt = $db->prepare($recent_users_query);
$recent_users_stmt->execute();
$recent_users = $recent_users_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات أسماء المستخدمين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إحصائيات أسماء المستخدمين</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي المستخدمين
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_users']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            مولد تلقائياً
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['auto_generated_users']); ?>
                                            <small class="text-muted">
                                                (<?php echo round(($stats['auto_generated_users'] / $stats['total_users']) * 100, 1); ?>%)
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-magic fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            مستخدمين نشطين
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['active_users']); ?>
                                            <small class="text-muted">
                                                (<?php echo round(($stats['active_users'] / $stats['total_users']) * 100, 1); ?>%)
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            متوسط تسجيلات الدخول
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['avg_login_count'], 1); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- الرسم البياني -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">التسجيلات اليومية (آخر 7 أيام)</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="dailyRegistrationsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات مفصلة -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">تفاصيل إضافية</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>تسجيلات اليوم:</span>
                                        <strong><?php echo number_format($stats['users_today']); ?></strong>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>تسجيلات الأسبوع:</span>
                                        <strong><?php echo number_format($stats['users_week']); ?></strong>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>تسجيلات الشهر:</span>
                                        <strong><?php echo number_format($stats['users_month']); ?></strong>
                                    </div>
                                </div>
                                <hr>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>نشطين (تلقائي):</span>
                                        <strong class="text-success"><?php echo number_format($stats['active_auto_users']); ?></strong>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>نشطين (يدوي):</span>
                                        <strong class="text-info"><?php echo number_format($stats['active_manual_users']); ?></strong>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>غير نشطين:</span>
                                        <strong class="text-warning"><?php echo number_format($stats['inactive_users']); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- أكثر المستخدمين نشاطاً -->
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">أكثر المستخدمين نشاطاً</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>اسم المستخدم</th>
                                                <th>تسجيلات الدخول</th>
                                                <th>النوع</th>
                                                <th>آخر دخول</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_users as $user): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success"><?php echo number_format($user['login_count']); ?></span>
                                                    </td>
                                                    <td>
                                                        <?php if ($user['is_auto_generated']): ?>
                                                            <span class="badge bg-info">تلقائي</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-primary">يدوي</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($user['last_login']): ?>
                                                            <?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">لم يسجل دخول</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أحدث التسجيلات -->
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">أحدث التسجيلات</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>اسم المستخدم</th>
                                                <th>النوع</th>
                                                <th>تاريخ التسجيل</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_users as $user): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php if ($user['is_auto_generated']): ?>
                                                            <span class="badge bg-info">تلقائي</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-primary">يدوي</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                                                    <td>
                                                        <?php if ($user['login_count'] > 0): ?>
                                                            <span class="badge bg-success">نشط</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-warning">لم يستخدم</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        // الرسم البياني للتسجيلات اليومية
        const ctx = document.getElementById('dailyRegistrationsChart').getContext('2d');
        const dailyData = <?php echo json_encode(array_reverse($daily_stats)); ?>;
        
        const labels = dailyData.map(item => item.date);
        const totalData = dailyData.map(item => parseInt(item.total_registrations));
        const autoData = dailyData.map(item => parseInt(item.auto_registrations));
        const manualData = dailyData.map(item => parseInt(item.manual_registrations));
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'إجمالي التسجيلات',
                    data: totalData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: 'تلقائي',
                    data: autoData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.1
                }, {
                    label: 'يدوي',
                    data: manualData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'التسجيلات اليومية'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
