<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$ipQuality = new IPQuality($database);

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = sanitize($_POST['action']);
        
        switch ($action) {
            case 'update_settings':
                $settings = [
                    'ip_quality_enabled' => isset($_POST['ip_quality_enabled']) ? '1' : '0',
                    'ip_quality_api_key' => sanitize($_POST['ip_quality_api_key']),
                    'ip_quality_service' => sanitize($_POST['ip_quality_service']),
                    'ip_quality_min_score' => intval($_POST['ip_quality_min_score']),
                    'ip_quality_block_vpn' => isset($_POST['ip_quality_block_vpn']) ? '1' : '0',
                    'ip_quality_block_tor' => isset($_POST['ip_quality_block_tor']) ? '1' : '0',
                    'ip_quality_cache_hours' => intval($_POST['ip_quality_cache_hours'])
                ];
                
                if ($ipQuality->updateSettings($settings)) {
                    $_SESSION['success'] = 'تم تحديث إعدادات جودة IP بنجاح!';
                } else {
                    $_SESSION['error'] = 'حدث خطأ في تحديث الإعدادات';
                }
                break;
                
            case 'test_api':
                $test_ip = sanitize($_POST['test_ip']) ?: '*******';
                $result = $ipQuality->checkIPQuality($test_ip);
                $_SESSION['test_result'] = $result;
                $_SESSION['test_ip'] = $test_ip;
                break;
                
            case 'clean_cache':
                $cleaned = $ipQuality->cleanExpiredCache();
                $_SESSION['success'] = "تم حذف {$cleaned} سجل منتهي الصلاحية من الكاش";
                break;
        }
    }
    
    header('Location: ip-quality.php');
    exit();
}

// جلب الإحصائيات
$cache_stats = $ipQuality->getCacheStats();

// جلب أحدث فحوصات IP
$recent_query = "SELECT * FROM ip_quality_cache 
                ORDER BY last_checked DESC 
                LIMIT 20";
$recent_stmt = $db->prepare($recent_query);
$recent_stmt->execute();
$recent_checks = $recent_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات جودة IP - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    <style>
        .quality-score {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 4px;
            color: white;
        }
        .score-excellent { background-color: #28a745; }
        .score-good { background-color: #17a2b8; }
        .score-fair { background-color: #ffc107; color: #000; }
        .score-poor { background-color: #dc3545; }
    </style>
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إعدادات جودة IP</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#testModal">
                                <i class="fas fa-vial me-1"></i>اختبار API
                            </button>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="clean_cache">
                                <button type="submit" class="btn btn-outline-warning btn-sm" 
                                        onclick="return confirm('حذف الكاش المنتهي الصلاحية؟')">
                                    <i class="fas fa-broom me-1"></i>تنظيف الكاش
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- الإحصائيات -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إحصائيات الكاش</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-12 mb-3">
                                        <h4 class="text-primary"><?php echo number_format($cache_stats['total_cached'] ?? 0); ?></h4>
                                        <small class="text-muted">إجمالي IPs مفحوصة</small>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <h4 class="text-success"><?php echo number_format($cache_stats['active_cached'] ?? 0); ?></h4>
                                        <small class="text-muted">كاش نشط</small>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <h4 class="text-info"><?php echo number_format($cache_stats['avg_quality_score'] ?? 0, 1); ?></h4>
                                        <small class="text-muted">متوسط درجة الجودة</small>
                                    </div>
                                </div>
                                
                                <hr>
                                <h6>أنواع التهديدات:</h6>
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-shield-alt text-warning"></i> VPN</span>
                                    <strong><?php echo number_format($cache_stats['vpn_count'] ?? 0); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-user-secret text-danger"></i> Proxy</span>
                                    <strong><?php echo number_format($cache_stats['proxy_count'] ?? 0); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-mask text-dark"></i> Tor</span>
                                    <strong><?php echo number_format($cache_stats['tor_count'] ?? 0); ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإعدادات -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إعدادات فحص جودة IP</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_settings">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="ip_quality_enabled" 
                                                       name="ip_quality_enabled" 
                                                       <?php echo ($ipQuality->settings['ip_quality_enabled'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="ip_quality_enabled">
                                                    <strong>تفعيل فحص جودة IP</strong>
                                                </label>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="ip_quality_service" class="form-label">خدمة الفحص</label>
                                                <select class="form-control" id="ip_quality_service" name="ip_quality_service">
                                                    <option value="ipqualityscore" 
                                                            <?php echo ($ipQuality->settings['ip_quality_service'] ?? 'ipqualityscore') === 'ipqualityscore' ? 'selected' : ''; ?>>
                                                        IPQualityScore
                                                    </option>
                                                    <option value="proxycheck" 
                                                            <?php echo ($ipQuality->settings['ip_quality_service'] ?? '') === 'proxycheck' ? 'selected' : ''; ?>>
                                                        ProxyCheck.io
                                                    </option>
                                                </select>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="ip_quality_api_key" class="form-label">مفتاح API</label>
                                                <input type="text" class="form-control" id="ip_quality_api_key" 
                                                       name="ip_quality_api_key" 
                                                       value="<?php echo htmlspecialchars($ipQuality->settings['ip_quality_api_key'] ?? ''); ?>"
                                                       placeholder="أدخل مفتاح API">
                                                <small class="text-muted">احصل على مفتاح من موقع الخدمة</small>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="ip_quality_min_score" class="form-label">الحد الأدنى لدرجة الجودة</label>
                                                <input type="range" class="form-range" id="ip_quality_min_score" 
                                                       name="ip_quality_min_score" min="0" max="100" 
                                                       value="<?php echo htmlspecialchars($ipQuality->settings['ip_quality_min_score'] ?? '75'); ?>"
                                                       oninput="updateScoreDisplay(this.value)">
                                                <div class="d-flex justify-content-between">
                                                    <small>0</small>
                                                    <span id="scoreDisplay" class="badge bg-primary">75</span>
                                                    <small>100</small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="ip_quality_block_vpn" 
                                                       name="ip_quality_block_vpn"
                                                       <?php echo ($ipQuality->settings['ip_quality_block_vpn'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="ip_quality_block_vpn">
                                                    حظر VPN والبروكسي
                                                </label>
                                            </div>
                                            
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="ip_quality_block_tor" 
                                                       name="ip_quality_block_tor"
                                                       <?php echo ($ipQuality->settings['ip_quality_block_tor'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="ip_quality_block_tor">
                                                    حظر شبكة Tor
                                                </label>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="ip_quality_cache_hours" class="form-label">مدة تخزين الكاش (ساعات)</label>
                                                <input type="number" class="form-control" id="ip_quality_cache_hours" 
                                                       name="ip_quality_cache_hours" min="1" max="168"
                                                       value="<?php echo htmlspecialchars($ipQuality->settings['ip_quality_cache_hours'] ?? '24'); ?>">
                                                <small class="text-muted">مدة الاحتفاظ بنتائج الفحص (1-168 ساعة)</small>
                                            </div>
                                            
                                            <div class="alert alert-info">
                                                <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                                                <ul class="mb-0 small">
                                                    <li>IPQualityScore: دقة عالية، 5000 استعلام مجاني شهرياً</li>
                                                    <li>ProxyCheck.io: سريع، 1000 استعلام مجاني يومياً</li>
                                                    <li>الكاش يقلل استهلاك API ويحسن الأداء</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أحدث فحوصات IP -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أحدث فحوصات IP</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_checks)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-search fa-3x mb-3"></i>
                                <p>لا توجد فحوصات IP حتى الآن</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>IP Address</th>
                                            <th>درجة الجودة</th>
                                            <th>الموقع</th>
                                            <th>مزود الخدمة</th>
                                            <th>التهديدات</th>
                                            <th>آخر فحص</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_checks as $check): ?>
                                            <tr>
                                                <td><code><?php echo htmlspecialchars($check['ip_address']); ?></code></td>
                                                <td>
                                                    <?php 
                                                    $score = intval($check['quality_score']);
                                                    $class = 'score-poor';
                                                    if ($score >= 90) $class = 'score-excellent';
                                                    elseif ($score >= 75) $class = 'score-good';
                                                    elseif ($score >= 50) $class = 'score-fair';
                                                    ?>
                                                    <span class="quality-score <?php echo $class; ?>"><?php echo $score; ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($check['country_name']): ?>
                                                        <?php echo htmlspecialchars($check['city'] . ', ' . $check['country_name']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير معروف</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($check['isp'] ?: 'غير معروف'); ?></td>
                                                <td>
                                                    <?php if ($check['is_vpn']): ?>
                                                        <span class="badge bg-warning">VPN</span>
                                                    <?php endif; ?>
                                                    <?php if ($check['is_proxy']): ?>
                                                        <span class="badge bg-danger">Proxy</span>
                                                    <?php endif; ?>
                                                    <?php if ($check['is_tor']): ?>
                                                        <span class="badge bg-dark">Tor</span>
                                                    <?php endif; ?>
                                                    <?php if (!$check['is_vpn'] && !$check['is_proxy'] && !$check['is_tor']): ?>
                                                        <span class="badge bg-success">نظيف</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($check['last_checked'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal اختبار API -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">اختبار API</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="test_api">
                        <div class="mb-3">
                            <label for="test_ip" class="form-label">IP للاختبار</label>
                            <input type="text" class="form-control" id="test_ip" name="test_ip" 
                                   value="*******" placeholder="*******">
                        </div>
                        
                        <?php if (isset($_SESSION['test_result'])): ?>
                            <div class="alert alert-info">
                                <h6>نتيجة اختبار IP: <?php echo htmlspecialchars($_SESSION['test_ip']); ?></h6>
                                <?php $result = $_SESSION['test_result']; ?>
                                <ul class="mb-0">
                                    <li>درجة الجودة: <strong><?php echo $result['quality_score']; ?>/100</strong></li>
                                    <li>الموقع: <?php echo htmlspecialchars($result['city'] . ', ' . $result['country_name']); ?></li>
                                    <li>مزود الخدمة: <?php echo htmlspecialchars($result['isp']); ?></li>
                                    <li>VPN: <?php echo $result['is_vpn'] ? 'نعم' : 'لا'; ?></li>
                                    <li>Proxy: <?php echo $result['is_proxy'] ? 'نعم' : 'لا'; ?></li>
                                </ul>
                            </div>
                            <?php unset($_SESSION['test_result'], $_SESSION['test_ip']); ?>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="submit" class="btn btn-primary">اختبار</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        function updateScoreDisplay(value) {
            document.getElementById('scoreDisplay').textContent = value;
        }
        
        // تحديث العرض عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('ip_quality_min_score');
            updateScoreDisplay(slider.value);
        });
    </script>
</body>
</html>
