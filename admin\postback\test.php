<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

$test_result = '';
$test_success = false;

// معالجة اختبار Postback
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $test_type = sanitize($_POST['test_type'] ?? 'simple');
    $click_id = sanitize($_POST['click_id'] ?? '');
    $subid = sanitize($_POST['subid'] ?? '');
    $payout = floatval($_POST['payout'] ?? 1.00);
    $password = sanitize($_POST['password'] ?? '');
    
    // بناء URL الاختبار
    $base_url = SITE_URL . '/tracking/postback.php';
    $params = [];
    
    if ($test_type === 'simple') {
        $params = [
            'click_id' => $click_id,
            'payout' => $payout,
            'status' => 'approved'
        ];
        if (!empty($password)) {
            $params['password'] = $password;
        }
    } elseif ($test_type === 'cpalead') {
        $params = [
            'campaign_id' => '1234',
            'campaign_name' => 'Test Campaign',
            'subid' => $subid,
            'subid2' => 'test_source',
            'subid3' => 'test_campaign',
            'payout' => $payout,
            'lead_id' => 'TEST_LEAD_' . time(),
            'country_iso' => 'US',
            'virtual_currency' => '50'
        ];
        if (!empty($password)) {
            $params['password'] = $password;
        }
    }
    
    $test_url = $base_url . '?' . http_build_query($params);
    
    // تنفيذ الاختبار
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'CPA-System-Test/1.0'
            ]
        ]);
        
        $response = @file_get_contents($test_url, false, $context);
        $http_code = 200;
        
        // استخراج رمز الاستجابة HTTP
        if (isset($http_response_header)) {
            foreach ($http_response_header as $header) {
                if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                    $http_code = intval($matches[1]);
                    break;
                }
            }
        }
        
        if ($response !== false && $http_code === 200) {
            $test_success = true;
            $test_result = "✅ نجح الاختبار!\n\nURL المُختبر:\n$test_url\n\nالاستجابة:\n$response\n\nHTTP Code: $http_code";
        } else {
            $test_result = "❌ فشل الاختبار!\n\nURL المُختبر:\n$test_url\n\nالاستجابة:\n" . ($response ?: 'لا توجد استجابة') . "\n\nHTTP Code: $http_code";
        }
        
    } catch (Exception $e) {
        $test_result = "❌ خطأ في الاختبار:\n" . $e->getMessage();
    }
}

// جلب بعض النقرات للاختبار
$clicks_query = "SELECT c.id, c.sub_id, o.title as offer_title, u.username
                 FROM clicks c
                 JOIN tracking_links tl ON c.tracking_id = tl.tracking_id
                 JOIN offers o ON tl.offer_id = o.id
                 JOIN users u ON tl.user_id = u.id
                 ORDER BY c.clicked_at DESC
                 LIMIT 10";

$clicks_stmt = $db->prepare($clicks_query);
$clicks_stmt->execute();
$recent_clicks = $clicks_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب إعدادات Postback
$settings_query = "SELECT setting_key, setting_value FROM settings 
                   WHERE setting_key IN ('postback_password')";
$settings_stmt = $db->prepare($settings_query);
$settings_stmt->execute();
$settings = $settings_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$saved_password = $settings['postback_password'] ?? '';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Postback - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">اختبار Postback</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للإعدادات
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- نموذج الاختبار -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">اختبار Postback</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="test_type" class="form-label">نوع الاختبار</label>
                                        <select class="form-control" id="test_type" name="test_type" onchange="toggleTestFields()">
                                            <option value="simple">اختبار بسيط</option>
                                            <option value="cpalead">اختبار CPALead</option>
                                        </select>
                                    </div>

                                    <div id="simple_fields">
                                        <div class="mb-3">
                                            <label for="click_id" class="form-label">Click ID</label>
                                            <select class="form-control" id="click_id" name="click_id">
                                                <option value="">اختر Click ID أو أدخل يدوياً</option>
                                                <?php foreach ($recent_clicks as $click): ?>
                                                    <option value="<?php echo $click['id']; ?>">
                                                        <?php echo $click['id']; ?> - <?php echo htmlspecialchars($click['offer_title']); ?> (<?php echo htmlspecialchars($click['username']); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <input type="text" class="form-control mt-2" placeholder="أو أدخل Click ID يدوياً" 
                                                   onchange="document.getElementById('click_id').value = this.value">
                                        </div>
                                    </div>

                                    <div id="cpalead_fields" style="display: none;">
                                        <div class="mb-3">
                                            <label for="subid" class="form-label">SubID</label>
                                            <select class="form-control" id="subid" name="subid">
                                                <option value="">اختر SubID أو أدخل يدوياً</option>
                                                <?php foreach ($recent_clicks as $click): ?>
                                                    <?php if (!empty($click['sub_id'])): ?>
                                                        <option value="<?php echo htmlspecialchars($click['sub_id']); ?>">
                                                            <?php echo htmlspecialchars($click['sub_id']); ?> - <?php echo htmlspecialchars($click['offer_title']); ?>
                                                        </option>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </select>
                                            <input type="text" class="form-control mt-2" placeholder="أو أدخل SubID يدوياً" 
                                                   onchange="document.getElementById('subid').value = this.value">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="payout" class="form-label">العمولة</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                            <input type="number" class="form-control" id="payout" name="payout" 
                                                   value="1.00" step="0.01" min="0">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password" class="form-label">كلمة المرور (اختياري)</label>
                                        <input type="text" class="form-control" id="password" name="password" 
                                               value="<?php echo htmlspecialchars($saved_password); ?>" 
                                               placeholder="كلمة مرور Postback">
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-play me-1"></i>تشغيل الاختبار
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- نتيجة الاختبار -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">نتيجة الاختبار</h6>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($test_result)): ?>
                                    <div class="alert alert-<?php echo $test_success ? 'success' : 'danger'; ?>">
                                        <pre style="white-space: pre-wrap; margin: 0;"><?php echo htmlspecialchars($test_result); ?></pre>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-vial fa-3x mb-3"></i>
                                        <p>اختر نوع الاختبار وأدخل البيانات المطلوبة ثم اضغط "تشغيل الاختبار"</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- أمثلة على URLs -->
                        <div class="card shadow mt-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">أمثلة على Postback URLs</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label"><strong>اختبار بسيط:</strong></label>
                                    <code class="d-block p-2 bg-light rounded">
                                        <?php echo SITE_URL; ?>/tracking/postback.php?click_id=12345&payout=2.50&status=approved
                                    </code>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label"><strong>اختبار CPALead:</strong></label>
                                    <code class="d-block p-2 bg-light rounded" style="word-break: break-all;">
                                        <?php echo SITE_URL; ?>/tracking/postback.php?campaign_id=1234&campaign_name=Test&subid=test_001&payout=1.25&lead_id=LEAD_789&country_iso=US&virtual_currency=50
                                    </code>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label"><strong>مع كلمة مرور:</strong></label>
                                    <code class="d-block p-2 bg-light rounded">
                                        <?php echo SITE_URL; ?>/tracking/postback.php?click_id=12345&payout=2.50&status=approved&password=<?php echo htmlspecialchars($saved_password); ?>
                                    </code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- النقرات الأخيرة -->
                <?php if (!empty($recent_clicks)): ?>
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">النقرات الأخيرة المتاحة للاختبار</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Click ID</th>
                                        <th>Sub ID</th>
                                        <th>العرض</th>
                                        <th>المستخدم</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_clicks as $click): ?>
                                    <tr>
                                        <td><code><?php echo $click['id']; ?></code></td>
                                        <td><code><?php echo htmlspecialchars($click['sub_id'] ?: '-'); ?></code></td>
                                        <td><?php echo htmlspecialchars($click['offer_title']); ?></td>
                                        <td><?php echo htmlspecialchars($click['username']); ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="testClick(<?php echo $click['id']; ?>, '<?php echo htmlspecialchars($click['sub_id']); ?>')">
                                                <i class="fas fa-play"></i> اختبار
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        function toggleTestFields() {
            const testType = document.getElementById('test_type').value;
            const simpleFields = document.getElementById('simple_fields');
            const cpalead_fields = document.getElementById('cpalead_fields');
            
            if (testType === 'simple') {
                simpleFields.style.display = 'block';
                cpalead_fields.style.display = 'none';
            } else {
                simpleFields.style.display = 'none';
                cpalead_fields.style.display = 'block';
            }
        }

        function testClick(clickId, subId) {
            document.getElementById('click_id').value = clickId;
            document.getElementById('subid').value = subId;
            
            if (subId) {
                document.getElementById('test_type').value = 'cpalead';
                toggleTestFields();
            } else {
                document.getElementById('test_type').value = 'simple';
                toggleTestFields();
            }
        }
    </script>
</body>
</html>
