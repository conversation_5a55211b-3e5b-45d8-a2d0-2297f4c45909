# Postback Documentation - CPA Marketing System

## نظرة عامة
يدعم نظام CPA الخاص بنا نظام Postback متقدم متوافق مع جميع الشبكات الرئيسية مثل CPALead وMaxBounty وغيرها. يتيح هذا النظام تتبع التحويلات في الوقت الفعلي وإرسال إشعارات فورية عند حدوث التحويلات.

## إعداد روابط التتبع

### الشكل الأساسي لرابط التتبع
```
https://yourdomain.com/tracking/click.php?t={tracking_id}&subid={your_subid}&subid2={additional_subid}&subid3={additional_subid}
```

### للتطبيقات المحمولة
```
https://yourdomain.com/tracking/click.php?t={tracking_id}&subid={your_subid}&idfa={apple_idfa}&gaid={google_gaid}
```

### مثال كامل
```
https://yourdomain.com/tracking/click.php?t=abc123def456&subid=campaign_001&subid2=source_facebook&subid3=ad_group_1&idfa=12345678-1234-1234-1234-123456789012
```

## إعداد Postback URL

### URL الأساسي
```
https://yourdomain.com/tracking/postback.php
```

### مثال Postback URL للشبكات
```
https://yourdomain.com/tracking/postback.php?click_id={click_id}&transaction_id={transaction_id}&payout={payout}&status=approved
```

### مثال متقدم مع جميع المعاملات (متوافق مع CPALead)
```
https://yourdomain.com/tracking/postback.php?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&payout={payout}&ip_address={ip_address}&gateway_id={gateway_id}&lead_id={lead_id}&country_iso={country_iso}&password={password}&virtual_currency={virtual_currency}&click_id={click_id}&transaction_id={transaction_id}&status=approved
```

## المعاملات المدعومة

### المعاملات الأساسية
| المعامل | الوصف | مطلوب | مثال |
|---------|-------|--------|-------|
| `click_id` | معرف النقرة الفريد | نعم | 12345 |
| `transaction_id` | معرف المعاملة من الشبكة | لا | TXN_ABC123 |
| `payout` | قيمة العمولة | لا | 2.50 |
| `status` | حالة التحويل | لا | approved |

### معاملات CPALead
| المعامل | الوصف | مثال |
|---------|-------|-------|
| `campaign_id` | معرف الحملة | 1234 |
| `campaign_name` | اسم الحملة | Mobile App Install |
| `lead_id` | معرف العميل المحتمل | LEAD_789 |
| `gateway_id` | معرف البوابة | GW_456 |
| `virtual_currency` | العملة الافتراضية | 100 |
| `country_iso` | رمز البلد (ISO) | US |
| `password` | كلمة مرور Postback | secret123 |

### معاملات Sub ID
| المعامل | الوصف | مثال |
|---------|-------|-------|
| `subid` | معرف فرعي أساسي | campaign_001 |
| `subid2` | معرف فرعي إضافي | source_facebook |
| `subid3` | معرف فرعي إضافي | ad_group_1 |

### معاملات الأجهزة المحمولة
| المعامل | الوصف | مثال |
|---------|-------|-------|
| `idfa` | Apple IDFA | 12345678-1234-1234-1234-123456789012 |
| `gaid` | Google Advertising ID | 12345678-1234-1234-1234-123456789012 |

## أمثلة على الاستخدام

### 1. Postback بسيط
```
GET https://yourdomain.com/tracking/postback.php?click_id=12345&payout=2.50&status=approved
```

### 2. Postback من CPALead
```
GET https://yourdomain.com/tracking/postback.php?campaign_id=1234&campaign_name=Mobile%20Survey&subid=test_001&subid2=facebook&payout=1.25&lead_id=LEAD_789&country_iso=US&password=secret123&virtual_currency=50
```

### 3. Postback للتطبيقات المحمولة
```
GET https://yourdomain.com/tracking/postback.php?click_id=12345&payout=3.00&idfa=12345678-1234-1234-1234-123456789012&status=approved
```

## الاستجابات

### استجابة ناجحة
```
HTTP/1.1 200 OK
Content-Type: text/plain

OK - تم تسجيل التحويل بنجاح
```

### استجابة خطأ - معرف مفقود
```
HTTP/1.1 400 Bad Request
Content-Type: text/plain

معرف النقرة أو SubID مطلوب
```

### استجابة خطأ - كلمة مرور خاطئة
```
HTTP/1.1 401 Unauthorized
Content-Type: text/plain

كلمة مرور Postback غير صحيحة
```

### استجابة خطأ - IP غير مصرح
```
HTTP/1.1 403 Forbidden
Content-Type: text/plain

IP غير مصرح
```

## الأمان

### 1. كلمة مرور Postback
يمكن تعيين كلمة مرور لحماية Postback URLs:
```php
// في ملف tracking/postback.php
$required_password = 'your_secure_password_here';
```

### 2. قائمة IPs المسموحة
يتم التحقق من IP المصدر:
```php
$allowed_ips = [
    '************',  // CPALead IP
    '*************', // IP أخرى
    '127.0.0.1'      // للاختبار المحلي
];
```

### 3. HTTPS
يُنصح بشدة باستخدام HTTPS لجميع Postback URLs.

## اختبار Postback

### أداة اختبار بسيطة
```bash
# اختبار Postback بسيط
curl "https://yourdomain.com/tracking/postback.php?click_id=12345&payout=2.50&status=approved"

# اختبار مع كلمة مرور
curl "https://yourdomain.com/tracking/postback.php?click_id=12345&payout=2.50&status=approved&password=secret123"
```

### سكريبت PHP للاختبار
```php
<?php
$postback_url = 'https://yourdomain.com/tracking/postback.php';
$params = [
    'click_id' => '12345',
    'transaction_id' => 'TEST_TXN_001',
    'payout' => '2.50',
    'status' => 'approved',
    'password' => 'secret123'
];

$url = $postback_url . '?' . http_build_query($params);
$response = file_get_contents($url);
echo "Response: " . $response;
?>
```

## إعداد الشبكات

### CPALead
1. اذهب إلى لوحة تحكم CPALead
2. انتقل إلى "Postback" -> "Configuration"
3. أدخل Postback URL:
```
https://yourdomain.com/tracking/postback.php?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&payout={payout}&lead_id={lead_id}&country_iso={country_iso}&password=your_password&virtual_currency={virtual_currency}
```

### MaxBounty
```
https://yourdomain.com/tracking/postback.php?click_id={transaction_id}&payout={payout}&status=approved&password=your_password
```

### PeerFly
```
https://yourdomain.com/tracking/postback.php?subid={s1}&transaction_id={transaction_id}&payout={payout}&status=approved
```

## استكشاف الأخطاء

### مشاكل شائعة
1. **لا يتم استقبال Postbacks**: تحقق من IP المصدر وكلمة المرور
2. **تحويلات مكررة**: النظام يتعامل مع هذا تلقائياً
3. **معاملات مفقودة**: تحقق من إعداد Postback URL في الشبكة

### سجلات الأخطاء
يتم تسجيل جميع الأخطاء في:
- `logs/error.log`
- جدول `activity_logs` في قاعدة البيانات

### تفعيل وضع التطوير
```php
// في config/config.php
define('DEBUG_MODE', true);
```

## API للمطورين

### جلب إحصائيات التحويلات
```
GET /api/stats.php?api_key=your_api_key&date_from=2024-01-01&date_to=2024-01-31
```

### إنشاء رابط تتبع برمجياً
```
POST /api/tracking/create
{
    "offer_id": 123,
    "subid": "campaign_001",
    "source": "facebook"
}
```

## الدعم
للحصول على المساعدة:
1. راجع سجلات الأخطاء
2. تحقق من إعدادات الشبكة
3. اتصل بالدعم الفني

---

**ملاحظة**: تأكد من تحديث كلمات المرور وIPs المسموحة قبل الاستخدام في الإنتاج.
