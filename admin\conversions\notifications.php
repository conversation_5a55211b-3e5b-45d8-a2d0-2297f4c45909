<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$notificationManager = new NotificationManager($database);

$errors = [];
$success = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitize($_POST['action'] ?? '');
    
    switch ($action) {
        case 'send_conversion_reminder':
            $user_id = intval($_POST['user_id']);
            $offer_id = intval($_POST['offer_id']);
            
            // جلب بيانات المستخدم والعرض
            $user_query = "SELECT username, email FROM users WHERE id = :user_id";
            $user_stmt = $db->prepare($user_query);
            $user_stmt->bindParam(':user_id', $user_id);
            $user_stmt->execute();
            $user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);
            
            $offer_query = "SELECT title, payout FROM offers WHERE id = :offer_id";
            $offer_stmt = $db->prepare($offer_query);
            $offer_stmt->bindParam(':offer_id', $offer_id);
            $offer_stmt->execute();
            $offer_data = $offer_stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user_data && $offer_data) {
                $notification_data = [
                    'title' => '⏰ تذكير: أكمل العرض للحصول على العمولة',
                    'message' => "لا تنس إكمال العرض: {$offer_data['title']} للحصول على عمولة " . CURRENCY_SYMBOL . number_format($offer_data['payout'], 2),
                    'type' => 'warning',
                    'target_audience' => 'custom',
                    'target_users' => json_encode([$user_id]),
                    'offer_id' => $offer_id,
                    'priority' => 'medium',
                    'show_popup' => true,
                    'show_banner' => true,
                    'show_sidebar' => true,
                    'auto_hide' => true,
                    'hide_after_seconds' => 15,
                    'start_date' => date('Y-m-d H:i:s'),
                    'end_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
                    'created_by' => $_SESSION['user_id']
                ];
                
                if ($notificationManager->createNotification($notification_data)) {
                    $success = 'تم إرسال تذكير التحويل بنجاح!';
                } else {
                    $errors[] = 'فشل في إرسال التذكير';
                }
            } else {
                $errors[] = 'بيانات المستخدم أو العرض غير صحيحة';
            }
            break;
            
        case 'send_bulk_reminder':
            $days_ago = intval($_POST['days_ago']);
            
            // جلب المستخدمين الذين لديهم نقرات بدون تحويلات
            $users_query = "SELECT DISTINCT c.user_id, u.username, c.offer_id, o.title as offer_title, o.payout
                           FROM clicks c
                           INNER JOIN users u ON c.user_id = u.id
                           INNER JOIN offers o ON c.offer_id = o.id
                           LEFT JOIN conversions conv ON c.id = conv.click_id
                           WHERE c.created_at >= DATE_SUB(NOW(), INTERVAL :days_ago DAY)
                           AND conv.id IS NULL
                           AND u.status = 'active'
                           ORDER BY c.created_at DESC
                           LIMIT 50";
            
            $users_stmt = $db->prepare($users_query);
            $users_stmt->bindParam(':days_ago', $days_ago);
            $users_stmt->execute();
            $users_data = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $sent_count = 0;
            foreach ($users_data as $user_data) {
                $notification_data = [
                    'title' => '⏰ تذكير: أكمل العروض للحصول على العمولات',
                    'message' => "لديك عروض لم تكملها بعد! أكمل العرض: {$user_data['offer_title']} للحصول على عمولة " . CURRENCY_SYMBOL . number_format($user_data['payout'], 2),
                    'type' => 'warning',
                    'target_audience' => 'custom',
                    'target_users' => json_encode([$user_data['user_id']]),
                    'offer_id' => $user_data['offer_id'],
                    'priority' => 'medium',
                    'show_popup' => false,
                    'show_banner' => true,
                    'show_sidebar' => true,
                    'auto_hide' => true,
                    'hide_after_seconds' => 20,
                    'start_date' => date('Y-m-d H:i:s'),
                    'end_date' => date('Y-m-d H:i:s', strtotime('+5 days')),
                    'created_by' => $_SESSION['user_id']
                ];
                
                if ($notificationManager->createNotification($notification_data)) {
                    $sent_count++;
                }
            }
            
            $success = "تم إرسال {$sent_count} تذكير للمستخدمين";
            break;
            
        case 'test_conversion_notification':
            $test_user_id = intval($_POST['test_user_id']);
            
            // إنشاء إشعار تجريبي
            $notification_data = [
                'title' => '🧪 إشعار تجريبي - تم إكمال العرض',
                'message' => 'هذا إشعار تجريبي لاختبار نظام إشعارات التحويلات. تم إكمال العرض التجريبي وحصلت على عمولة $25.00',
                'type' => 'success',
                'target_audience' => 'custom',
                'target_users' => json_encode([$test_user_id]),
                'offer_id' => null,
                'priority' => 'high',
                'show_popup' => true,
                'show_banner' => true,
                'show_sidebar' => true,
                'auto_hide' => false,
                'hide_after_seconds' => 0,
                'start_date' => date('Y-m-d H:i:s'),
                'end_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'created_by' => $_SESSION['user_id']
            ];
            
            if ($notificationManager->createNotification($notification_data)) {
                $success = 'تم إرسال الإشعار التجريبي بنجاح!';
            } else {
                $errors[] = 'فشل في إرسال الإشعار التجريبي';
            }
            break;
    }
}

// جلب إحصائيات التحويلات
$conversion_stats = [];
try {
    $stats_query = "SELECT 
                        COUNT(*) as total_conversions,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_conversions,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_conversions,
                        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_conversions,
                        SUM(CASE WHEN status = 'approved' THEN payout ELSE 0 END) as total_earnings
                    FROM conversions 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
    
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->execute();
    $conversion_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // قيم افتراضية
    $conversion_stats = [
        'total_conversions' => 0,
        'approved_conversions' => 0,
        'pending_conversions' => 0,
        'rejected_conversions' => 0,
        'total_earnings' => 0
    ];
}

// جلب المستخدمين النشطين
$users_query = "SELECT id, username FROM users WHERE status = 'active' AND role = 'publisher' ORDER BY username";
$users_stmt = $db->prepare($users_query);
$users_stmt->execute();
$active_users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب آخر التحويلات
$recent_conversions_query = "SELECT c.*, u.username, o.title as offer_title 
                            FROM conversions c
                            INNER JOIN users u ON c.user_id = u.id
                            INNER JOIN offers o ON c.offer_id = o.id
                            ORDER BY c.created_at DESC
                            LIMIT 10";
$recent_conversions_stmt = $db->prepare($recent_conversions_query);
$recent_conversions_stmt->execute();
$recent_conversions = $recent_conversions_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إشعارات التحويلات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة إشعارات التحويلات</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <span class="badge bg-info">إدارة الإشعارات</span>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات التحويلات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center bg-primary text-white">
                            <div class="card-body">
                                <h3><?php echo number_format($conversion_stats['total_conversions']); ?></h3>
                                <p class="mb-0">إجمالي التحويلات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-success text-white">
                            <div class="card-body">
                                <h3><?php echo number_format($conversion_stats['approved_conversions']); ?></h3>
                                <p class="mb-0">التحويلات المعتمدة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-warning text-dark">
                            <div class="card-body">
                                <h3><?php echo number_format($conversion_stats['pending_conversions']); ?></h3>
                                <p class="mb-0">في الانتظار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-info text-white">
                            <div class="card-body">
                                <h3><?php echo CURRENCY_SYMBOL . number_format($conversion_stats['total_earnings'], 2); ?></h3>
                                <p class="mb-0">إجمالي الأرباح</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- إرسال تذكير فردي -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إرسال تذكير فردي</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="send_conversion_reminder">
                                    
                                    <div class="mb-3">
                                        <label for="user_id" class="form-label">المستخدم</label>
                                        <select class="form-select" id="user_id" name="user_id" required>
                                            <option value="">اختر المستخدم</option>
                                            <?php foreach ($active_users as $user): ?>
                                                <option value="<?php echo $user['id']; ?>"><?php echo htmlspecialchars($user['username']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="offer_id" class="form-label">العرض</label>
                                        <select class="form-select" id="offer_id" name="offer_id" required>
                                            <option value="">اختر العرض</option>
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </select>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-paper-plane me-1"></i>إرسال التذكير
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إرسال تذكير جماعي -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-warning">تذكير جماعي</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="send_bulk_reminder">
                                    
                                    <div class="mb-3">
                                        <label for="days_ago" class="form-label">المستخدمين الذين نقروا خلال</label>
                                        <select class="form-select" id="days_ago" name="days_ago" required>
                                            <option value="1">آخر يوم</option>
                                            <option value="3" selected>آخر 3 أيام</option>
                                            <option value="7">آخر أسبوع</option>
                                            <option value="14">آخر أسبوعين</option>
                                            <option value="30">آخر شهر</option>
                                        </select>
                                    </div>
                                    
                                    <div class="alert alert-info">
                                        <small>سيتم إرسال تذكيرات للمستخدمين الذين نقروا على العروض ولم يكملوها</small>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-warning w-100" onclick="return confirm('هل أنت متأكد من إرسال تذكيرات جماعية؟')">
                                        <i class="fas fa-bullhorn me-1"></i>إرسال تذكيرات جماعية
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- اختبار الإشعارات -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-success">اختبار الإشعارات</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="test_conversion_notification">
                                    
                                    <div class="mb-3">
                                        <label for="test_user_id" class="form-label">المستخدم للاختبار</label>
                                        <select class="form-select" id="test_user_id" name="test_user_id" required>
                                            <option value="">اختر المستخدم</option>
                                            <?php foreach ($active_users as $user): ?>
                                                <option value="<?php echo $user['id']; ?>"><?php echo htmlspecialchars($user['username']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="alert alert-success">
                                        <small>سيتم إرسال إشعار تجريبي لاختبار النظام</small>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-flask me-1"></i>إرسال إشعار تجريبي
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر التحويلات -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-dark">آخر التحويلات</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_conversions)): ?>
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <p>لا توجد تحويلات حديثة</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>المستخدم</th>
                                            <th>العرض</th>
                                            <th>العمولة</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_conversions as $conversion): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($conversion['username']); ?></td>
                                                <td><?php echo htmlspecialchars($conversion['offer_title']); ?></td>
                                                <td><?php echo CURRENCY_SYMBOL . number_format($conversion['payout'], 2); ?></td>
                                                <td>
                                                    <?php
                                                    $status_classes = [
                                                        'approved' => 'success',
                                                        'pending' => 'warning',
                                                        'rejected' => 'danger',
                                                        'hold' => 'secondary'
                                                    ];
                                                    $status_class = $status_classes[$conversion['status']] ?? 'secondary';
                                                    ?>
                                                    <span class="badge bg-<?php echo $status_class; ?>"><?php echo $conversion['status']; ?></span>
                                                </td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($conversion['created_at'])); ?></td>
                                                <td>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="send_conversion_reminder">
                                                        <input type="hidden" name="user_id" value="<?php echo $conversion['user_id']; ?>">
                                                        <input type="hidden" name="offer_id" value="<?php echo $conversion['offer_id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-primary" title="إرسال تذكير">
                                                            <i class="fas fa-bell"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        // تحميل العروض عند اختيار المستخدم
        document.getElementById('user_id').addEventListener('change', function() {
            const userId = this.value;
            const offerSelect = document.getElementById('offer_id');
            
            if (userId) {
                fetch(`../../api/user-offers.php?user_id=${userId}`)
                    .then(response => response.json())
                    .then(data => {
                        offerSelect.innerHTML = '<option value="">اختر العرض</option>';
                        data.offers.forEach(offer => {
                            offerSelect.innerHTML += `<option value="${offer.id}">${offer.title} - ${offer.payout}</option>`;
                        });
                    })
                    .catch(error => {
                        console.error('Error loading offers:', error);
                        offerSelect.innerHTML = '<option value="">خطأ في تحميل العروض</option>';
                    });
            } else {
                offerSelect.innerHTML = '<option value="">اختر العرض</option>';
            }
        });
    </script>
</body>
</html>
