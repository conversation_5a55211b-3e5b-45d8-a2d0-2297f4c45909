<?php
require_once '../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

$errors = [];
$success = '';

// جلب بيانات الأدمن الحالية
$admin_query = "SELECT * FROM users WHERE id = :user_id AND role = 'admin'";
$admin_stmt = $db->prepare($admin_query);
$admin_stmt->bindParam(':user_id', $_SESSION['user_id']);
$admin_stmt->execute();
$admin_data = $admin_stmt->fetch(PDO::FETCH_ASSOC);

if (!$admin_data) {
    $_SESSION['error'] = 'لم يتم العثور على بيانات المدير';
    header('Location: dashboard.php');
    exit();
}

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitize($_POST['action'] ?? '');
    
    switch ($action) {
        case 'update_profile':
            $username = sanitize($_POST['username']);
            $email = sanitize($_POST['email']);
            $first_name = sanitize($_POST['first_name']);
            $last_name = sanitize($_POST['last_name']);
            
            // التحقق من البيانات
            if (empty($username)) {
                $errors[] = 'اسم المستخدم مطلوب';
            } elseif (strlen($username) < 3) {
                $errors[] = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
            }
            
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'البريد الإلكتروني غير صحيح';
            }
            
            if (empty($first_name)) {
                $errors[] = 'الاسم الأول مطلوب';
            }
            
            if (empty($last_name)) {
                $errors[] = 'الاسم الأخير مطلوب';
            }
            
            // التحقق من عدم تكرار اسم المستخدم والبريد الإلكتروني
            if (empty($errors)) {
                $check_query = "SELECT id FROM users WHERE (username = :username OR email = :email) AND id != :user_id";
                $check_stmt = $db->prepare($check_query);
                $check_stmt->bindParam(':username', $username);
                $check_stmt->bindParam(':email', $email);
                $check_stmt->bindParam(':user_id', $_SESSION['user_id']);
                $check_stmt->execute();
                
                if ($check_stmt->rowCount() > 0) {
                    $errors[] = 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل';
                }
            }
            
            // تحديث البيانات
            if (empty($errors)) {
                try {
                    $update_query = "UPDATE users SET 
                                    username = :username, 
                                    email = :email, 
                                    first_name = :first_name, 
                                    last_name = :last_name,
                                    updated_at = NOW()
                                    WHERE id = :user_id";
                    
                    $update_stmt = $db->prepare($update_query);
                    $update_stmt->bindParam(':username', $username);
                    $update_stmt->bindParam(':email', $email);
                    $update_stmt->bindParam(':first_name', $first_name);
                    $update_stmt->bindParam(':last_name', $last_name);
                    $update_stmt->bindParam(':user_id', $_SESSION['user_id']);
                    
                    if ($update_stmt->execute()) {
                        // تحديث بيانات الجلسة
                        $_SESSION['username'] = $username;
                        
                        $success = 'تم تحديث البيانات الشخصية بنجاح!';
                        
                        // إعادة جلب البيانات المحدثة
                        $admin_stmt->execute();
                        $admin_data = $admin_stmt->fetch(PDO::FETCH_ASSOC);
                    } else {
                        $errors[] = 'حدث خطأ في تحديث البيانات';
                    }
                    
                } catch (PDOException $e) {
                    logError("خطأ في تحديث ملف الأدمن: " . $e->getMessage());
                    $errors[] = 'حدث خطأ في النظام';
                }
            }
            break;
            
        case 'change_password':
            $current_password = $_POST['current_password'];
            $new_password = $_POST['new_password'];
            $confirm_password = $_POST['confirm_password'];
            
            // التحقق من كلمة المرور الحالية
            if (!password_verify($current_password, $admin_data['password'])) {
                $errors[] = 'كلمة المرور الحالية غير صحيحة';
            }
            
            // التحقق من كلمة المرور الجديدة
            if (empty($new_password)) {
                $errors[] = 'كلمة المرور الجديدة مطلوبة';
            } elseif (strlen($new_password) < 6) {
                $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            }
            
            if ($new_password !== $confirm_password) {
                $errors[] = 'كلمات المرور غير متطابقة';
            }
            
            // تحديث كلمة المرور
            if (empty($errors)) {
                try {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    
                    $password_query = "UPDATE users SET password = :password, updated_at = NOW() WHERE id = :user_id";
                    $password_stmt = $db->prepare($password_query);
                    $password_stmt->bindParam(':password', $hashed_password);
                    $password_stmt->bindParam(':user_id', $_SESSION['user_id']);
                    
                    if ($password_stmt->execute()) {
                        $success = 'تم تغيير كلمة المرور بنجاح!';
                    } else {
                        $errors[] = 'حدث خطأ في تغيير كلمة المرور';
                    }
                    
                } catch (PDOException $e) {
                    logError("خطأ في تغيير كلمة مرور الأدمن: " . $e->getMessage());
                    $errors[] = 'حدث خطأ في النظام';
                }
            }
            break;
            
        case 'regenerate_api_key':
            try {
                $new_api_key = hash('sha256', uniqid(rand(), true));
                
                $api_query = "UPDATE users SET api_key = :api_key, updated_at = NOW() WHERE id = :user_id";
                $api_stmt = $db->prepare($api_query);
                $api_stmt->bindParam(':api_key', $new_api_key);
                $api_stmt->bindParam(':user_id', $_SESSION['user_id']);
                
                if ($api_stmt->execute()) {
                    $success = 'تم إنشاء مفتاح API جديد بنجاح!';
                    
                    // إعادة جلب البيانات المحدثة
                    $admin_stmt->execute();
                    $admin_data = $admin_stmt->fetch(PDO::FETCH_ASSOC);
                } else {
                    $errors[] = 'حدث خطأ في إنشاء مفتاح API جديد';
                }
                
            } catch (PDOException $e) {
                logError("خطأ في إنشاء مفتاح API للأدمن: " . $e->getMessage());
                $errors[] = 'حدث خطأ في النظام';
            }
            break;
    }
}

// جلب إحصائيات نشاط الأدمن
$activity_query = "SELECT COUNT(*) as total_logins, MAX(last_login) as last_login_time 
                   FROM users WHERE id = :user_id";
$activity_stmt = $db->prepare($activity_query);
$activity_stmt->bindParam(':user_id', $_SESSION['user_id']);
$activity_stmt->execute();
$activity_stats = $activity_stmt->fetch(PDO::FETCH_ASSOC);

// جلب آخر الأنشطة
$logs_query = "SELECT action, description, created_at FROM activity_logs 
               WHERE user_id = :user_id 
               ORDER BY created_at DESC 
               LIMIT 10";
$logs_stmt = $db->prepare($logs_query);
$logs_stmt->bindParam(':user_id', $_SESSION['user_id']);
$logs_stmt->execute();
$recent_activities = $logs_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي للمدير - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">الملف الشخصي للمدير</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <span class="badge bg-success">مدير النظام</span>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- البيانات الشخصية -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">البيانات الشخصية</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_profile">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="username" class="form-label">اسم المستخدم</label>
                                                <input type="text" class="form-control" id="username" name="username" 
                                                       value="<?php echo htmlspecialchars($admin_data['username']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                                <input type="email" class="form-control" id="email" name="email" 
                                                       value="<?php echo htmlspecialchars($admin_data['email']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="first_name" class="form-label">الاسم الأول</label>
                                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                                       value="<?php echo htmlspecialchars($admin_data['first_name']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="last_name" class="form-label">الاسم الأخير</label>
                                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                                       value="<?php echo htmlspecialchars($admin_data['last_name']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- تغيير كلمة المرور -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-warning">تغيير كلمة المرور</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="change_password">
                                    
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الحساب -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-info">معلومات الحساب</h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="avatar-circle bg-primary text-white mx-auto mb-2" style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem;">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <h5><?php echo htmlspecialchars($admin_data['first_name'] . ' ' . $admin_data['last_name']); ?></h5>
                                    <span class="badge bg-danger">مدير النظام</span>
                                </div>
                                
                                <hr>
                                
                                <div class="mb-2">
                                    <strong>معرف المستخدم:</strong><br>
                                    <code>#<?php echo $admin_data['id']; ?></code>
                                </div>
                                
                                <div class="mb-2">
                                    <strong>تاريخ الإنشاء:</strong><br>
                                    <?php echo date('Y-m-d H:i', strtotime($admin_data['created_at'])); ?>
                                </div>
                                
                                <div class="mb-2">
                                    <strong>آخر تحديث:</strong><br>
                                    <?php echo $admin_data['updated_at'] ? date('Y-m-d H:i', strtotime($admin_data['updated_at'])) : 'لم يتم التحديث'; ?>
                                </div>
                                
                                <div class="mb-2">
                                    <strong>آخر تسجيل دخول:</strong><br>
                                    <?php echo $admin_data['last_login'] ? date('Y-m-d H:i', strtotime($admin_data['last_login'])) : 'لم يسجل دخول من قبل'; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>عدد تسجيلات الدخول:</strong><br>
                                    <span class="badge bg-success"><?php echo number_format($admin_data['login_count'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- مفتاح API -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-secondary">مفتاح API</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">مفتاح API الحالي:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($admin_data['api_key']); ?>" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="copyApiKey()">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <form method="POST" onsubmit="return confirm('هل أنت متأكد من إنشاء مفتاح API جديد؟ سيتم إلغاء المفتاح الحالي.')">
                                    <input type="hidden" name="action" value="regenerate_api_key">
                                    <button type="submit" class="btn btn-secondary btn-sm w-100">
                                        <i class="fas fa-sync me-1"></i>إنشاء مفتاح جديد
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الأنشطة -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">آخر الأنشطة</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_activities)): ?>
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-history fa-2x mb-2"></i>
                                <p>لا توجد أنشطة مسجلة</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>النشاط</th>
                                            <th>الوصف</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_activities as $activity): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($activity['action']); ?></td>
                                                <td><?php echo htmlspecialchars($activity['description']); ?></td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($activity['created_at'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        function copyApiKey() {
            const apiKeyInput = document.querySelector('input[readonly]');
            apiKeyInput.select();
            document.execCommand('copy');
            
            // تغيير أيقونة الزر مؤقتاً
            const copyBtn = document.querySelector('button[onclick="copyApiKey()"]');
            const originalHTML = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check text-success"></i>';
            
            setTimeout(() => {
                copyBtn.innerHTML = originalHTML;
            }, 2000);
        }

        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
