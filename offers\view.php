<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit();
}

$offer_id = intval($_GET['id'] ?? 0);
if ($offer_id <= 0) {
    header('Location: index.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

// جلب تفاصيل العرض
$offer_query = "SELECT o.*, n.name as network_name, n.website as network_website,
                CASE 
                    WHEN o.external_image_url IS NOT NULL AND o.external_image_url != '' 
                    THEN o.external_image_url 
                    ELSE o.image 
                END as display_image,
                CASE 
                    WHEN o.is_external = 1 THEN 'external'
                    ELSE 'network'
                END as offer_source
                FROM offers o 
                JOIN networks n ON o.network_id = n.id 
                WHERE o.id = :offer_id AND o.status = 'active'";

$offer_stmt = $db->prepare($offer_query);
$offer_stmt->bindParam(':offer_id', $offer_id);
$offer_stmt->execute();

$offer = $offer_stmt->fetch(PDO::FETCH_ASSOC);

if (!$offer) {
    $_SESSION['error'] = 'العرض غير موجود أو غير متاح';
    header('Location: index.php');
    exit();
}

// جلب إحصائيات المستخدم لهذا العرض
$stats_query = "SELECT 
    COALESCE(SUM(clicks), 0) as total_clicks,
    COALESCE(SUM(unique_clicks), 0) as unique_clicks,
    COALESCE(SUM(conversions), 0) as total_conversions,
    COALESCE(SUM(earnings), 0) as total_earnings
    FROM daily_stats 
    WHERE user_id = :user_id AND offer_id = :offer_id";

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->bindParam(':user_id', $user_id);
$stats_stmt->bindParam(':offer_id', $offer_id);
$stats_stmt->execute();
$user_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// جلب روابط التتبع الموجودة
$links_query = "SELECT tracking_id, sub_id, source, campaign, created_at 
                FROM tracking_links 
                WHERE user_id = :user_id AND offer_id = :offer_id AND status = 'active'
                ORDER BY created_at DESC
                LIMIT 5";

$links_stmt = $db->prepare($links_query);
$links_stmt->bindParam(':user_id', $user_id);
$links_stmt->bindParam(':offer_id', $offer_id);
$links_stmt->execute();
$tracking_links = $links_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($offer['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تفاصيل العرض</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="../tracking/generate.php?offer_id=<?php echo $offer['id']; ?>" class="btn btn-success">
                                <i class="fas fa-link me-1"></i>إنشاء رابط تتبع
                            </a>
                            <?php if ($offer['offer_source'] === 'external'): ?>
                                <a href="<?php echo htmlspecialchars($offer['external_offer_url']); ?>" 
                                   target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>زيارة العرض
                                </a>
                            <?php endif; ?>
                        </div>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للعروض
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- تفاصيل العرض -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h3 class="mb-3">
                                            <?php echo htmlspecialchars($offer['title']); ?>
                                            <?php if ($offer['offer_source'] === 'external'): ?>
                                                <span class="badge bg-info ms-2">عرض يدوي</span>
                                            <?php endif; ?>
                                        </h3>
                                        
                                        <?php if ($offer['description']): ?>
                                            <div class="mb-3">
                                                <h6>الوصف:</h6>
                                                <p class="text-muted"><?php echo nl2br(htmlspecialchars($offer['description'])); ?></p>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="row mb-3">
                                            <div class="col-sm-6">
                                                <strong>الشبكة:</strong> 
                                                <a href="<?php echo htmlspecialchars($offer['network_website']); ?>" target="_blank">
                                                    <?php echo htmlspecialchars($offer['network_name']); ?>
                                                </a>
                                            </div>
                                            <div class="col-sm-6">
                                                <strong>النوع:</strong> 
                                                <span class="badge bg-info"><?php echo strtoupper($offer['type']); ?></span>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-sm-6">
                                                <strong>العمولة:</strong> 
                                                <span class="text-success fs-5"><?php echo CURRENCY_SYMBOL . number_format($offer['payout'], 2); ?></span>
                                            </div>
                                            <div class="col-sm-6">
                                                <strong>الفئة:</strong> <?php echo htmlspecialchars($offer['category'] ?: 'غير محدد'); ?>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-sm-6">
                                                <strong>البلدان:</strong> <?php echo htmlspecialchars($offer['countries'] ?: 'جميع البلدان'); ?>
                                            </div>
                                            <div class="col-sm-6">
                                                <strong>تاريخ الإضافة:</strong> <?php echo date('Y-m-d', strtotime($offer['created_at'])); ?>
                                            </div>
                                        </div>
                                        
                                        <?php if ($offer['restrictions']): ?>
                                            <div class="mb-3">
                                                <h6>متطلبات الإكمال:</h6>
                                                <div class="alert alert-info">
                                                    <?php echo nl2br(htmlspecialchars($offer['restrictions'])); ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-4 text-center">
                                        <?php if ($offer['display_image']): ?>
                                            <?php if ($offer['offer_source'] === 'external' && $offer['external_image_url']): ?>
                                                <img src="<?php echo htmlspecialchars($offer['external_image_url']); ?>" 
                                                     class="img-fluid rounded shadow" alt="صورة العرض" style="max-height: 250px;"
                                                     onerror="this.style.display='none'">
                                            <?php elseif ($offer['image']): ?>
                                                <img src="../uploads/offers/<?php echo $offer['image']; ?>" 
                                                     class="img-fluid rounded shadow" alt="صورة العرض" style="max-height: 250px;">
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                                <i class="fas fa-image fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإحصائيات -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إحصائياتك</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <div class="border-end">
                                            <h4 class="text-primary"><?php echo number_format($user_stats['total_clicks']); ?></h4>
                                            <small class="text-muted">إجمالي النقرات</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h4 class="text-info"><?php echo number_format($user_stats['unique_clicks']); ?></h4>
                                        <small class="text-muted">نقرات فريدة</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h4 class="text-success"><?php echo number_format($user_stats['total_conversions']); ?></h4>
                                            <small class="text-muted">التحويلات</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-warning"><?php echo CURRENCY_SYMBOL . number_format($user_stats['total_earnings'], 2); ?></h4>
                                        <small class="text-muted">الأرباح</small>
                                    </div>
                                </div>
                                
                                <?php if ($user_stats['total_clicks'] > 0): ?>
                                    <hr>
                                    <div class="text-center">
                                        <small class="text-muted">معدل التحويل: 
                                            <strong><?php echo number_format(($user_stats['total_conversions'] / $user_stats['total_clicks']) * 100, 2); ?>%</strong>
                                        </small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- روابط التتبع الأخيرة -->
                        <?php if (!empty($tracking_links)): ?>
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">روابط التتبع الأخيرة</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($tracking_links as $link): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                        <div>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($link['sub_id'] ?: 'بدون SubID'); ?>
                                                <?php if ($link['source']): ?>
                                                    | <?php echo htmlspecialchars($link['source']); ?>
                                                <?php endif; ?>
                                            </small>
                                            <br>
                                            <code class="small"><?php echo htmlspecialchars($link['tracking_id']); ?></code>
                                        </div>
                                        <small class="text-muted"><?php echo date('m/d', strtotime($link['created_at'])); ?></small>
                                    </div>
                                <?php endforeach; ?>
                                <div class="text-center mt-3">
                                    <a href="../tracking/generate.php?offer_id=<?php echo $offer['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        عرض جميع الروابط
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
