<?php
require_once '../config/config.php';

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: login.php');
    exit();
}

// التحقق من البيانات المرسلة
$email = sanitize($_POST['email'] ?? '');
$password = $_POST['password'] ?? '';
$remember = isset($_POST['remember']);

// التحقق من وجود البيانات
if (empty($email) || empty($password)) {
    $_SESSION['error'] = 'يرجى ملء جميع الحقول المطلوبة';
    header('Location: login.php');
    exit();
}

// التحقق من صحة البريد الإلكتروني
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $_SESSION['error'] = 'يرجى إدخال بريد إلكتروني صحيح';
    header('Location: login.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // البحث عن المستخدم
    $query = "SELECT id, username, email, password, first_name, last_name, role, status 
              FROM users 
              WHERE email = :email";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();
    
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // التحقق من وجود المستخدم وصحة كلمة المرور
    if ($user && password_verify($password, $user['password'])) {
        
        // التحقق من حالة الحساب
        if ($user['status'] !== 'active') {
            $_SESSION['error'] = 'حسابك غير مفعل أو معلق. يرجى التواصل مع الإدارة';
            header('Location: login.php');
            exit();
        }

        // تسجيل الدخول بنجاح
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['full_name'] = $user['first_name'] . ' ' . $user['last_name'];

        // تحديث وقت آخر دخول
        $update_query = "UPDATE users SET last_login = NOW() WHERE id = :user_id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':user_id', $user['id']);
        $update_stmt->execute();

        // تسجيل النشاط
        $log_query = "INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) 
                      VALUES (:user_id, 'login', 'تسجيل دخول ناجح', :ip, :user_agent)";
        $log_stmt = $db->prepare($log_query);
        $log_stmt->bindParam(':user_id', $user['id']);
        $log_stmt->bindParam(':ip', $_SERVER['REMOTE_ADDR']);
        $log_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT']);
        $log_stmt->execute();

        // إعداد كوكي التذكر إذا تم اختياره
        if ($remember) {
            $token = generateToken();
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true); // 30 يوم
            
            // حفظ الرمز في قاعدة البيانات (يمكن إضافة جدول منفصل للرموز)
            $token_query = "UPDATE users SET api_key = :token WHERE id = :user_id";
            $token_stmt = $db->prepare($token_query);
            $token_stmt->bindParam(':token', $token);
            $token_stmt->bindParam(':user_id', $user['id']);
            $token_stmt->execute();
        }

        // إعادة التوجيه حسب نوع المستخدم
        if ($user['role'] === 'admin') {
            header('Location: ../admin/dashboard.php');
        } else {
            header('Location: ../index.php');
        }
        exit();

    } else {
        // فشل في تسجيل الدخول
        $_SESSION['error'] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        
        // تسجيل محاولة دخول فاشلة
        if ($user) {
            $log_query = "INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) 
                          VALUES (:user_id, 'login_failed', 'محاولة دخول فاشلة', :ip, :user_agent)";
            $log_stmt = $db->prepare($log_query);
            $log_stmt->bindParam(':user_id', $user['id']);
            $log_stmt->bindParam(':ip', $_SERVER['REMOTE_ADDR']);
            $log_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT']);
            $log_stmt->execute();
        }
        
        header('Location: login.php');
        exit();
    }

} catch (PDOException $e) {
    logError("خطأ في تسجيل الدخول: " . $e->getMessage());
    $_SESSION['error'] = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى';
    header('Location: login.php');
    exit();
}
?>
