<?php
/**
 * فئة إدارة حماية IP للعروض
 */
class IPProtection {
    private $db;
    private $settings;
    
    public function __construct($database) {
        $this->db = $database->getConnection();
        $this->loadSettings();
    }
    
    /**
     * تحميل إعدادات حماية IP
     */
    private function loadSettings() {
        $query = "SELECT setting_key, setting_value FROM settings 
                  WHERE setting_key LIKE 'ip_protection_%'";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $this->settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->settings[$row['setting_key']] = $row['setting_value'];
        }
        
        // القيم الافتراضية
        $defaults = [
            'ip_protection_enabled' => '1',
            'ip_protection_days' => '7',
            'ip_protection_per_offer' => '1',
            'ip_protection_global' => '0',
            'ip_protection_whitelist' => '',
            'ip_protection_reset_hour' => '0'
        ];
        
        foreach ($defaults as $key => $value) {
            if (!isset($this->settings[$key])) {
                $this->settings[$key] = $value;
            }
        }
    }
    
    /**
     * التحقق من تفعيل حماية IP
     */
    public function isEnabled() {
        return $this->settings['ip_protection_enabled'] === '1';
    }
    
    /**
     * التحقق من IP في القائمة البيضاء
     */
    public function isWhitelisted($ip) {
        if (empty($this->settings['ip_protection_whitelist'])) {
            return false;
        }
        
        $whitelist = array_map('trim', explode(',', $this->settings['ip_protection_whitelist']));
        return in_array($ip, $whitelist);
    }
    
    /**
     * التحقق من حماية IP لعرض معين
     */
    public function isBlocked($ip, $offer_id = null, $user_id = null) {
        if (!$this->isEnabled() || $this->isWhitelisted($ip)) {
            return false;
        }
        
        $current_time = date('Y-m-d H:i:s');
        
        // التحقق من الحماية العامة
        if ($this->settings['ip_protection_global'] === '1') {
            $global_query = "SELECT id FROM ip_protection 
                            WHERE ip_address = :ip AND protection_type = 'global' 
                            AND blocked_until > :current_time";
            $global_stmt = $this->db->prepare($global_query);
            $global_stmt->bindParam(':ip', $ip);
            $global_stmt->bindParam(':current_time', $current_time);
            $global_stmt->execute();
            
            if ($global_stmt->rowCount() > 0) {
                return true;
            }
        }
        
        // التحقق من حماية العرض المحدد
        if ($offer_id && $this->settings['ip_protection_per_offer'] === '1') {
            $offer_query = "SELECT id, blocked_until FROM ip_protection 
                           WHERE ip_address = :ip AND offer_id = :offer_id 
                           AND protection_type = 'offer' AND blocked_until > :current_time";
            $offer_stmt = $this->db->prepare($offer_query);
            $offer_stmt->bindParam(':ip', $ip);
            $offer_stmt->bindParam(':offer_id', $offer_id);
            $offer_stmt->bindParam(':current_time', $current_time);
            $offer_stmt->execute();
            
            if ($offer_stmt->rowCount() > 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * إضافة حماية IP بعد النقر على العرض
     */
    public function addProtection($ip, $offer_id = null, $user_id = null) {
        if (!$this->isEnabled() || $this->isWhitelisted($ip)) {
            return false;
        }
        
        $protection_days = intval($this->settings['ip_protection_days']);
        $blocked_until = date('Y-m-d H:i:s', strtotime("+{$protection_days} days"));
        
        try {
            // إضافة حماية للعرض المحدد
            if ($offer_id && $this->settings['ip_protection_per_offer'] === '1') {
                $this->insertOrUpdateProtection($ip, $offer_id, $user_id, 'offer', $blocked_until);
            }
            
            // إضافة حماية عامة
            if ($this->settings['ip_protection_global'] === '1') {
                $this->insertOrUpdateProtection($ip, null, $user_id, 'global', $blocked_until);
            }
            
            return true;
            
        } catch (PDOException $e) {
            logError("خطأ في إضافة حماية IP: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إدراج أو تحديث حماية IP
     */
    private function insertOrUpdateProtection($ip, $offer_id, $user_id, $type, $blocked_until) {
        // التحقق من وجود سجل موجود
        $check_query = "SELECT id, click_count FROM ip_protection 
                        WHERE ip_address = :ip AND protection_type = :type";
        
        if ($type === 'offer') {
            $check_query .= " AND offer_id = :offer_id";
        } else {
            $check_query .= " AND offer_id IS NULL";
        }
        
        $check_stmt = $this->db->prepare($check_query);
        $check_stmt->bindParam(':ip', $ip);
        $check_stmt->bindParam(':type', $type);
        if ($type === 'offer') {
            $check_stmt->bindParam(':offer_id', $offer_id);
        }
        $check_stmt->execute();
        
        if ($existing = $check_stmt->fetch(PDO::FETCH_ASSOC)) {
            // تحديث السجل الموجود
            $update_query = "UPDATE ip_protection 
                            SET blocked_until = :blocked_until, 
                                click_count = click_count + 1,
                                last_click = CURRENT_TIMESTAMP,
                                user_id = :user_id
                            WHERE id = :id";
            
            $update_stmt = $this->db->prepare($update_query);
            $update_stmt->bindParam(':blocked_until', $blocked_until);
            $update_stmt->bindParam(':user_id', $user_id);
            $update_stmt->bindParam(':id', $existing['id']);
            $update_stmt->execute();
            
        } else {
            // إنشاء سجل جديد
            $insert_query = "INSERT INTO ip_protection 
                            (ip_address, offer_id, user_id, protection_type, blocked_until) 
                            VALUES (:ip, :offer_id, :user_id, :type, :blocked_until)";
            
            $insert_stmt = $this->db->prepare($insert_query);
            $insert_stmt->bindParam(':ip', $ip);
            $insert_stmt->bindParam(':offer_id', $offer_id);
            $insert_stmt->bindParam(':user_id', $user_id);
            $insert_stmt->bindParam(':type', $type);
            $insert_stmt->bindParam(':blocked_until', $blocked_until);
            $insert_stmt->execute();
        }
    }
    
    /**
     * الحصول على معلومات الحماية لـ IP معين
     */
    public function getProtectionInfo($ip, $offer_id = null) {
        $info = [
            'is_blocked' => false,
            'blocked_until' => null,
            'remaining_time' => 0,
            'click_count' => 0,
            'protection_type' => null
        ];
        
        if (!$this->isEnabled() || $this->isWhitelisted($ip)) {
            return $info;
        }
        
        $current_time = date('Y-m-d H:i:s');
        $query = "SELECT protection_type, blocked_until, click_count, last_click 
                  FROM ip_protection 
                  WHERE ip_address = :ip AND blocked_until > :current_time";
        
        if ($offer_id) {
            $query .= " AND (offer_id = :offer_id OR protection_type = 'global')";
        }
        
        $query .= " ORDER BY blocked_until DESC LIMIT 1";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':ip', $ip);
        $stmt->bindParam(':current_time', $current_time);
        if ($offer_id) {
            $stmt->bindParam(':offer_id', $offer_id);
        }
        $stmt->execute();
        
        if ($protection = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $info['is_blocked'] = true;
            $info['blocked_until'] = $protection['blocked_until'];
            $info['remaining_time'] = strtotime($protection['blocked_until']) - time();
            $info['click_count'] = $protection['click_count'];
            $info['protection_type'] = $protection['protection_type'];
        }
        
        return $info;
    }
    
    /**
     * تنظيف السجلات المنتهية الصلاحية
     */
    public function cleanExpiredProtections() {
        try {
            $current_time = date('Y-m-d H:i:s');
            $delete_query = "DELETE FROM ip_protection WHERE blocked_until <= :current_time";
            $delete_stmt = $this->db->prepare($delete_query);
            $delete_stmt->bindParam(':current_time', $current_time);
            $delete_stmt->execute();
            
            return $delete_stmt->rowCount();
            
        } catch (PDOException $e) {
            logError("خطأ في تنظيف حماية IP: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * إعادة تعيين حماية IP لجميع العروض
     */
    public function resetAllProtections() {
        try {
            $delete_query = "DELETE FROM ip_protection";
            $delete_stmt = $this->db->prepare($delete_query);
            $delete_stmt->execute();
            
            return $delete_stmt->rowCount();
            
        } catch (PDOException $e) {
            logError("خطأ في إعادة تعيين حماية IP: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * إعادة تعيين حماية IP لعرض معين
     */
    public function resetOfferProtection($offer_id) {
        try {
            $delete_query = "DELETE FROM ip_protection WHERE offer_id = :offer_id";
            $delete_stmt = $this->db->prepare($delete_query);
            $delete_stmt->bindParam(':offer_id', $offer_id);
            $delete_stmt->execute();
            
            return $delete_stmt->rowCount();
            
        } catch (PDOException $e) {
            logError("خطأ في إعادة تعيين حماية العرض: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * الحصول على إحصائيات حماية IP
     */
    public function getProtectionStats() {
        try {
            $current_time = date('Y-m-d H:i:s');
            
            // إجمالي IPs المحمية حالياً
            $active_query = "SELECT COUNT(DISTINCT ip_address) as active_ips,
                            COUNT(*) as total_protections
                            FROM ip_protection 
                            WHERE blocked_until > :current_time";
            $active_stmt = $this->db->prepare($active_query);
            $active_stmt->bindParam(':current_time', $current_time);
            $active_stmt->execute();
            $active_stats = $active_stmt->fetch(PDO::FETCH_ASSOC);
            
            // إحصائيات حسب النوع
            $type_query = "SELECT protection_type, COUNT(*) as count
                          FROM ip_protection 
                          WHERE blocked_until > :current_time
                          GROUP BY protection_type";
            $type_stmt = $this->db->prepare($type_query);
            $type_stmt->bindParam(':current_time', $current_time);
            $type_stmt->execute();
            $type_stats = $type_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'active_ips' => $active_stats['active_ips'] ?? 0,
                'total_protections' => $active_stats['total_protections'] ?? 0,
                'by_type' => $type_stats,
                'settings' => $this->settings
            ];
            
        } catch (PDOException $e) {
            logError("خطأ في إحصائيات حماية IP: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * تحديث إعدادات حماية IP
     */
    public function updateSettings($new_settings) {
        try {
            foreach ($new_settings as $key => $value) {
                if (strpos($key, 'ip_protection_') === 0) {
                    $update_query = "UPDATE settings SET setting_value = :value 
                                    WHERE setting_key = :key";
                    $update_stmt = $this->db->prepare($update_query);
                    $update_stmt->bindParam(':value', $value);
                    $update_stmt->bindParam(':key', $key);
                    $update_stmt->execute();
                }
            }
            
            // إعادة تحميل الإعدادات
            $this->loadSettings();
            return true;
            
        } catch (PDOException $e) {
            logError("خطأ في تحديث إعدادات حماية IP: " . $e->getMessage());
            return false;
        }
    }
}
?>
