<?php
require_once '../config/config.php';

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة', 'success' => false]);
    exit();
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح', 'success' => false]);
    exit();
}

try {
    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        http_response_code(400);
        echo json_encode(['error' => 'بيانات غير صحيحة', 'success' => false]);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    // استخراج البيانات
    $event = sanitize($data['event'] ?? '');
    $subid = sanitize($data['subid'] ?? '');
    $timestamp = sanitize($data['timestamp'] ?? date('Y-m-d H:i:s'));
    $event_data = json_encode($data['data'] ?? []);
    
    // التحقق من البيانات المطلوبة
    if (empty($event)) {
        http_response_code(400);
        echo json_encode(['error' => 'اسم الحدث مطلوب', 'success' => false]);
        exit();
    }
    
    // تسجيل الحدث في قاعدة البيانات
    $insert_query = "INSERT INTO offerwall_events (user_id, event_name, subid, event_data, ip_address, user_agent, created_at) 
                     VALUES (:user_id, :event_name, :subid, :event_data, :ip_address, :user_agent, :created_at)";
    
    $insert_stmt = $db->prepare($insert_query);
    $insert_stmt->bindParam(':user_id', $user_id);
    $insert_stmt->bindParam(':event_name', $event);
    $insert_stmt->bindParam(':subid', $subid);
    $insert_stmt->bindParam(':event_data', $event_data);
    $insert_stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
    $insert_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
    $insert_stmt->bindParam(':created_at', $timestamp);
    
    if ($insert_stmt->execute()) {
        $event_id = $db->lastInsertId();
        
        // معالجة أحداث خاصة
        switch ($event) {
            case 'offer_completed':
                handleOfferCompleted($db, $user_id, $data['data'] ?? []);
                break;
            case 'popup_opened':
            case 'iframe_loaded':
                updateOfferwallStats($db, $user_id, 'view');
                break;
        }
        
        echo json_encode([
            'success' => true,
            'event_id' => $event_id,
            'message' => 'تم تسجيل الحدث بنجاح'
        ]);
        
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في تسجيل الحدث', 'success' => false]);
    }
    
} catch (PDOException $e) {
    logError("خطأ في تتبع أحداث Offerwall: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في النظام', 'success' => false]);
} catch (Exception $e) {
    logError("خطأ عام في تتبع الأحداث: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في النظام', 'success' => false]);
}

/**
 * معالجة إكمال العرض
 */
function handleOfferCompleted($db, $user_id, $data) {
    try {
        // تسجيل في سجل النشاطات
        $activity_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                          VALUES (:user_id, 'offerwall_completion', 'إكمال عرض من Offerwall', :data)";
        
        $activity_stmt = $db->prepare($activity_query);
        $activity_stmt->bindParam(':user_id', $user_id);
        $activity_stmt->bindParam(':data', json_encode($data));
        $activity_stmt->execute();
        
        // تحديث إحصائيات Offerwall
        updateOfferwallStats($db, $user_id, 'completion');
        
    } catch (PDOException $e) {
        logError("خطأ في معالجة إكمال العرض: " . $e->getMessage());
    }
}

/**
 * تحديث إحصائيات Offerwall
 */
function updateOfferwallStats($db, $user_id, $type) {
    try {
        $today = date('Y-m-d');
        
        // التحقق من وجود سجل لليوم
        $check_query = "SELECT id FROM offerwall_stats 
                        WHERE user_id = :user_id AND date = :date";
        
        $check_stmt = $db->prepare($check_query);
        $check_stmt->bindParam(':user_id', $user_id);
        $check_stmt->bindParam(':date', $today);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() > 0) {
            // تحديث السجل الموجود
            $column = $type === 'completion' ? 'completions' : 'views';
            $update_query = "UPDATE offerwall_stats 
                            SET $column = $column + 1, updated_at = NOW()
                            WHERE user_id = :user_id AND date = :date";
            
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(':user_id', $user_id);
            $update_stmt->bindParam(':date', $today);
            $update_stmt->execute();
        } else {
            // إنشاء سجل جديد
            $views = $type === 'view' ? 1 : 0;
            $completions = $type === 'completion' ? 1 : 0;
            
            $insert_query = "INSERT INTO offerwall_stats (user_id, date, views, completions) 
                            VALUES (:user_id, :date, :views, :completions)";
            
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':user_id', $user_id);
            $insert_stmt->bindParam(':date', $today);
            $insert_stmt->bindParam(':views', $views);
            $insert_stmt->bindParam(':completions', $completions);
            $insert_stmt->execute();
        }
        
    } catch (PDOException $e) {
        logError("خطأ في تحديث إحصائيات Offerwall: " . $e->getMessage());
    }
}
?>
