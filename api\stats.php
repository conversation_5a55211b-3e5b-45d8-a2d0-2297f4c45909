<?php
require_once '../config/config.php';

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من تسجيل الدخول أو API key
$authenticated = false;
$user_id = null;

if (isLoggedIn()) {
    $authenticated = true;
    $user_id = $_SESSION['user_id'];
} else {
    // التحقق من API key
    $api_key = $_GET['api_key'] ?? $_POST['api_key'] ?? '';
    if (!empty($api_key)) {
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            $user_query = "SELECT id FROM users WHERE api_key = :api_key AND status = 'active'";
            $user_stmt = $db->prepare($user_query);
            $user_stmt->bindParam(':api_key', $api_key);
            $user_stmt->execute();
            
            $user = $user_stmt->fetch(PDO::FETCH_ASSOC);
            if ($user) {
                $authenticated = true;
                $user_id = $user['id'];
            }
        } catch (PDOException $e) {
            // تجاهل الخطأ
        }
    }
}

if (!$authenticated) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح', 'success' => false]);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // معالجة المعاملات
    $date_from = sanitize($_GET['date_from'] ?? date('Y-m-01')); // بداية الشهر الحالي
    $date_to = sanitize($_GET['date_to'] ?? date('Y-m-d')); // اليوم الحالي
    $offer_id = intval($_GET['offer_id'] ?? 0);
    $group_by = sanitize($_GET['group_by'] ?? 'day'); // day, week, month

    // بناء الاستعلام الأساسي
    $where_conditions = ["ds.user_id = :user_id", "ds.date BETWEEN :date_from AND :date_to"];
    $params = [
        ':user_id' => $user_id,
        ':date_from' => $date_from,
        ':date_to' => $date_to
    ];

    if ($offer_id > 0) {
        $where_conditions[] = "ds.offer_id = :offer_id";
        $params[':offer_id'] = $offer_id;
    }

    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

    // تحديد التجميع حسب الفترة
    $date_format = match($group_by) {
        'week' => "DATE_FORMAT(ds.date, '%Y-%u')",
        'month' => "DATE_FORMAT(ds.date, '%Y-%m')",
        default => "ds.date"
    };

    // الإحصائيات الإجمالية
    $total_query = "SELECT 
        COALESCE(SUM(ds.clicks), 0) as total_clicks,
        COALESCE(SUM(ds.unique_clicks), 0) as total_unique_clicks,
        COALESCE(SUM(ds.conversions), 0) as total_conversions,
        COALESCE(SUM(ds.earnings), 0) as total_earnings,
        CASE 
            WHEN SUM(ds.clicks) > 0 THEN ROUND((SUM(ds.conversions) / SUM(ds.clicks)) * 100, 2)
            ELSE 0 
        END as conversion_rate,
        CASE 
            WHEN SUM(ds.conversions) > 0 THEN ROUND(SUM(ds.earnings) / SUM(ds.conversions), 2)
            ELSE 0 
        END as epc
        FROM daily_stats ds 
        $where_clause";

    $total_stmt = $db->prepare($total_query);
    foreach ($params as $key => $value) {
        $total_stmt->bindValue($key, $value);
    }
    $total_stmt->execute();
    $totals = $total_stmt->fetch(PDO::FETCH_ASSOC);

    // الإحصائيات المجمعة حسب الفترة
    $grouped_query = "SELECT 
        $date_format as period,
        COALESCE(SUM(ds.clicks), 0) as clicks,
        COALESCE(SUM(ds.unique_clicks), 0) as unique_clicks,
        COALESCE(SUM(ds.conversions), 0) as conversions,
        COALESCE(SUM(ds.earnings), 0) as earnings
        FROM daily_stats ds 
        $where_clause
        GROUP BY $date_format
        ORDER BY period";

    $grouped_stmt = $db->prepare($grouped_query);
    foreach ($params as $key => $value) {
        $grouped_stmt->bindValue($key, $value);
    }
    $grouped_stmt->execute();
    $grouped_data = $grouped_stmt->fetchAll(PDO::FETCH_ASSOC);

    // أفضل العروض
    $top_offers_query = "SELECT 
        o.id, o.title, n.name as network_name,
        COALESCE(SUM(ds.clicks), 0) as clicks,
        COALESCE(SUM(ds.conversions), 0) as conversions,
        COALESCE(SUM(ds.earnings), 0) as earnings
        FROM daily_stats ds
        JOIN offers o ON ds.offer_id = o.id
        JOIN networks n ON o.network_id = n.id
        $where_clause
        GROUP BY ds.offer_id
        ORDER BY earnings DESC
        LIMIT 10";

    $top_offers_stmt = $db->prepare($top_offers_query);
    foreach ($params as $key => $value) {
        $top_offers_stmt->bindValue($key, $value);
    }
    $top_offers_stmt->execute();
    $top_offers = $top_offers_stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات النقرات الأخيرة
    $recent_clicks_query = "SELECT 
        c.id, c.ip_address, c.country, c.device_type, c.browser, c.clicked_at,
        o.title as offer_title, n.name as network_name
        FROM clicks c
        JOIN tracking_links tl ON c.tracking_id = tl.tracking_id
        JOIN offers o ON tl.offer_id = o.id
        JOIN networks n ON o.network_id = n.id
        WHERE c.user_id = :user_id
        ORDER BY c.clicked_at DESC
        LIMIT 20";

    $recent_clicks_stmt = $db->prepare($recent_clicks_query);
    $recent_clicks_stmt->bindParam(':user_id', $user_id);
    $recent_clicks_stmt->execute();
    $recent_clicks = $recent_clicks_stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات التحويلات الأخيرة
    $recent_conversions_query = "SELECT 
        conv.id, conv.payout, conv.status, conv.converted_at,
        o.title as offer_title, n.name as network_name
        FROM conversions conv
        JOIN offers o ON conv.offer_id = o.id
        JOIN networks n ON o.network_id = n.id
        WHERE conv.user_id = :user_id
        ORDER BY conv.converted_at DESC
        LIMIT 10";

    $recent_conversions_stmt = $db->prepare($recent_conversions_query);
    $recent_conversions_stmt->bindParam(':user_id', $user_id);
    $recent_conversions_stmt->execute();
    $recent_conversions = $recent_conversions_stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات الدول
    $countries_query = "SELECT 
        c.country,
        COUNT(*) as clicks,
        COALESCE(SUM(conv.payout), 0) as earnings
        FROM clicks c
        LEFT JOIN conversions conv ON c.id = conv.click_id
        WHERE c.user_id = :user_id 
        AND c.clicked_at BETWEEN :date_from AND :date_to
        GROUP BY c.country
        ORDER BY clicks DESC
        LIMIT 10";

    $countries_stmt = $db->prepare($countries_query);
    $countries_stmt->bindParam(':user_id', $user_id);
    $countries_stmt->bindParam(':date_from', $date_from);
    $countries_stmt->bindParam(':date_to', $date_to);
    $countries_stmt->execute();
    $countries_stats = $countries_stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات الأجهزة
    $devices_query = "SELECT 
        c.device_type,
        COUNT(*) as clicks,
        COALESCE(SUM(conv.payout), 0) as earnings
        FROM clicks c
        LEFT JOIN conversions conv ON c.id = conv.click_id
        WHERE c.user_id = :user_id 
        AND c.clicked_at BETWEEN :date_from AND :date_to
        GROUP BY c.device_type
        ORDER BY clicks DESC";

    $devices_stmt = $db->prepare($devices_query);
    $devices_stmt->bindParam(':user_id', $user_id);
    $devices_stmt->bindParam(':date_from', $date_from);
    $devices_stmt->bindParam(':date_to', $date_to);
    $devices_stmt->execute();
    $devices_stats = $devices_stmt->fetchAll(PDO::FETCH_ASSOC);

    // بناء الاستجابة
    $response = [
        'success' => true,
        'data' => [
            'totals' => $totals,
            'grouped_data' => $grouped_data,
            'top_offers' => $top_offers,
            'recent_clicks' => $recent_clicks,
            'recent_conversions' => $recent_conversions,
            'countries_stats' => $countries_stats,
            'devices_stats' => $devices_stats
        ],
        'meta' => [
            'date_from' => $date_from,
            'date_to' => $date_to,
            'group_by' => $group_by,
            'offer_id' => $offer_id,
            'generated_at' => date('Y-m-d H:i:s')
        ]
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    logError("خطأ في API الإحصائيات: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في النظام'
    ]);
}
?>
