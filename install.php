<?php
/**
 * ملف التثبيت السريع لنظام CPA
 * يقوم بإنشاء قاعدة البيانات والجداول والبيانات الأساسية
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_username = 'root';
$db_password = '';
$db_name = 'cpa_system';

$error_messages = [];
$success_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_username = $_POST['db_username'] ?? 'root';
    $db_password = $_POST['db_password'] ?? '';
    $db_name = $_POST['db_name'] ?? 'cpa_system';
    
    $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
    $admin_password = $_POST['admin_password'] ?? 'admin123';
    $site_url = $_POST['site_url'] ?? 'http://localhost/cpa';
    
    try {
        // الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=$db_host", $db_username, $db_password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$db_name`");
        
        // قراءة وتنفيذ ملف SQL
        $sql_file = 'database/schema.sql';
        if (file_exists($sql_file)) {
            $sql_content = file_get_contents($sql_file);
            // إزالة USE database من الملف
            $sql_content = preg_replace('/USE\s+\w+;/', '', $sql_content);
            
            // تقسيم الاستعلامات
            $queries = explode(';', $sql_content);
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query)) {
                    $pdo->exec($query);
                }
            }
            
            $success_messages[] = 'تم إنشاء قاعدة البيانات والجداول بنجاح';
        } else {
            $error_messages[] = 'ملف schema.sql غير موجود';
        }
        
        // إنشاء حساب المدير
        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
        $admin_query = "INSERT INTO users (username, email, password, first_name, last_name, role, status) 
                       VALUES ('admin', :email, :password, 'مدير', 'النظام', 'admin', 'active')
                       ON DUPLICATE KEY UPDATE 
                       email = :email, password = :password";
        
        $admin_stmt = $pdo->prepare($admin_query);
        $admin_stmt->bindParam(':email', $admin_email);
        $admin_stmt->bindParam(':password', $hashed_password);
        $admin_stmt->execute();
        
        $success_messages[] = 'تم إنشاء حساب المدير بنجاح';
        
        // تحديث ملف الإعدادات
        $config_content = "<?php
/**
 * إعدادات قاعدة البيانات
 */

class Database {
    private \$host = '$db_host';
    private \$db_name = '$db_name';
    private \$username = '$db_username';
    private \$password = '$db_password';
    private \$conn;

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$this->conn = new PDO(
                \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=utf8\",
                \$this->username,
                \$this->password
            );
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException \$exception) {
            echo \"خطأ في الاتصال: \" . \$exception->getMessage();
        }
        
        return \$this->conn;
    }
}
?>";
        
        file_put_contents('config/database.php', $config_content);
        
        // تحديث ملف config.php
        $config_file = 'config/config.php';
        if (file_exists($config_file)) {
            $config_content = file_get_contents($config_file);
            $config_content = str_replace("define('SITE_URL', 'http://localhost/cpa');", "define('SITE_URL', '$site_url');", $config_content);
            $config_content = str_replace("define('ADMIN_EMAIL', '<EMAIL>');", "define('ADMIN_EMAIL', '$admin_email');", $config_content);
            file_put_contents($config_file, $config_content);
        }
        
        $success_messages[] = 'تم تحديث ملفات الإعدادات بنجاح';
        
        // إنشاء المجلدات المطلوبة
        $directories = ['uploads', 'uploads/offers', 'uploads/networks', 'uploads/users', 'logs'];
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        $success_messages[] = 'تم إنشاء المجلدات المطلوبة بنجاح';
        $success_messages[] = 'تم التثبيت بنجاح! يمكنك الآن تسجيل الدخول';
        
    } catch (PDOException $e) {
        $error_messages[] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    } catch (Exception $e) {
        $error_messages[] = 'خطأ عام: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام CPA Marketing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .install-body {
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="install-card">
        <div class="install-header">
            <i class="fas fa-cogs fa-3x mb-3"></i>
            <h3>تثبيت نظام CPA Marketing</h3>
            <p class="mb-0">إعداد النظام للمرة الأولى</p>
        </div>
        
        <div class="install-body">
            <?php if (!empty($error_messages)): ?>
                <?php foreach ($error_messages as $error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php if (!empty($success_messages)): ?>
                <?php foreach ($success_messages as $success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endforeach; ?>
                
                <div class="text-center mt-4">
                    <a href="auth/login.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        الذهاب لتسجيل الدخول
                    </a>
                </div>
            <?php else: ?>
                <form method="POST">
                    <h5 class="mb-3">إعدادات قاعدة البيانات</h5>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" 
                                   value="<?php echo htmlspecialchars($db_host); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" 
                                   value="<?php echo htmlspecialchars($db_name); ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="db_username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="db_username" name="db_username" 
                                   value="<?php echo htmlspecialchars($db_username); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="db_password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="db_password" name="db_password" 
                                   value="<?php echo htmlspecialchars($db_password); ?>">
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <h5 class="mb-3">إعدادات المدير</h5>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="admin_email" class="form-label">بريد المدير الإلكتروني</label>
                            <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                   value="<EMAIL>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="admin_password" class="form-label">كلمة مرور المدير</label>
                            <input type="password" class="form-control" id="admin_password" name="admin_password" 
                                   value="admin123" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="site_url" class="form-label">رابط الموقع</label>
                        <input type="url" class="form-control" id="site_url" name="site_url" 
                               value="http://localhost/cpa" required>
                        <small class="text-muted">الرابط الكامل للموقع بدون / في النهاية</small>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> تأكد من أن خادم MySQL يعمل وأن لديك صلاحيات إنشاء قواعد البيانات.
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-download me-2"></i>
                        بدء التثبيت
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
