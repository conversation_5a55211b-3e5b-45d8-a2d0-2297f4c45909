<?php
// تضمين كاشف الاستضافة
require_once 'hosting-detector.php';
$hosting_info = detectHostingType();

// إعدادات خاصة لـ InfinityFree
$suggestions = [
    'db_host' => 'sql303.infinityfree.com',
    'db_name' => 'if0_39395085_q12',
    'db_username' => 'if0_39395085'
];
?>

<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-database me-2"></i>الخطوة 2: إعداد قاعدة البيانات</h4>
        <small class="text-muted">تم اكتشاف: <?php echo $hosting_info['name']; ?></small>
    </div>
    <div class="card-body">
        <div class="alert alert-<?php echo $hosting_info['db_creation_allowed'] ? 'success' : 'warning'; ?>">
            <h6><i class="fas fa-<?php echo $hosting_info['db_creation_allowed'] ? 'check' : 'exclamation-triangle'; ?> me-2"></i>
                نوع الاستضافة: <?php echo $hosting_info['name']; ?>
            </h6>
            <?php if (!empty($hosting_info['instructions'])): ?>
                <ol class="mb-0">
                    <?php foreach ($hosting_info['instructions'] as $instruction): ?>
                        <li><?php echo $instruction; ?></li>
                    <?php endforeach; ?>
                </ol>
            <?php endif; ?>
        </div>

        <p class="text-muted">يرجى إدخال بيانات الاتصال بقاعدة البيانات</p>
        
        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">
                            <i class="fas fa-server me-1"></i>عنوان الخادم
                        </label>
                        <input type="text" class="form-control" id="db_host" name="db_host"
                               value="<?php echo $_POST['db_host'] ?? $suggestions['db_host']; ?>" required>
                        <div class="form-text">للاستضافة المجانية InfinityFree: sql303.infinityfree.com</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_name" class="form-label">
                            <i class="fas fa-database me-1"></i>اسم قاعدة البيانات
                        </label>
                        <input type="text" class="form-control" id="db_name" name="db_name"
                               value="<?php echo $_POST['db_name'] ?? $suggestions['db_name']; ?>" required>
                        <div class="form-text">للاستضافة المجانية: if0_39395085_q12</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_username" class="form-label">
                            <i class="fas fa-user me-1"></i>اسم المستخدم
                        </label>
                        <input type="text" class="form-control" id="db_username" name="db_username"
                               value="<?php echo $_POST['db_username'] ?? $suggestions['db_username']; ?>" required>
                        <div class="form-text">للاستضافة المجانية: if0_39395085</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_password" class="form-label">
                            <i class="fas fa-lock me-1"></i>كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="db_password" name="db_password" 
                               value="<?php echo $_POST['db_password'] ?? ''; ?>">
                        <div class="form-text">كلمة مرور قاعدة البيانات (اتركها فارغة إذا لم تكن موجودة)</div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                <ul class="mb-0">
                    <li><strong>للاستضافة المجانية (InfinityFree, 000webhost, etc):</strong> يجب إنشاء قاعدة البيانات من cPanel أولاً</li>
                    <li><strong>اسم قاعدة البيانات:</strong> عادة ما يكون بصيغة <code>username_dbname</code></li>
                    <li><strong>اسم المستخدم:</strong> عادة ما يكون بصيغة <code>username_dbuser</code></li>
                    <li>تأكد من صحة بيانات الاتصال قبل المتابعة</li>
                    <li>سيتم إنشاء الجداول تلقائياً</li>
                </ul>
            </div>

            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>مهم جداً - للاستضافة المجانية InfinityFree:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>البيانات الصحيحة لحسابك:</strong></p>
                        <div class="bg-white p-2 rounded border">
                            <code>Host: sql303.infinityfree.com</code><br>
                            <code>Username: if0_39395085</code><br>
                            <code>Database: if0_39395085_q12</code><br>
                            <code>Password: Qweeee12</code>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <p><strong>خطوات التحقق:</strong></p>
                        <ol class="small mb-0">
                            <li>تأكد من وجود قاعدة البيانات <code>if0_39395085_q12</code> في cPanel</li>
                            <li>تأكد من ربط المستخدم <code>if0_39395085</code> بها</li>
                            <li>تأكد من الصلاحيات الكاملة (ALL PRIVILEGES)</li>
                            <li>استخدم الأسماء الكاملة بالضبط كما هو مكتوب</li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- اختبار الاتصال -->
            <div class="mb-3 text-center">
                <button type="button" class="btn btn-success me-2" onclick="fillInfinityFreeData()">
                    <i class="fas fa-magic me-1"></i>ملء بيانات InfinityFree
                </button>
                <button type="button" class="btn btn-outline-info me-2" onclick="testConnection()">
                    <i class="fas fa-plug me-1"></i>اختبار الاتصال
                </button>
                <a href="hosting-detector.php" target="_blank" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-info-circle me-1"></i>معلومات الاستضافة
                </a>
                <a href="test-database.php" target="_blank" class="btn btn-outline-warning">
                    <i class="fas fa-flask me-1"></i>اختبار شامل
                </a>
                <div id="connection-result" class="mt-2"></div>
            </div>
            
            <div class="text-end">
                <a href="installer.php?step=1" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i>السابق
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-1"></i>المتابعة
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function fillInfinityFreeData() {
    document.getElementById('db_host').value = 'sql303.infinityfree.com';
    document.getElementById('db_name').value = 'if0_39395085_q12';
    document.getElementById('db_username').value = 'if0_39395085';
    document.getElementById('db_password').value = 'Qweeee12';

    // إظهار رسالة تأكيد
    const resultDiv = document.getElementById('connection-result');
    resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>تم ملء البيانات بنجاح! يمكنك الآن اختبار الاتصال.</div>';
}

function testConnection() {
    const resultDiv = document.getElementById('connection-result');
    const formData = new FormData();

    formData.append('action', 'test_connection');
    formData.append('db_host', document.getElementById('db_host').value);
    formData.append('db_username', document.getElementById('db_username').value);
    formData.append('db_password', document.getElementById('db_password').value);
    formData.append('db_name', document.getElementById('db_name').value);

    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري اختبار الاتصال...</div>';

    fetch('installer.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('success')) {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>تم الاتصال بنجاح!</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>فشل في الاتصال. تحقق من البيانات.</div>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الاتصال: ' + error.message + '</div>';
    });
}
</script>
