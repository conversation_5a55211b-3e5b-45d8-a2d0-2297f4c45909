<?php
// تضمين كاشف الاستضافة
require_once 'hosting-detector.php';
$hosting_info = detectHostingType();
$suggestions = generateDatabaseSuggestions();
?>

<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-database me-2"></i>الخطوة 2: إعداد قاعدة البيانات</h4>
        <small class="text-muted">تم اكتشاف: <?php echo $hosting_info['name']; ?></small>
    </div>
    <div class="card-body">
        <div class="alert alert-<?php echo $hosting_info['db_creation_allowed'] ? 'success' : 'warning'; ?>">
            <h6><i class="fas fa-<?php echo $hosting_info['db_creation_allowed'] ? 'check' : 'exclamation-triangle'; ?> me-2"></i>
                نوع الاستضافة: <?php echo $hosting_info['name']; ?>
            </h6>
            <?php if (!empty($hosting_info['instructions'])): ?>
                <ol class="mb-0">
                    <?php foreach ($hosting_info['instructions'] as $instruction): ?>
                        <li><?php echo $instruction; ?></li>
                    <?php endforeach; ?>
                </ol>
            <?php endif; ?>
        </div>

        <p class="text-muted">يرجى إدخال بيانات الاتصال بقاعدة البيانات</p>
        
        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">
                            <i class="fas fa-server me-1"></i>عنوان الخادم
                        </label>
                        <input type="text" class="form-control" id="db_host" name="db_host"
                               value="<?php echo $_POST['db_host'] ?? $suggestions['db_host']; ?>" required>
                        <div class="form-text">عادة ما يكون localhost</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_name" class="form-label">
                            <i class="fas fa-database me-1"></i>اسم قاعدة البيانات
                        </label>
                        <input type="text" class="form-control" id="db_name" name="db_name"
                               value="<?php echo $_POST['db_name'] ?? $suggestions['db_name']; ?>" required>
                        <div class="form-text">اسم قاعدة البيانات التي تم إنشاؤها</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_username" class="form-label">
                            <i class="fas fa-user me-1"></i>اسم المستخدم
                        </label>
                        <input type="text" class="form-control" id="db_username" name="db_username"
                               value="<?php echo $_POST['db_username'] ?? $suggestions['db_username']; ?>" required>
                        <div class="form-text">اسم مستخدم قاعدة البيانات</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_password" class="form-label">
                            <i class="fas fa-lock me-1"></i>كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="db_password" name="db_password" 
                               value="<?php echo $_POST['db_password'] ?? ''; ?>">
                        <div class="form-text">كلمة مرور قاعدة البيانات (اتركها فارغة إذا لم تكن موجودة)</div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                <ul class="mb-0">
                    <li><strong>للاستضافة المجانية (InfinityFree, 000webhost, etc):</strong> يجب إنشاء قاعدة البيانات من cPanel أولاً</li>
                    <li><strong>اسم قاعدة البيانات:</strong> عادة ما يكون بصيغة <code>username_dbname</code></li>
                    <li><strong>اسم المستخدم:</strong> عادة ما يكون بصيغة <code>username_dbuser</code></li>
                    <li>تأكد من صحة بيانات الاتصال قبل المتابعة</li>
                    <li>سيتم إنشاء الجداول تلقائياً</li>
                </ul>
            </div>

            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>للاستضافة المجانية:</h6>
                <p class="mb-2">إذا كنت تستخدم استضافة مجانية مثل InfinityFree:</p>
                <ol class="mb-0">
                    <li>اذهب إلى cPanel → MySQL Databases</li>
                    <li>أنشئ قاعدة بيانات جديدة (مثل: <code>cpa_system</code>)</li>
                    <li>أنشئ مستخدم قاعدة بيانات</li>
                    <li>اربط المستخدم بقاعدة البيانات مع صلاحيات كاملة</li>
                    <li>استخدم الأسماء الكاملة هنا (مثل: <code>if0_39395085_cpa_system</code>)</li>
                </ol>
            </div>
            
            <!-- اختبار الاتصال -->
            <div class="mb-3">
                <button type="button" class="btn btn-outline-info me-2" onclick="testConnection()">
                    <i class="fas fa-plug me-1"></i>اختبار الاتصال
                </button>
                <a href="hosting-detector.php" target="_blank" class="btn btn-outline-secondary">
                    <i class="fas fa-info-circle me-1"></i>معلومات الاستضافة
                </a>
                <div id="connection-result" class="mt-2"></div>
            </div>
            
            <div class="text-end">
                <a href="installer.php?step=1" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i>السابق
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-1"></i>المتابعة
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function testConnection() {
    const resultDiv = document.getElementById('connection-result');
    const formData = new FormData();
    
    formData.append('action', 'test_connection');
    formData.append('db_host', document.getElementById('db_host').value);
    formData.append('db_username', document.getElementById('db_username').value);
    formData.append('db_password', document.getElementById('db_password').value);
    formData.append('db_name', document.getElementById('db_name').value);
    
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري اختبار الاتصال...</div>';
    
    fetch('installer.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('success')) {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>تم الاتصال بنجاح!</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>فشل في الاتصال. تحقق من البيانات.</div>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الاتصال: ' + error.message + '</div>';
    });
}
</script>
