<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-database me-2"></i>الخطوة 2: إعداد قاعدة البيانات</h4>
    </div>
    <div class="card-body">
        <p class="text-muted">يرجى إدخال بيانات الاتصال بقاعدة البيانات</p>
        
        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">
                            <i class="fas fa-server me-1"></i>عنوان الخادم
                        </label>
                        <input type="text" class="form-control" id="db_host" name="db_host" 
                               value="<?php echo $_POST['db_host'] ?? 'localhost'; ?>" required>
                        <div class="form-text">عادة ما يكون localhost</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_name" class="form-label">
                            <i class="fas fa-database me-1"></i>اسم قاعدة البيانات
                        </label>
                        <input type="text" class="form-control" id="db_name" name="db_name" 
                               value="<?php echo $_POST['db_name'] ?? 'cpa_system'; ?>" required>
                        <div class="form-text">اسم قاعدة البيانات التي تم إنشاؤها</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_username" class="form-label">
                            <i class="fas fa-user me-1"></i>اسم المستخدم
                        </label>
                        <input type="text" class="form-control" id="db_username" name="db_username" 
                               value="<?php echo $_POST['db_username'] ?? 'root'; ?>" required>
                        <div class="form-text">اسم مستخدم قاعدة البيانات</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_password" class="form-label">
                            <i class="fas fa-lock me-1"></i>كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="db_password" name="db_password" 
                               value="<?php echo $_POST['db_password'] ?? ''; ?>">
                        <div class="form-text">كلمة مرور قاعدة البيانات (اتركها فارغة إذا لم تكن موجودة)</div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                <ul class="mb-0">
                    <li>تأكد من إنشاء قاعدة البيانات مسبقاً في cPanel أو phpMyAdmin</li>
                    <li>تأكد من صحة بيانات الاتصال قبل المتابعة</li>
                    <li>سيتم إنشاء الجداول تلقائياً</li>
                    <li>تأكد من أن المستخدم له صلاحيات كاملة على قاعدة البيانات</li>
                </ul>
            </div>
            
            <!-- اختبار الاتصال -->
            <div class="mb-3">
                <button type="button" class="btn btn-outline-info" onclick="testConnection()">
                    <i class="fas fa-plug me-1"></i>اختبار الاتصال
                </button>
                <div id="connection-result" class="mt-2"></div>
            </div>
            
            <div class="text-end">
                <a href="installer.php?step=1" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i>السابق
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-1"></i>المتابعة
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function testConnection() {
    const resultDiv = document.getElementById('connection-result');
    const formData = new FormData();
    
    formData.append('action', 'test_connection');
    formData.append('db_host', document.getElementById('db_host').value);
    formData.append('db_username', document.getElementById('db_username').value);
    formData.append('db_password', document.getElementById('db_password').value);
    formData.append('db_name', document.getElementById('db_name').value);
    
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري اختبار الاتصال...</div>';
    
    fetch('installer.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('success')) {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>تم الاتصال بنجاح!</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>فشل في الاتصال. تحقق من البيانات.</div>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الاتصال: ' + error.message + '</div>';
    });
}
</script>
