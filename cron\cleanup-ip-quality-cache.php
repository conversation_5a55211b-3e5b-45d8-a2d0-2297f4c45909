<?php
/**
 * مهمة تنظيف كاش جودة IP المنتهي الصلاحية
 * يجب تشغيلها يومياً عبر Cron Job
 * 
 * إضافة إلى Cron:
 * 0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup-ip-quality-cache.php
 */

// منع الوصول المباشر من المتصفح
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    die('Access denied. This script can only be run from command line.');
}

require_once __DIR__ . '/../config/config.php';

try {
    $database = new Database();
    $ipQuality = new IPQuality($database);
    
    echo "[" . date('Y-m-d H:i:s') . "] بدء تنظيف كاش جودة IP...\n";
    
    // تنظيف الكاش المنتهي الصلاحية
    $cleaned_count = $ipQuality->cleanExpiredCache();
    
    echo "[" . date('Y-m-d H:i:s') . "] تم حذف {$cleaned_count} سجل منتهي الصلاحية\n";
    
    // الحصول على إحصائيات بعد التنظيف
    $stats = $ipQuality->getCacheStats();
    $total_cached = $stats['total_cached'] ?? 0;
    $active_cached = $stats['active_cached'] ?? 0;
    $avg_score = $stats['avg_quality_score'] ?? 0;
    
    echo "[" . date('Y-m-d H:i:s') . "] الإحصائيات الحالية:\n";
    echo "  - إجمالي السجلات: {$total_cached}\n";
    echo "  - السجلات النشطة: {$active_cached}\n";
    echo "  - متوسط درجة الجودة: " . number_format($avg_score, 1) . "\n";
    
    // تسجيل النشاط في قاعدة البيانات
    if ($cleaned_count > 0) {
        $db = $database->getConnection();
        $log_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                      VALUES (NULL, 'ip_quality_cache_cleanup', 'تنظيف تلقائي لكاش جودة IP', :data)";
        
        $log_data = json_encode([
            'cleaned_records' => $cleaned_count,
            'remaining_total' => $total_cached,
            'remaining_active' => $active_cached,
            'avg_quality_score' => $avg_score,
            'cleanup_time' => date('Y-m-d H:i:s')
        ]);
        
        $log_stmt = $db->prepare($log_query);
        $log_stmt->bindParam(':data', $log_data);
        $log_stmt->execute();
    }
    
    // تحذير إذا كان الكاش كبير جداً
    if ($total_cached > 10000) {
        echo "[" . date('Y-m-d H:i:s') . "] تحذير: الكاش كبير جداً ({$total_cached} سجل)\n";
        echo "  يُنصح بتقليل مدة التخزين المؤقت أو زيادة تكرار التنظيف\n";
        
        // يمكن إضافة إشعار للمدير هنا
        // sendAdminAlert("كاش جودة IP كبير جداً: {$total_cached} سجل");
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] تم الانتهاء من التنظيف بنجاح\n";
    
} catch (Exception $e) {
    $error_message = "خطأ في تنظيف كاش جودة IP: " . $e->getMessage();
    echo "[" . date('Y-m-d H:i:s') . "] {$error_message}\n";
    
    // تسجيل الخطأ
    logError($error_message);
    
    exit(1);
}

exit(0);
?>
