<?php
/**
 * اختبار الصفحة الرئيسية
 */

echo "<h2>🏠 اختبار الصفحة الرئيسية</h2>";

// اختبار تحميل ملف التكوين
echo "<h3>1. اختبار تحميل ملف التكوين:</h3>";
try {
    require_once 'config/config.php';
    echo "✅ تم تحميل ملف التكوين بنجاح<br>";
    
    // اختبار الثوابت
    if (defined('SITE_NAME')) {
        echo "✅ SITE_NAME: " . SITE_NAME . "<br>";
    } else {
        echo "❌ SITE_NAME غير معرف<br>";
    }
    
    if (defined('CURRENCY_SYMBOL')) {
        echo "✅ CURRENCY_SYMBOL: " . CURRENCY_SYMBOL . "<br>";
    } else {
        echo "❌ CURRENCY_SYMBOL غير معرف<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في تحميل ملف التكوين: " . $e->getMessage() . "<br>";
}

// اختبار الجلسة
echo "<h3>2. اختبار الجلسة:</h3>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ الجلسة نشطة<br>";
    echo "Session ID: " . session_id() . "<br>";
    
    if (isset($_SESSION['user_id'])) {
        echo "✅ المستخدم مسجل دخول: ID = " . $_SESSION['user_id'] . "<br>";
        echo "✅ اسم المستخدم: " . ($_SESSION['username'] ?? 'غير محدد') . "<br>";
        echo "✅ الدور: " . ($_SESSION['role'] ?? 'غير محدد') . "<br>";
    } else {
        echo "⚠️ المستخدم غير مسجل دخول<br>";
    }
} else {
    echo "❌ الجلسة غير نشطة<br>";
}

// اختبار قاعدة البيانات
echo "<h3>3. اختبار قاعدة البيانات:</h3>";
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ تم الاتصال بقاعدة البيانات<br>";
    
    // اختبار الجداول المطلوبة
    $required_tables = ['users', 'offers', 'networks', 'clicks', 'conversions'];
    foreach ($required_tables as $table) {
        $check = $db->query("SHOW TABLES LIKE '$table'");
        if ($check->rowCount() > 0) {
            echo "✅ جدول $table موجود<br>";
        } else {
            echo "❌ جدول $table غير موجود<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار الملفات المطلوبة
echo "<h3>4. اختبار الملفات المطلوبة:</h3>";
$required_files = [
    'includes/header.php',
    'includes/sidebar.php',
    'includes/footer.php',
    'includes/ip-quality-widget.php',
    'assets/css/style.css',
    'assets/js/main.js'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

// اختبار الدوال المطلوبة
echo "<h3>5. اختبار الدوال المطلوبة:</h3>";
$required_functions = ['isLoggedIn', 'isAdmin'];

foreach ($required_functions as $function) {
    if (function_exists($function)) {
        echo "✅ دالة $function موجودة<br>";
    } else {
        echo "❌ دالة $function غير موجودة<br>";
    }
}

// اختبار الفئات المطلوبة
echo "<h3>6. اختبار الفئات المطلوبة:</h3>";
$required_classes = ['Database', 'IPQuality'];

foreach ($required_classes as $class) {
    if (class_exists($class)) {
        echo "✅ فئة $class موجودة<br>";
    } else {
        echo "❌ فئة $class غير موجودة<br>";
    }
}

// محاولة تشغيل جزء من كود الصفحة الرئيسية
echo "<h3>7. اختبار كود الصفحة الرئيسية:</h3>";

try {
    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
        
        // اختبار استعلام الإحصائيات
        $stats_query = "SELECT 
            COUNT(DISTINCT o.id) as total_offers,
            0 as total_clicks,
            0 as total_conversions,
            0 as total_earnings
        FROM offers o 
        WHERE o.status = 'active'";
        
        $stats_stmt = $db->prepare($stats_query);
        $stats_stmt->execute();
        $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "✅ استعلام الإحصائيات نجح<br>";
        echo "- إجمالي العروض: " . $stats['total_offers'] . "<br>";
        
        // اختبار استعلام العروض
        $offers_query = "SELECT o.id, o.title, o.payout, o.type 
        FROM offers o 
        WHERE o.status = 'active' 
        ORDER BY o.payout DESC 
        LIMIT 5";
        
        $offers_stmt = $db->prepare($offers_query);
        $offers_stmt->execute();
        $offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "✅ استعلام العروض نجح<br>";
        echo "- عدد العروض المتاحة: " . count($offers) . "<br>";
        
    } else {
        echo "⚠️ لا يمكن اختبار الاستعلامات - المستخدم غير مسجل دخول<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في كود الصفحة الرئيسية: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>🎯 الخلاصة:</h3>";

if (!isset($_SESSION['user_id'])) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
    echo "<h5>⚠️ تحتاج لتسجيل الدخول أولاً</h5>";
    echo "<p>الصفحة الرئيسية تتطلب تسجيل دخول المستخدم.</p>";
    echo "<a href='auth/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔐 تسجيل الدخول</a>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
    echo "<h5>✅ النظام جاهز</h5>";
    echo "<p>يمكنك الآن الوصول للصفحة الرئيسية.</p>";
    echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 الصفحة الرئيسية</a>";
    echo "</div>";
}

echo "<h4>🔧 أدوات الإصلاح:</h4>";
echo "<p><a href='fix-users.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin: 5px;'>👥 إصلاح المستخدمين</a>";
echo "<a href='test-login.php' style='background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 اختبار تسجيل الدخول</a>";
echo "<a href='test-database-final.php' style='background: #fd7e14; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin: 5px;'>🗄️ اختبار قاعدة البيانات</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3, h4, h5 {
    color: #333;
}
</style>
