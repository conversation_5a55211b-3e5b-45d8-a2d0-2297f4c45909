<?php
/**
 * اختبار نهائي لقاعدة البيانات
 * يتحقق من الاتصال وإنشاء الجداول
 */

// إعدادات قاعدة البيانات
$host = 'sql303.infinityfree.com';
$dbname = 'if0_39395085_q12';
$username = 'if0_39395085';
$password = 'Qweeee12';

echo "<h2>🔧 اختبار قاعدة البيانات النهائي</h2>";

try {
    // اختبار الاتصال
    echo "<h3>1. اختبار الاتصال:</h3>";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ تم الاتصال بنجاح!<br>";
    
    // اختبار إنشاء جدول بسيط
    echo "<h3>2. اختبار إنشاء جدول:</h3>";
    $pdo->exec("CREATE TABLE IF NOT EXISTS test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✅ تم إنشاء جدول الاختبار بنجاح!<br>";
    
    // اختبار إدراج بيانات
    echo "<h3>3. اختبار إدراج البيانات:</h3>";
    $stmt = $pdo->prepare("INSERT INTO test_table (name) VALUES (?)");
    $stmt->execute(['اختبار ' . date('Y-m-d H:i:s')]);
    echo "✅ تم إدراج البيانات بنجاح!<br>";
    
    // اختبار قراءة البيانات
    echo "<h3>4. اختبار قراءة البيانات:</h3>";
    $stmt = $pdo->query("SELECT * FROM test_table ORDER BY id DESC LIMIT 5");
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ تم قراءة " . count($results) . " سجل بنجاح!<br>";
    
    foreach ($results as $row) {
        echo "- ID: {$row['id']}, Name: {$row['name']}, Created: {$row['created_at']}<br>";
    }
    
    // اختبار حذف الجدول
    echo "<h3>5. تنظيف الاختبار:</h3>";
    $pdo->exec("DROP TABLE IF EXISTS test_table");
    echo "✅ تم حذف جدول الاختبار بنجاح!<br>";
    
    echo "<hr>";
    echo "<h3>🎉 جميع الاختبارات نجحت!</h3>";
    echo "<p><strong>قاعدة البيانات جاهزة للاستخدام.</strong></p>";
    echo "<p><a href='installer.php' class='btn btn-success'>متابعة التثبيت</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;'>";
    echo "<strong>رسالة الخطأ:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>رقم الخطأ:</strong> " . $e->getCode() . "<br>";
    echo "</div>";
    
    echo "<h4>🔧 خطوات الإصلاح:</h4>";
    echo "<ol>";
    echo "<li>تأكد من أن قاعدة البيانات <code>if0_39395085_q12</code> موجودة في cPanel</li>";
    echo "<li>تأكد من أن المستخدم <code>if0_39395085</code> مربوط بقاعدة البيانات</li>";
    echo "<li>تأكد من أن المستخدم لديه صلاحيات كاملة (ALL PRIVILEGES)</li>";
    echo "<li>تأكد من كلمة المرور الصحيحة: <code>Qweeee12</code></li>";
    echo "</ol>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3 {
    color: #333;
}
.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #28a745;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 10px;
}
.btn:hover {
    background: #218838;
}
code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
