<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

$errors = [];
$success = '';

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitize($_POST['action'] ?? '');
    
    switch ($action) {
        case 'update_site_settings':
            $site_name = sanitize($_POST['site_name']);
            $site_description = sanitize($_POST['site_description']);
            $site_keywords = sanitize($_POST['site_keywords']);
            $admin_email = sanitize($_POST['admin_email']);
            $currency_symbol = sanitize($_POST['currency_symbol']);
            $timezone = sanitize($_POST['timezone']);
            
            // التحقق من البيانات
            if (empty($site_name)) {
                $errors[] = 'اسم الموقع مطلوب';
            }
            
            if (empty($admin_email) || !filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'بريد المدير غير صحيح';
            }
            
            // تحديث الإعدادات في ملف config.php
            if (empty($errors)) {
                try {
                    // قراءة ملف config.php
                    $config_file = '../../config/config.php';
                    $config_content = file_get_contents($config_file);
                    
                    // تحديث القيم
                    $config_content = preg_replace("/define\('SITE_NAME', '.*?'\);/", "define('SITE_NAME', '{$site_name}');", $config_content);
                    $config_content = preg_replace("/define\('SITE_DESCRIPTION', '.*?'\);/", "define('SITE_DESCRIPTION', '{$site_description}');", $config_content);
                    $config_content = preg_replace("/define\('ADMIN_EMAIL', '.*?'\);/", "define('ADMIN_EMAIL', '{$admin_email}');", $config_content);
                    $config_content = preg_replace("/define\('CURRENCY_SYMBOL', '.*?'\);/", "define('CURRENCY_SYMBOL', '{$currency_symbol}');", $config_content);
                    
                    // حفظ الملف
                    if (file_put_contents($config_file, $config_content)) {
                        $success = 'تم تحديث إعدادات الموقع بنجاح!';
                    } else {
                        $errors[] = 'فشل في حفظ إعدادات الموقع';
                    }
                    
                } catch (Exception $e) {
                    logError("خطأ في تحديث إعدادات الموقع: " . $e->getMessage());
                    $errors[] = 'حدث خطأ في تحديث الإعدادات';
                }
            }
            break;
            
        case 'update_security_settings':
            $max_login_attempts = intval($_POST['max_login_attempts']);
            $session_timeout = intval($_POST['session_timeout']);
            $enable_2fa = isset($_POST['enable_2fa']) ? 1 : 0;
            $force_https = isset($_POST['force_https']) ? 1 : 0;
            
            // حفظ إعدادات الأمان في قاعدة البيانات
            try {
                $security_settings = [
                    'max_login_attempts' => $max_login_attempts,
                    'session_timeout' => $session_timeout,
                    'enable_2fa' => $enable_2fa,
                    'force_https' => $force_https
                ];
                
                foreach ($security_settings as $key => $value) {
                    $query = "INSERT INTO settings (setting_key, setting_value) VALUES (:key, :value) 
                             ON DUPLICATE KEY UPDATE setting_value = :value";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':key', $key);
                    $stmt->bindParam(':value', $value);
                    $stmt->execute();
                }
                
                $success = 'تم تحديث إعدادات الأمان بنجاح!';
                
            } catch (PDOException $e) {
                logError("خطأ في تحديث إعدادات الأمان: " . $e->getMessage());
                $errors[] = 'حدث خطأ في تحديث إعدادات الأمان';
            }
            break;
            
        case 'clear_cache':
            // مسح ملفات الكاش
            $cache_dir = '../../cache/';
            if (is_dir($cache_dir)) {
                $files = glob($cache_dir . '*');
                $cleared_count = 0;
                
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                        $cleared_count++;
                    }
                }
                
                $success = "تم مسح {$cleared_count} ملف من الكاش";
            } else {
                $success = 'لا توجد ملفات كاش لمسحها';
            }
            break;
            
        case 'backup_database':
            // إنشاء نسخة احتياطية من قاعدة البيانات
            try {
                $backup_file = '../../backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
                $backup_dir = dirname($backup_file);
                
                if (!is_dir($backup_dir)) {
                    mkdir($backup_dir, 0755, true);
                }
                
                // تنفيذ النسخ الاحتياطي (مبسط)
                $tables_query = "SHOW TABLES";
                $tables_stmt = $db->prepare($tables_query);
                $tables_stmt->execute();
                $tables = $tables_stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $backup_content = "-- نسخة احتياطية من قاعدة البيانات\n";
                $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
                
                foreach ($tables as $table) {
                    $backup_content .= "-- جدول: {$table}\n";
                    $backup_content .= "DROP TABLE IF EXISTS `{$table}`;\n";
                    
                    // هيكل الجدول
                    $structure_query = "SHOW CREATE TABLE `{$table}`";
                    $structure_stmt = $db->prepare($structure_query);
                    $structure_stmt->execute();
                    $structure = $structure_stmt->fetch(PDO::FETCH_ASSOC);
                    $backup_content .= $structure['Create Table'] . ";\n\n";
                }
                
                if (file_put_contents($backup_file, $backup_content)) {
                    $success = 'تم إنشاء النسخة الاحتياطية بنجاح: ' . basename($backup_file);
                } else {
                    $errors[] = 'فشل في إنشاء النسخة الاحتياطية';
                }
                
            } catch (Exception $e) {
                logError("خطأ في النسخ الاحتياطي: " . $e->getMessage());
                $errors[] = 'حدث خطأ في إنشاء النسخة الاحتياطية';
            }
            break;
    }
}

// جلب الإعدادات الحالية
$current_settings = [
    'max_login_attempts' => 5,
    'session_timeout' => 3600,
    'enable_2fa' => 0,
    'force_https' => 0
];

try {
    $settings_query = "SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('max_login_attempts', 'session_timeout', 'enable_2fa', 'force_https')";
    $settings_stmt = $db->prepare($settings_query);
    $settings_stmt->execute();
    
    while ($row = $settings_stmt->fetch(PDO::FETCH_ASSOC)) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    // استخدام القيم الافتراضية في حالة الخطأ
}

// إحصائيات النظام
$system_stats = [
    'total_users' => 0,
    'total_offers' => 0,
    'total_clicks' => 0,
    'disk_usage' => 0
];

try {
    // عدد المستخدمين
    $users_query = "SELECT COUNT(*) FROM users";
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $system_stats['total_users'] = $users_stmt->fetchColumn();
    
    // عدد العروض
    $offers_query = "SELECT COUNT(*) FROM offers";
    $offers_stmt = $db->prepare($offers_query);
    $offers_stmt->execute();
    $system_stats['total_offers'] = $offers_stmt->fetchColumn();
    
    // استخدام القرص
    $system_stats['disk_usage'] = disk_free_space('.') / (1024 * 1024 * 1024); // GB
    
} catch (PDOException $e) {
    // استخدام القيم الافتراضية
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات المدير - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إعدادات المدير المتقدمة</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <span class="badge bg-danger">مدير النظام</span>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات النظام -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center bg-primary text-white">
                            <div class="card-body">
                                <h3><?php echo number_format($system_stats['total_users']); ?></h3>
                                <p class="mb-0">إجمالي المستخدمين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-success text-white">
                            <div class="card-body">
                                <h3><?php echo number_format($system_stats['total_offers']); ?></h3>
                                <p class="mb-0">إجمالي العروض</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-info text-white">
                            <div class="card-body">
                                <h3><?php echo number_format($system_stats['disk_usage'], 1); ?> GB</h3>
                                <p class="mb-0">مساحة القرص المتاحة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-warning text-dark">
                            <div class="card-body">
                                <h3><?php echo phpversion(); ?></h3>
                                <p class="mb-0">إصدار PHP</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- إعدادات الموقع -->
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إعدادات الموقع العامة</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_site_settings">
                                    
                                    <div class="mb-3">
                                        <label for="site_name" class="form-label">اسم الموقع</label>
                                        <input type="text" class="form-control" id="site_name" name="site_name" 
                                               value="<?php echo htmlspecialchars(SITE_NAME); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="site_description" class="form-label">وصف الموقع</label>
                                        <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars(SITE_DESCRIPTION ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="admin_email" class="form-label">بريد المدير</label>
                                        <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                               value="<?php echo htmlspecialchars(ADMIN_EMAIL ?? ''); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="currency_symbol" class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" 
                                               value="<?php echo htmlspecialchars(CURRENCY_SYMBOL); ?>" required>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الأمان -->
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-warning">إعدادات الأمان</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_security_settings">
                                    
                                    <div class="mb-3">
                                        <label for="max_login_attempts" class="form-label">عدد محاولات تسجيل الدخول</label>
                                        <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                               value="<?php echo $current_settings['max_login_attempts']; ?>" min="3" max="10">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="session_timeout" class="form-label">مهلة الجلسة (ثانية)</label>
                                        <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                               value="<?php echo $current_settings['session_timeout']; ?>" min="1800" max="86400">
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enable_2fa" name="enable_2fa" 
                                               <?php echo $current_settings['enable_2fa'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_2fa">
                                            تفعيل المصادقة الثنائية
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="force_https" name="force_https" 
                                               <?php echo $current_settings['force_https'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="force_https">
                                            إجبار استخدام HTTPS
                                        </label>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-shield-alt me-1"></i>حفظ إعدادات الأمان
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات النظام -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">أدوات النظام</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-broom fa-2x text-info mb-2"></i>
                                        <h6>مسح الكاش</h6>
                                        <p class="text-muted small">مسح ملفات الكاش المؤقتة</p>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="clear_cache">
                                            <button type="submit" class="btn btn-info btn-sm">
                                                <i class="fas fa-trash me-1"></i>مسح الكاش
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-database fa-2x text-success mb-2"></i>
                                        <h6>نسخة احتياطية</h6>
                                        <p class="text-muted small">إنشاء نسخة احتياطية من قاعدة البيانات</p>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="backup_database">
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fas fa-download me-1"></i>إنشاء نسخة
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                                        <h6>تقرير النظام</h6>
                                        <p class="text-muted small">عرض تقرير مفصل عن النظام</p>
                                        <a href="system-report.php" class="btn btn-warning btn-sm">
                                            <i class="fas fa-file-alt me-1"></i>عرض التقرير
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
</body>
</html>
