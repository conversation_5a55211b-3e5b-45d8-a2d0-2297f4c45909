<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// معالجة البحث والفلترة
$search = sanitize($_GET['search'] ?? '');
$network_filter = sanitize($_GET['network'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$type_filter = sanitize($_GET['type'] ?? '');

// بناء الاستعلام
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(o.title LIKE :search OR o.description LIKE :search)";
    $params[':search'] = "%$search%";
}

if (!empty($network_filter)) {
    $where_conditions[] = "o.network_id = :network_id";
    $params[':network_id'] = $network_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($type_filter)) {
    $where_conditions[] = "o.type = :type";
    $params[':type'] = $type_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب العروض
$offers_query = "SELECT o.*, n.name as network_name,
                 COALESCE(SUM(ds.clicks), 0) as total_clicks,
                 COALESCE(SUM(ds.conversions), 0) as total_conversions,
                 COALESCE(SUM(ds.earnings), 0) as total_earnings
                 FROM offers o 
                 JOIN networks n ON o.network_id = n.id 
                 LEFT JOIN daily_stats ds ON o.id = ds.offer_id
                 $where_clause
                 GROUP BY o.id
                 ORDER BY o.created_at DESC";

$offers_stmt = $db->prepare($offers_query);
foreach ($params as $key => $value) {
    $offers_stmt->bindValue($key, $value);
}
$offers_stmt->execute();
$offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب الشبكات للفلترة
$networks_query = "SELECT id, name FROM networks WHERE status = 'active' ORDER BY name";
$networks_stmt = $db->prepare($networks_query);
$networks_stmt->execute();
$networks = $networks_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة العروض</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-plus me-1"></i>إضافة عرض
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="add.php">
                                    <i class="fas fa-network-wired me-2"></i>من الشبكة
                                </a></li>
                                <li><a class="dropdown-item" href="add-manual.php">
                                    <i class="fas fa-edit me-2"></i>عرض يدوي
                                </a></li>
                            </ul>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData('offers', 'excel')">
                                <i class="fas fa-file-excel me-1"></i>تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="البحث في العنوان أو الوصف">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="network" class="form-label">الشبكة</label>
                                <select class="form-control" id="network" name="network">
                                    <option value="">جميع الشبكات</option>
                                    <?php foreach ($networks as $network): ?>
                                        <option value="<?php echo $network['id']; ?>" 
                                                <?php echo $network_filter == $network['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($network['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                    <option value="paused" <?php echo $status_filter == 'paused' ? 'selected' : ''; ?>>متوقف</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="type" class="form-label">النوع</label>
                                <select class="form-control" id="type" name="type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="cpa" <?php echo $type_filter == 'cpa' ? 'selected' : ''; ?>>CPA</option>
                                    <option value="cpl" <?php echo $type_filter == 'cpl' ? 'selected' : ''; ?>>CPL</option>
                                    <option value="cps" <?php echo $type_filter == 'cps' ? 'selected' : ''; ?>>CPS</option>
                                    <option value="cpi" <?php echo $type_filter == 'cpi' ? 'selected' : ''; ?>>CPI</option>
                                    <option value="cpc" <?php echo $type_filter == 'cpc' ? 'selected' : ''; ?>>CPC</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid gap-2 d-md-flex">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>بحث
                                    </button>
                                    <a href="index.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول العروض -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            العروض (<?php echo count($offers); ?>)
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered data-table" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>العرض</th>
                                        <th>الشبكة</th>
                                        <th>النوع</th>
                                        <th>العمولة</th>
                                        <th>النقرات</th>
                                        <th>التحويلات</th>
                                        <th>الأرباح</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($offers as $offer): ?>
                                    <tr>
                                        <td><?php echo $offer['id']; ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if ($offer['image']): ?>
                                                    <img src="../../uploads/offers/<?php echo $offer['image']; ?>" 
                                                         class="rounded me-2" width="40" height="40" alt="صورة العرض">
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($offer['title']); ?></strong>
                                                    <?php if ($offer['description']): ?>
                                                        <br><small class="text-muted">
                                                            <?php echo htmlspecialchars(substr($offer['description'], 0, 50)) . '...'; ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($offer['network_name']); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo strtoupper($offer['type']); ?></span>
                                        </td>
                                        <td><?php echo CURRENCY_SYMBOL . number_format($offer['payout'], 2); ?></td>
                                        <td><?php echo number_format($offer['total_clicks']); ?></td>
                                        <td><?php echo number_format($offer['total_conversions']); ?></td>
                                        <td><?php echo CURRENCY_SYMBOL . number_format($offer['total_earnings'], 2); ?></td>
                                        <td>
                                            <?php
                                            $status_class = [
                                                'active' => 'success',
                                                'inactive' => 'secondary',
                                                'paused' => 'warning'
                                            ];
                                            $status_text = [
                                                'active' => 'نشط',
                                                'inactive' => 'غير نشط',
                                                'paused' => 'متوقف'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_class[$offer['status']]; ?>">
                                                <?php echo $status_text[$offer['status']]; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="view.php?id=<?php echo $offer['id']; ?>" 
                                                   class="btn btn-info btn-sm" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit.php?id=<?php echo $offer['id']; ?>" 
                                                   class="btn btn-warning btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-danger btn-sm" 
                                                        onclick="deleteOffer(<?php echo $offer['id']; ?>)" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" 
                                                            data-bs-toggle="dropdown">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="stats.php?id=<?php echo $offer['id']; ?>">
                                                            <i class="fas fa-chart-bar me-2"></i>الإحصائيات
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="duplicate.php?id=<?php echo $offer['id']; ?>">
                                                            <i class="fas fa-copy me-2"></i>نسخ
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item" href="#" onclick="toggleStatus(<?php echo $offer['id']; ?>, '<?php echo $offer['status']; ?>')">
                                                            <i class="fas fa-toggle-<?php echo $offer['status'] == 'active' ? 'off' : 'on'; ?> me-2"></i>
                                                            <?php echo $offer['status'] == 'active' ? 'إيقاف' : 'تفعيل'; ?>
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        // حذف عرض
        function deleteOffer(offerId) {
            if (confirmDelete('هل أنت متأكد من حذف هذا العرض؟ سيتم حذف جميع البيانات المرتبطة به.')) {
                fetch('delete.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({id: offerId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('تم حذف العرض بنجاح', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('حدث خطأ في الاتصال', 'danger');
                });
            }
        }

        // تغيير حالة العرض
        function toggleStatus(offerId, currentStatus) {
            const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
            const action = newStatus === 'active' ? 'تفعيل' : 'إيقاف';
            
            if (confirm(`هل أنت متأكد من ${action} هذا العرض؟`)) {
                fetch('toggle_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({id: offerId, status: newStatus})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert(`تم ${action} العرض بنجاح`, 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('حدث خطأ في الاتصال', 'danger');
                });
            }
        }
    </script>
</body>
</html>
