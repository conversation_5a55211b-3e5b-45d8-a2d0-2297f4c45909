<?php
require_once '../config/config.php';

// تسجيل النشاط قبل تسجيل الخروج
if (isLoggedIn()) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $log_query = "INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) 
                      VALUES (:user_id, 'logout', 'تسجيل خروج', :ip, :user_agent)";
        $log_stmt = $db->prepare($log_query);
        $log_stmt->bindParam(':user_id', $_SESSION['user_id']);
        $log_stmt->bindParam(':ip', $_SERVER['REMOTE_ADDR']);
        $log_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT']);
        $log_stmt->execute();
    } catch (PDOException $e) {
        logError("خطأ في تسجيل نشاط الخروج: " . $e->getMessage());
    }
}

// حذف كوكي التذكر
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/', '', false, true);
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php');
exit();
?>
