<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// معالجة إضافة العرض
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);
    $external_offer_url = sanitize($_POST['external_offer_url']);
    $external_image_url = sanitize($_POST['external_image_url']);
    $payout = floatval($_POST['payout']);
    $type = sanitize($_POST['type']);
    $category = sanitize($_POST['category']);
    $countries = sanitize($_POST['countries']);
    $requirements = sanitize($_POST['requirements']);
    $network_id = intval($_POST['network_id']);
    $status = sanitize($_POST['status']);
    
    $errors = [];
    
    // التحقق من البيانات
    if (empty($title)) {
        $errors[] = 'عنوان العرض مطلوب';
    }
    
    if (empty($external_offer_url) || !filter_var($external_offer_url, FILTER_VALIDATE_URL)) {
        $errors[] = 'رابط العرض مطلوب ويجب أن يكون صحيحاً';
    }
    
    if (!empty($external_image_url) && !filter_var($external_image_url, FILTER_VALIDATE_URL)) {
        $errors[] = 'رابط الصورة يجب أن يكون صحيحاً';
    }
    
    if ($payout <= 0) {
        $errors[] = 'العمولة يجب أن تكون أكبر من صفر';
    }
    
    if ($network_id <= 0) {
        $errors[] = 'يجب اختيار شبكة';
    }
    
    // معالجة رفع الصورة (اختياري)
    $uploaded_image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../../uploads/offers/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        
        if (in_array($file_extension, $allowed_extensions)) {
            $uploaded_image = uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $uploaded_image;
            
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $errors[] = 'فشل في رفع الصورة';
                $uploaded_image = '';
            }
        } else {
            $errors[] = 'نوع الصورة غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP';
        }
    }
    
    if (empty($errors)) {
        try {
            // إدراج العرض الجديد
            $insert_query = "INSERT INTO offers (
                network_id, title, description, external_offer_url, external_image_url, 
                image, payout, type, category, countries, restrictions, 
                is_external, external_tracking, status
            ) VALUES (
                :network_id, :title, :description, :external_offer_url, :external_image_url,
                :image, :payout, :type, :category, :countries, :restrictions,
                1, 1, :status
            )";
            
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':network_id', $network_id);
            $insert_stmt->bindParam(':title', $title);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':external_offer_url', $external_offer_url);
            $insert_stmt->bindParam(':external_image_url', $external_image_url);
            $insert_stmt->bindParam(':image', $uploaded_image);
            $insert_stmt->bindParam(':payout', $payout);
            $insert_stmt->bindParam(':type', $type);
            $insert_stmt->bindParam(':category', $category);
            $insert_stmt->bindParam(':countries', $countries);
            $insert_stmt->bindParam(':restrictions', $requirements);
            $insert_stmt->bindParam(':status', $status);
            
            if ($insert_stmt->execute()) {
                $offer_id = $db->lastInsertId();
                
                // تسجيل النشاط
                $activity_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                                  VALUES (:user_id, 'offer_created', 'إضافة عرض يدوي جديد', :data)";
                
                $activity_data = json_encode([
                    'offer_id' => $offer_id,
                    'title' => $title,
                    'type' => 'manual',
                    'payout' => $payout
                ]);

                $activity_stmt = $db->prepare($activity_query);
                $activity_stmt->bindParam(':user_id', $_SESSION['user_id']);
                $activity_stmt->bindParam(':data', $activity_data);
                $activity_stmt->execute();
                
                $_SESSION['success'] = 'تم إضافة العرض بنجاح!';
                header('Location: index.php');
                exit();
            } else {
                $errors[] = 'حدث خطأ في إضافة العرض';
            }
            
        } catch (PDOException $e) {
            logError("خطأ في إضافة عرض يدوي: " . $e->getMessage());
            $errors[] = 'حدث خطأ في النظام';
        }
    }
}

// جلب الشبكات
$networks_query = "SELECT id, name FROM networks WHERE status = 'active' ORDER BY name";
$networks_stmt = $db->prepare($networks_query);
$networks_stmt->execute();
$networks = $networks_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب الفئات الموجودة
$categories_query = "SELECT DISTINCT category FROM offers WHERE category IS NOT NULL AND category != '' ORDER BY category";
$categories_stmt = $db->prepare($categories_query);
$categories_stmt->execute();
$existing_categories = $categories_stmt->fetchAll(PDO::FETCH_COLUMN);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عرض يدوي - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إضافة عرض يدوي</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للعروض
                        </a>
                    </div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>يرجى تصحيح الأخطاء التالية:</h6>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-plus me-2"></i>
                            معلومات العرض الجديد
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="row">
                                <!-- معلومات أساسية -->
                                <div class="col-lg-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">عنوان العرض *</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" 
                                               required placeholder="مثل: تحميل تطبيق الألعاب">
                                    </div>

                                    <div class="mb-3">
                                        <label for="description" class="form-label">وصف العرض</label>
                                        <textarea class="form-control" id="description" name="description" rows="4" 
                                                  placeholder="وصف مفصل للعرض ومتطلبات الإكمال"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="external_offer_url" class="form-label">رابط العرض *</label>
                                        <input type="url" class="form-control" id="external_offer_url" name="external_offer_url" 
                                               value="<?php echo htmlspecialchars($_POST['external_offer_url'] ?? ''); ?>" 
                                               required placeholder="https://example.com/offer">
                                        <small class="text-muted">الرابط الذي سيتم توجيه المستخدمين إليه</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="requirements" class="form-label">متطلبات الإكمال</label>
                                        <textarea class="form-control" id="requirements" name="requirements" rows="3" 
                                                  placeholder="مثل: تحميل التطبيق، التسجيل، الوصول للمستوى 5"><?php echo htmlspecialchars($_POST['requirements'] ?? ''); ?></textarea>
                                    </div>
                                </div>

                                <!-- الإعدادات -->
                                <div class="col-lg-4">
                                    <div class="mb-3">
                                        <label for="network_id" class="form-label">الشبكة *</label>
                                        <select class="form-control" id="network_id" name="network_id" required>
                                            <option value="">اختر الشبكة</option>
                                            <?php foreach ($networks as $network): ?>
                                                <option value="<?php echo $network['id']; ?>" 
                                                        <?php echo (($_POST['network_id'] ?? '') == $network['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($network['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="payout" class="form-label">العمولة *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                            <input type="number" class="form-control" id="payout" name="payout" 
                                                   value="<?php echo htmlspecialchars($_POST['payout'] ?? ''); ?>" 
                                                   step="0.01" min="0.01" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="type" class="form-label">نوع العرض</label>
                                        <select class="form-control" id="type" name="type">
                                            <option value="cpa" <?php echo (($_POST['type'] ?? 'cpa') == 'cpa') ? 'selected' : ''; ?>>CPA</option>
                                            <option value="cpl" <?php echo (($_POST['type'] ?? '') == 'cpl') ? 'selected' : ''; ?>>CPL</option>
                                            <option value="cps" <?php echo (($_POST['type'] ?? '') == 'cps') ? 'selected' : ''; ?>>CPS</option>
                                            <option value="cpi" <?php echo (($_POST['type'] ?? '') == 'cpi') ? 'selected' : ''; ?>>CPI</option>
                                            <option value="cpc" <?php echo (($_POST['type'] ?? '') == 'cpc') ? 'selected' : ''; ?>>CPC</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="category" class="form-label">الفئة</label>
                                        <input type="text" class="form-control" id="category" name="category" 
                                               value="<?php echo htmlspecialchars($_POST['category'] ?? ''); ?>" 
                                               list="categories" placeholder="مثل: ألعاب، تطبيقات، استطلاعات">
                                        <datalist id="categories">
                                            <?php foreach ($existing_categories as $cat): ?>
                                                <option value="<?php echo htmlspecialchars($cat); ?>">
                                            <?php endforeach; ?>
                                        </datalist>
                                    </div>

                                    <div class="mb-3">
                                        <label for="countries" class="form-label">البلدان المستهدفة</label>
                                        <input type="text" class="form-control" id="countries" name="countries" 
                                               value="<?php echo htmlspecialchars($_POST['countries'] ?? 'ALL'); ?>" 
                                               placeholder="مثل: US,UK,CA أو ALL للجميع">
                                        <small class="text-muted">اتركه ALL للجميع أو أدخل رموز البلدان مفصولة بفاصلة</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="status" class="form-label">الحالة</label>
                                        <select class="form-control" id="status" name="status">
                                            <option value="active" <?php echo (($_POST['status'] ?? 'active') == 'active') ? 'selected' : ''; ?>>نشط</option>
                                            <option value="inactive" <?php echo (($_POST['status'] ?? '') == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                                            <option value="paused" <?php echo (($_POST['status'] ?? '') == 'paused') ? 'selected' : ''; ?>>متوقف</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- الصور -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6 class="border-bottom pb-2 mb-3">صور العرض</h6>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="external_image_url" class="form-label">رابط الصورة الخارجي</label>
                                        <input type="url" class="form-control" id="external_image_url" name="external_image_url" 
                                               value="<?php echo htmlspecialchars($_POST['external_image_url'] ?? ''); ?>" 
                                               placeholder="https://example.com/image.jpg">
                                        <small class="text-muted">رابط مباشر لصورة العرض</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="image" class="form-label">أو ارفع صورة من جهازك</label>
                                        <input type="file" class="form-control" id="image" name="image" 
                                               accept="image/jpeg,image/jpg,image/png,image/gif,image/webp">
                                        <small class="text-muted">الأنواع المدعومة: JPG, PNG, GIF, WebP (حد أقصى 5MB)</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="index.php" class="btn btn-secondary me-md-2">
                                            <i class="fas fa-times me-1"></i>إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>حفظ العرض
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        // معاينة الصورة المرفوعة
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // يمكن إضافة معاينة للصورة هنا
                    console.log('Image selected:', file.name);
                };
                reader.readAsDataURL(file);
            }
        });

        // التحقق من رابط الصورة الخارجي
        document.getElementById('external_image_url').addEventListener('blur', function() {
            const url = this.value;
            if (url) {
                const img = new Image();
                img.onload = function() {
                    console.log('External image is valid');
                };
                img.onerror = function() {
                    alert('رابط الصورة غير صحيح أو لا يمكن الوصول إليه');
                };
                img.src = url;
            }
        });
    </script>
</body>
</html>
