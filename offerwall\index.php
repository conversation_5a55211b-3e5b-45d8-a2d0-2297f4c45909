<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

// جلب معلومات المستخدم
$user_query = "SELECT username, balance, total_earnings FROM users WHERE id = :user_id";
$user_stmt = $db->prepare($user_query);
$user_stmt->bindParam(':user_id', $user_id);
$user_stmt->execute();
$user_info = $user_stmt->fetch(PDO::FETCH_ASSOC);

// إنشاء SubID فريد للمستخدم
$subid = $user_id . '_' . time();

// تسجيل زيارة Offerwall
try {
    $visit_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                    VALUES (:user_id, 'offerwall_visit', 'زيارة Offerwall', :data)";
    
    $visit_data = json_encode([
        'subid' => $subid,
        'timestamp' => date('Y-m-d H:i:s'),
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT']
    ]);

    $visit_stmt = $db->prepare($visit_query);
    $visit_stmt->bindParam(':user_id', $user_id);
    $visit_stmt->bindParam(':data', $visit_data);
    $visit_stmt->execute();
} catch (PDOException $e) {
    logError("خطأ في تسجيل زيارة Offerwall: " . $e->getMessage());
}

// بناء رابط Offerwall مع SubID
$offerwall_url = "https://fastrsrvr.com/list/191?subid=" . urlencode($subid);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offerwall - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .offerwall-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            overflow: hidden;
        }
        .offerwall-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
        }
        .balance-card {
            background: linear-gradient(135deg, #1cc88a 0%, #36b9cc 100%);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .iframe-container {
            position: relative;
            width: 100%;
            height: 700px;
            border: none;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .mobile-toggle {
            display: none;
        }
        @media (max-width: 768px) {
            .mobile-toggle {
                display: block;
            }
            .iframe-container {
                height: 600px;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-gift me-2"></i>
                        Offerwall - اربح المزيد
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshOfferwall()">
                                <i class="fas fa-sync me-1"></i>تحديث
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info mobile-toggle" onclick="toggleMobileView()">
                                <i class="fas fa-mobile-alt me-1"></i>عرض الجوال
                            </button>
                        </div>
                    </div>
                </div>

                <!-- معلومات الرصيد -->
                <div class="balance-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">
                                <i class="fas fa-wallet me-2"></i>
                                مرحباً <?php echo htmlspecialchars($user_info['username']); ?>!
                            </h5>
                            <p class="mb-0">أكمل العروض أدناه لزيادة رصيدك</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="row">
                                <div class="col-6 col-md-12">
                                    <small>الرصيد الحالي</small>
                                    <h4 class="mb-0"><?php echo CURRENCY_SYMBOL . number_format($user_info['balance'], 2); ?></h4>
                                </div>
                                <div class="col-6 col-md-12">
                                    <small>إجمالي الأرباح</small>
                                    <h4 class="mb-0"><?php echo CURRENCY_SYMBOL . number_format($user_info['total_earnings'], 2); ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تعليمات سريعة -->
                <div class="alert alert-info mb-4">
                    <h6><i class="fas fa-info-circle me-2"></i>كيفية الاستخدام:</h6>
                    <ul class="mb-0">
                        <li>اختر عرضاً من القائمة أدناه</li>
                        <li>أكمل المتطلبات (تحميل تطبيق، تسجيل، استطلاع، إلخ)</li>
                        <li>ستتم إضافة العمولة لرصيدك تلقائياً</li>
                        <li>يمكنك سحب أرباحك عند الوصول للحد الأدنى</li>
                    </ul>
                </div>

                <!-- Offerwall Container -->
                <div class="offerwall-container">
                    <div class="offerwall-header">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    العروض المتاحة
                                </h5>
                                <small>SubID: <?php echo htmlspecialchars($subid); ?></small>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <small>مدعوم بواسطة CPALead</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="position-relative">
                        <!-- Loading Overlay -->
                        <div class="loading-overlay" id="loadingOverlay">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2 text-muted">جاري تحميل العروض...</p>
                            </div>
                        </div>

                        <!-- Offerwall iFrame -->
                        <iframe 
                            id="offerwallFrame"
                            sandbox="allow-popups allow-same-origin allow-scripts allow-top-navigation-by-user-activation allow-popups-to-escape-sandbox" 
                            src="<?php echo htmlspecialchars($offerwall_url); ?>" 
                            class="iframe-container"
                            frameborder="0"
                            onload="hideLoading()">
                        </iframe>
                    </div>
                </div>

                <!-- طرق بديلة للوصول -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-mobile-alt me-2"></i>
                                    للأجهزة المحمولة
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">افتح Offerwall في نافذة منفصلة للحصول على تجربة أفضل على الجوال</p>
                                <a href="<?php echo htmlspecialchars($offerwall_url); ?>" target="_blank" class="btn btn-primary btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    فتح في نافذة جديدة
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    إحصائياتك
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">تابع أداءك وأرباحك من العروض</p>
                                <a href="../reports/" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    عرض التقارير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نصائح للحصول على أرباح أكثر -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            نصائح لزيادة الأرباح
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-star text-warning me-1"></i>اختر العروض عالية القيمة</h6>
                                <p class="text-muted small">ابحث عن العروض التي تقدم عمولات أعلى</p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-clock text-info me-1"></i>أكمل العروض بسرعة</h6>
                                <p class="text-muted small">بعض العروض لها مدة صلاحية محدودة</p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-check-circle text-success me-1"></i>اتبع التعليمات بدقة</h6>
                                <p class="text-muted small">تأكد من إكمال جميع المتطلبات للحصول على العمولة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // إخفاء شاشة التحميل
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        // تحديث Offerwall
        function refreshOfferwall() {
            document.getElementById('loadingOverlay').style.display = 'flex';
            document.getElementById('offerwallFrame').src = document.getElementById('offerwallFrame').src;
        }

        // تبديل العرض للجوال
        function toggleMobileView() {
            const frame = document.getElementById('offerwallFrame');
            const currentSrc = frame.src;
            
            if (currentSrc.includes('mobile=1')) {
                frame.src = currentSrc.replace('&mobile=1', '');
            } else {
                frame.src = currentSrc + '&mobile=1';
            }
            
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        // تحديث الرصيد كل دقيقة
        setInterval(function() {
            fetch('../api/stats.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.totals) {
                    // يمكن تحديث الرصيد هنا إذا كان متوفراً في API
                }
            })
            .catch(error => {
                console.log('خطأ في تحديث الرصيد:', error);
            });
        }, 60000);

        // إظهار شاشة التحميل عند بداية التحميل
        document.getElementById('offerwallFrame').addEventListener('load', hideLoading);
    </script>
</body>
</html>
