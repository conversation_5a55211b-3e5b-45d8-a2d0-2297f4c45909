<?php
/**
 * مثال على إنشاء إشعار ترحيبي عن البريد المؤقت للمستخدمين الجدد
 * يمكن دمج هذا الكود في نظام التسجيل
 */

require_once '../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../auth/login.php');
    exit();
}

$database = new Database();
$notificationManager = new NotificationManager($database);

// إنشاء إشعار ترحيبي عن البريد المؤقت
function createTempMailWelcomeNotification($user_id, $notificationManager) {
    $notification_data = [
        'title' => 'مرحباً! اكتشف مواقع البريد المؤقت 📧',
        'message' => 'هل تعلم أنه يمكنك استخدام البريد المؤقت لإكمال العروض بسهولة؟ تحقق من مواقع البريد المؤقت المتاحة في الموقع!',
        'type' => 'info',
        'target_audience' => 'custom',
        'target_users' => json_encode([$user_id]),
        'offer_id' => null,
        'priority' => 'medium',
        'show_popup' => true,
        'show_banner' => true,
        'show_sidebar' => true,
        'auto_hide' => false, // لا نريد إخفاءه تلقائياً للمستخدمين الجدد
        'hide_after_seconds' => 0,
        'start_date' => date('Y-m-d H:i:s'),
        'end_date' => date('Y-m-d H:i:s', strtotime('+7 days')), // يبقى لمدة أسبوع
        'created_by' => 1 // معرف المدير
    ];
    
    return $notificationManager->createNotification($notification_data);
}

// إنشاء إشعار للمستخدمين الجدد (آخر 24 ساعة)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_welcome_notifications'])) {
    $db = $database->getConnection();
    
    // جلب المستخدمين الجدد الذين لم يحصلوا على إشعار البريد المؤقت
    $new_users_query = "SELECT u.id, u.username 
                        FROM users u
                        WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                        AND u.role = 'publisher'
                        AND NOT EXISTS (
                            SELECT 1 FROM user_notifications un 
                            INNER JOIN notifications n ON un.notification_id = n.id 
                            WHERE un.user_id = u.id 
                            AND n.title LIKE '%البريد المؤقت%'
                        )";
    
    $new_users_stmt = $db->prepare($new_users_query);
    $new_users_stmt->execute();
    $new_users = $new_users_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $created_count = 0;
    foreach ($new_users as $user) {
        if (createTempMailWelcomeNotification($user['id'], $notificationManager)) {
            $created_count++;
        }
    }
    
    $_SESSION['success'] = "تم إنشاء {$created_count} إشعار ترحيبي عن البريد المؤقت للمستخدمين الجدد";
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// إنشاء إشعار عام عن البريد المؤقت
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_general_notification'])) {
    $notification_data = [
        'title' => 'جديد! مواقع البريد المؤقت متاحة الآن 🎉',
        'message' => 'يمكنك الآن الوصول إلى أفضل مواقع البريد المؤقت مباشرة من موقعنا. استخدمها لإكمال العروض بسهولة وأمان!',
        'type' => 'success',
        'target_audience' => 'active',
        'target_users' => null,
        'offer_id' => null,
        'priority' => 'high',
        'show_popup' => true,
        'show_banner' => true,
        'show_sidebar' => true,
        'auto_hide' => true,
        'hide_after_seconds' => 15,
        'start_date' => date('Y-m-d H:i:s'),
        'end_date' => date('Y-m-d H:i:s', strtotime('+3 days')),
        'created_by' => $_SESSION['user_id']
    ];
    
    if ($notificationManager->createNotification($notification_data)) {
        $_SESSION['success'] = 'تم إنشاء إشعار عام عن البريد المؤقت بنجاح!';
    } else {
        $_SESSION['error'] = 'فشل في إنشاء الإشعار';
    }
    
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إشعارات البريد المؤقت - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-envelope me-2"></i>إدارة إشعارات البريد المؤقت</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($_SESSION['success'])): ?>
                            <div class="alert alert-success">
                                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($_SESSION['error'])): ?>
                            <div class="alert alert-danger">
                                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <!-- إشعار للمستخدمين الجدد -->
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">إشعار ترحيبي للمستخدمين الجدد</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted">
                                            إرسال إشعار ترحيبي عن مواقع البريد المؤقت للمستخدمين الذين سجلوا في آخر 24 ساعة
                                        </p>
                                        
                                        <div class="alert alert-info">
                                            <h6>محتوى الإشعار:</h6>
                                            <strong>العنوان:</strong> مرحباً! اكتشف مواقع البريد المؤقت 📧<br>
                                            <strong>الرسالة:</strong> هل تعلم أنه يمكنك استخدام البريد المؤقت لإكمال العروض بسهولة؟
                                        </div>
                                        
                                        <form method="POST">
                                            <button type="submit" name="create_welcome_notifications" class="btn btn-primary w-100">
                                                <i class="fas fa-paper-plane me-1"></i>إرسال للمستخدمين الجدد
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إشعار عام -->
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">إشعار عام للجميع</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted">
                                            إرسال إشعار عام لجميع المستخدمين النشطين عن توفر مواقع البريد المؤقت
                                        </p>
                                        
                                        <div class="alert alert-success">
                                            <h6>محتوى الإشعار:</h6>
                                            <strong>العنوان:</strong> جديد! مواقع البريد المؤقت متاحة الآن 🎉<br>
                                            <strong>الرسالة:</strong> يمكنك الآن الوصول إلى أفضل مواقع البريد المؤقت مباشرة من موقعنا
                                        </div>
                                        
                                        <form method="POST">
                                            <button type="submit" name="create_general_notification" class="btn btn-success w-100">
                                                <i class="fas fa-bullhorn me-1"></i>إرسال للجميع
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- معاينة الإشعارات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>معاينة الإشعارات</h5>
                                
                                <!-- إشعار ترحيبي -->
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-envelope me-2"></i>مرحباً! اكتشف مواقع البريد المؤقت 📧</h6>
                                    <p class="mb-2">هل تعلم أنه يمكنك استخدام البريد المؤقت لإكمال العروض بسهولة؟ تحقق من مواقع البريد المؤقت المتاحة في الموقع!</p>
                                    <a href="../temp-mail.php" class="btn btn-info btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>عرض مواقع البريد المؤقت
                                    </a>
                                </div>
                                
                                <!-- إشعار عام -->
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-gift me-2"></i>جديد! مواقع البريد المؤقت متاحة الآن 🎉</h6>
                                    <p class="mb-2">يمكنك الآن الوصول إلى أفضل مواقع البريد المؤقت مباشرة من موقعنا. استخدمها لإكمال العروض بسهولة وأمان!</p>
                                    <a href="../temp-mail.php" class="btn btn-success btn-sm">
                                        <i class="fas fa-external-link-alt me-1"></i>جرب الآن
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- إحصائيات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>إحصائيات سريعة:</h6>
                                        <div class="row text-center">
                                            <div class="col-md-4">
                                                <h4 class="text-primary">3</h4>
                                                <small>مواقع بريد مؤقت</small>
                                            </div>
                                            <div class="col-md-4">
                                                <h4 class="text-success">100%</h4>
                                                <small>مجاني</small>
                                            </div>
                                            <div class="col-md-4">
                                                <h4 class="text-info">24/7</h4>
                                                <small>متاح دائماً</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
