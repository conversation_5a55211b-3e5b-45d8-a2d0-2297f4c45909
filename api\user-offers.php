<?php
require_once '../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح', 'success' => false]);
    exit();
}

$user_id = intval($_GET['user_id'] ?? 0);

if ($user_id <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف المستخدم غير صحيح', 'success' => false]);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب العروض التي نقر عليها المستخدم ولم يكملها
    $offers_query = "SELECT DISTINCT o.id, o.title, o.payout, o.description
                     FROM offers o
                     INNER JOIN clicks c ON o.id = c.offer_id
                     LEFT JOIN conversions conv ON c.id = conv.click_id
                     WHERE c.user_id = :user_id 
                     AND conv.id IS NULL
                     AND o.status = 'active'
                     ORDER BY c.created_at DESC
                     LIMIT 20";
    
    $offers_stmt = $db->prepare($offers_query);
    $offers_stmt->bindParam(':user_id', $user_id);
    $offers_stmt->execute();
    $offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إضافة العروض النشطة الأخرى
    $all_offers_query = "SELECT id, title, payout, description
                         FROM offers 
                         WHERE status = 'active'
                         ORDER BY created_at DESC
                         LIMIT 50";
    
    $all_offers_stmt = $db->prepare($all_offers_query);
    $all_offers_stmt->execute();
    $all_offers = $all_offers_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // دمج العروض وإزالة المكررات
    $offer_ids = array_column($offers, 'id');
    foreach ($all_offers as $offer) {
        if (!in_array($offer['id'], $offer_ids)) {
            $offers[] = $offer;
        }
    }
    
    // تنسيق البيانات
    $formatted_offers = [];
    foreach ($offers as $offer) {
        $formatted_offers[] = [
            'id' => $offer['id'],
            'title' => $offer['title'],
            'payout' => CURRENCY_SYMBOL . number_format($offer['payout'], 2),
            'description' => substr($offer['description'], 0, 100) . '...'
        ];
    }
    
    echo json_encode([
        'success' => true,
        'offers' => $formatted_offers,
        'count' => count($formatted_offers)
    ]);
    
} catch (PDOException $e) {
    logError("خطأ في جلب عروض المستخدم: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في النظام', 'success' => false]);
}
?>
