<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

// معالجة البحث والفلترة
$search = sanitize($_GET['search'] ?? '');
$network_filter = sanitize($_GET['network'] ?? '');
$type_filter = sanitize($_GET['type'] ?? '');
$category_filter = sanitize($_GET['category'] ?? '');
$country_filter = sanitize($_GET['country'] ?? '');

// بناء الاستعلام
$where_conditions = ["o.status = 'active'"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(o.title LIKE :search OR o.description LIKE :search)";
    $params[':search'] = "%$search%";
}

if (!empty($network_filter)) {
    $where_conditions[] = "o.network_id = :network_id";
    $params[':network_id'] = $network_filter;
}

if (!empty($type_filter)) {
    $where_conditions[] = "o.type = :type";
    $params[':type'] = $type_filter;
}

if (!empty($category_filter)) {
    $where_conditions[] = "o.category = :category";
    $params[':category'] = $category_filter;
}

if (!empty($country_filter)) {
    $where_conditions[] = "(o.countries LIKE :country OR o.countries = 'ALL')";
    $params[':country'] = "%$country_filter%";
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// جلب العروض
$offers_query = "SELECT o.*, n.name as network_name, n.logo as network_logo,
                 COALESCE(user_stats.clicks, 0) as my_clicks,
                 COALESCE(user_stats.conversions, 0) as my_conversions,
                 COALESCE(user_stats.earnings, 0) as my_earnings
                 FROM offers o 
                 JOIN networks n ON o.network_id = n.id 
                 LEFT JOIN (
                     SELECT offer_id, 
                            SUM(clicks) as clicks,
                            SUM(conversions) as conversions,
                            SUM(earnings) as earnings
                     FROM daily_stats 
                     WHERE user_id = :user_id
                     GROUP BY offer_id
                 ) user_stats ON o.id = user_stats.offer_id
                 $where_clause
                 ORDER BY o.payout DESC, o.created_at DESC";

$offers_stmt = $db->prepare($offers_query);
$offers_stmt->bindParam(':user_id', $user_id);
foreach ($params as $key => $value) {
    $offers_stmt->bindValue($key, $value);
}
$offers_stmt->execute();
$offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب الشبكات للفلترة
$networks_query = "SELECT id, name FROM networks WHERE status = 'active' ORDER BY name";
$networks_stmt = $db->prepare($networks_query);
$networks_stmt->execute();
$networks = $networks_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب الفئات المتاحة
$categories_query = "SELECT DISTINCT category FROM offers WHERE status = 'active' AND category IS NOT NULL ORDER BY category";
$categories_stmt = $db->prepare($categories_query);
$categories_stmt->execute();
$categories = $categories_stmt->fetchAll(PDO::FETCH_COLUMN);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العروض المتاحة - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">العروض المتاحة</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="gridView">
                                <i class="fas fa-th me-1"></i>شبكة
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary active" id="listView">
                                <i class="fas fa-list me-1"></i>قائمة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="البحث في العروض">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="network" class="form-label">الشبكة</label>
                                <select class="form-control" id="network" name="network">
                                    <option value="">جميع الشبكات</option>
                                    <?php foreach ($networks as $network): ?>
                                        <option value="<?php echo $network['id']; ?>" 
                                                <?php echo $network_filter == $network['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($network['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="type" class="form-label">النوع</label>
                                <select class="form-control" id="type" name="type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="cpa" <?php echo $type_filter == 'cpa' ? 'selected' : ''; ?>>CPA</option>
                                    <option value="cpl" <?php echo $type_filter == 'cpl' ? 'selected' : ''; ?>>CPL</option>
                                    <option value="cps" <?php echo $type_filter == 'cps' ? 'selected' : ''; ?>>CPS</option>
                                    <option value="cpi" <?php echo $type_filter == 'cpi' ? 'selected' : ''; ?>>CPI</option>
                                    <option value="cpc" <?php echo $type_filter == 'cpc' ? 'selected' : ''; ?>>CPC</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="category" class="form-label">الفئة</label>
                                <select class="form-control" id="category" name="category">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category); ?>" 
                                                <?php echo $category_filter == $category ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid gap-2 d-md-flex">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>بحث
                                    </button>
                                    <a href="index.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- عرض العروض -->
                <div id="offersContainer">
                    <!-- عرض القائمة -->
                    <div id="listViewContainer" class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                العروض المتاحة (<?php echo count($offers); ?>)
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>العرض</th>
                                            <th>الشبكة</th>
                                            <th>النوع</th>
                                            <th>العمولة</th>
                                            <th>البلدان</th>
                                            <th>أدائي</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($offers as $offer): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($offer['image']): ?>
                                                        <img src="../uploads/offers/<?php echo $offer['image']; ?>" 
                                                             class="rounded me-2" width="40" height="40" alt="صورة العرض">
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($offer['title']); ?></strong>
                                                        <?php if ($offer['description']): ?>
                                                            <br><small class="text-muted">
                                                                <?php echo htmlspecialchars(substr($offer['description'], 0, 50)) . '...'; ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($offer['network_logo']): ?>
                                                        <img src="../uploads/networks/<?php echo $offer['network_logo']; ?>" 
                                                             class="rounded me-1" width="20" height="20" alt="شعار الشبكة">
                                                    <?php endif; ?>
                                                    <?php echo htmlspecialchars($offer['network_name']); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo strtoupper($offer['type']); ?></span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo CURRENCY_SYMBOL . number_format($offer['payout'], 2); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <small><?php echo htmlspecialchars($offer['countries']); ?></small>
                                            </td>
                                            <td>
                                                <small>
                                                    <i class="fas fa-mouse-pointer text-primary"></i> <?php echo number_format($offer['my_clicks']); ?><br>
                                                    <i class="fas fa-exchange-alt text-success"></i> <?php echo number_format($offer['my_conversions']); ?><br>
                                                    <i class="fas fa-dollar-sign text-warning"></i> <?php echo CURRENCY_SYMBOL . number_format($offer['my_earnings'], 2); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="view.php?id=<?php echo $offer['id']; ?>" 
                                                       class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="../tracking/generate.php?offer_id=<?php echo $offer['id']; ?>" 
                                                       class="btn btn-success btn-sm" title="إنشاء رابط">
                                                        <i class="fas fa-link"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- عرض الشبكة -->
                    <div id="gridViewContainer" class="row" style="display: none;">
                        <?php foreach ($offers as $offer): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card shadow h-100">
                                <?php if ($offer['image']): ?>
                                    <img src="../uploads/offers/<?php echo $offer['image']; ?>" 
                                         class="card-img-top" style="height: 200px; object-fit: cover;" alt="صورة العرض">
                                <?php endif; ?>
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($offer['title']); ?></h5>
                                    <p class="card-text">
                                        <?php echo htmlspecialchars(substr($offer['description'], 0, 100)) . '...'; ?>
                                    </p>
                                    
                                    <div class="row mb-2">
                                        <div class="col-6">
                                            <small class="text-muted">الشبكة:</small><br>
                                            <strong><?php echo htmlspecialchars($offer['network_name']); ?></strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">النوع:</small><br>
                                            <span class="badge bg-info"><?php echo strtoupper($offer['type']); ?></span>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-6">
                                            <small class="text-muted">العمولة:</small><br>
                                            <strong class="text-success">
                                                <?php echo CURRENCY_SYMBOL . number_format($offer['payout'], 2); ?>
                                            </strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">أدائي:</small><br>
                                            <small>
                                                <?php echo number_format($offer['my_clicks']); ?> نقرة<br>
                                                <?php echo number_format($offer['my_conversions']); ?> تحويل
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">البلدان:</small><br>
                                        <small><?php echo htmlspecialchars($offer['countries']); ?></small>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <a href="view.php?id=<?php echo $offer['id']; ?>" 
                                           class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye me-1"></i>التفاصيل
                                        </a>
                                        <a href="../tracking/generate.php?offer_id=<?php echo $offer['id']; ?>" 
                                           class="btn btn-success btn-sm">
                                            <i class="fas fa-link me-1"></i>إنشاء رابط
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <?php if (empty($offers)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>لا توجد عروض متاحة</h4>
                    <p class="text-muted">جرب تغيير معايير البحث أو تحقق لاحقاً من العروض الجديدة</p>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // تبديل عرض القائمة والشبكة
        document.getElementById('listView').addEventListener('click', function() {
            document.getElementById('listViewContainer').style.display = 'block';
            document.getElementById('gridViewContainer').style.display = 'none';
            this.classList.add('active');
            document.getElementById('gridView').classList.remove('active');
        });

        document.getElementById('gridView').addEventListener('click', function() {
            document.getElementById('listViewContainer').style.display = 'none';
            document.getElementById('gridViewContainer').style.display = 'flex';
            this.classList.add('active');
            document.getElementById('listView').classList.remove('active');
        });
    </script>
</body>
</html>
