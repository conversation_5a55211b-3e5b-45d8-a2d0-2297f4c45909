// الوظائف الأساسية للموقع

// تهيئة الموقع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeTooltips();
    initializeCharts();
    initializeDataTables();
    initializeFormValidation();
});

// تهيئة التلميحات
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تهيئة الرسوم البيانية
function initializeCharts() {
    // رسم بياني للنقرات اليومية
    if (document.getElementById('clicksChart')) {
        const ctx = document.getElementById('clicksChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
                datasets: [{
                    label: 'النقرات',
                    data: [12, 19, 3, 5, 2, 3, 10],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'النقرات اليومية'
                    }
                }
            }
        });
    }

    // رسم بياني للتحويلات
    if (document.getElementById('conversionsChart')) {
        const ctx = document.getElementById('conversionsChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'التحويلات',
                    data: [65, 59, 80, 81, 56, 55],
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'التحويلات الشهرية'
                    }
                }
            }
        });
    }
}

// تهيئة جداول البيانات
function initializeDataTables() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.data-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
            },
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']]
        });
    }
}

// تهيئة التحقق من النماذج
function initializeFormValidation() {
    // التحقق من نموذج تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            validateLoginForm();
        });
    }

    // التحقق من نموذج إضافة عرض
    const offerForm = document.getElementById('offerForm');
    if (offerForm) {
        offerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            validateOfferForm();
        });
    }
}

// التحقق من نموذج تسجيل الدخول
function validateLoginForm() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
        return false;
    }
    
    if (!isValidEmail(email)) {
        showAlert('يرجى إدخال بريد إلكتروني صحيح', 'danger');
        return false;
    }
    
    // إرسال البيانات
    submitLoginForm(email, password);
}

// التحقق من نموذج إضافة عرض
function validateOfferForm() {
    const title = document.getElementById('title').value;
    const payout = document.getElementById('payout').value;
    const network = document.getElementById('network').value;
    
    if (!title || !payout || !network) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
        return false;
    }
    
    if (isNaN(payout) || payout <= 0) {
        showAlert('يرجى إدخال قيمة عمولة صحيحة', 'danger');
        return false;
    }
    
    // إرسال البيانات
    submitOfferForm();
}

// التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// عرض الإشعارات
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer') || document.body;
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
    
    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// إرسال نموذج تسجيل الدخول
function submitLoginForm(email, password) {
    const formData = new FormData();
    formData.append('email', email);
    formData.append('password', password);
    
    fetch('auth/login_process.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = 'index.php';
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الاتصال', 'danger');
        console.error('Error:', error);
    });
}

// إرسال نموذج إضافة عرض
function submitOfferForm() {
    const form = document.getElementById('offerForm');
    const formData = new FormData(form);
    
    fetch('admin/offers/add_process.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إضافة العرض بنجاح', 'success');
            form.reset();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الاتصال', 'danger');
        console.error('Error:', error);
    });
}

// نسخ الرابط إلى الحافظة
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('تم نسخ الرابط بنجاح', 'success');
    }, function(err) {
        showAlert('فشل في نسخ الرابط', 'danger');
    });
}

// تحديث الإحصائيات في الوقت الفعلي
function updateStats() {
    fetch('api/stats.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('totalClicks').textContent = data.stats.clicks;
            document.getElementById('totalConversions').textContent = data.stats.conversions;
            document.getElementById('totalEarnings').textContent = '$' + data.stats.earnings;
        }
    })
    .catch(error => {
        console.error('Error updating stats:', error);
    });
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(updateStats, 30000);

// تصدير البيانات
function exportData(type, format) {
    const url = `api/export.php?type=${type}&format=${format}`;
    window.open(url, '_blank');
}

// تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// تحميل المزيد من البيانات
function loadMore(page, container) {
    fetch(`api/load_more.php?page=${page}`)
    .then(response => response.text())
    .then(html => {
        document.getElementById(container).innerHTML += html;
    })
    .catch(error => {
        console.error('Error loading more data:', error);
    });
}
