<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

// جلب معلومات المستخدم والرصيد
$user_query = "SELECT balance, total_earnings, payment_method, payment_details FROM users WHERE id = :user_id";
$user_stmt = $db->prepare($user_query);
$user_stmt->bindParam(':user_id', $user_id);
$user_stmt->execute();
$user_info = $user_stmt->fetch(PDO::FETCH_ASSOC);

// معالجة طلب سحب جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_payout'])) {
    $amount = floatval($_POST['amount']);
    $method = sanitize($_POST['method']);
    $payment_details = sanitize($_POST['payment_details']);
    $notes = sanitize($_POST['notes']);
    
    $error_message = '';
    
    // التحقق من المبلغ
    if ($amount < MIN_PAYOUT) {
        $error_message = 'الحد الأدنى للسحب هو ' . CURRENCY_SYMBOL . MIN_PAYOUT;
    } elseif ($amount > $user_info['balance']) {
        $error_message = 'المبلغ المطلوب أكبر من رصيدك المتاح';
    } elseif (empty($payment_details)) {
        $error_message = 'يرجى إدخال تفاصيل الدفع';
    } else {
        try {
            // إدراج طلب السحب
            $payout_query = "INSERT INTO payments (user_id, amount, method, payment_details, notes, status) 
                            VALUES (:user_id, :amount, :method, :payment_details, :notes, 'pending')";
            
            $payout_stmt = $db->prepare($payout_query);
            $payout_stmt->bindParam(':user_id', $user_id);
            $payout_stmt->bindParam(':amount', $amount);
            $payout_stmt->bindParam(':method', $method);
            $payout_stmt->bindParam(':payment_details', $payment_details);
            $payout_stmt->bindParam(':notes', $notes);
            $payout_stmt->execute();
            
            // خصم المبلغ من الرصيد
            $balance_query = "UPDATE users SET balance = balance - :amount WHERE id = :user_id";
            $balance_stmt = $db->prepare($balance_query);
            $balance_stmt->bindParam(':amount', $amount);
            $balance_stmt->bindParam(':user_id', $user_id);
            $balance_stmt->execute();
            
            // تسجيل النشاط
            $activity_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                              VALUES (:user_id, 'payout_request', :description, :data)";
            
            $activity_description = "طلب سحب بمبلغ " . CURRENCY_SYMBOL . number_format($amount, 2);
            $activity_data = json_encode([
                'amount' => $amount,
                'method' => $method,
                'payment_details' => $payment_details
            ]);

            $activity_stmt = $db->prepare($activity_query);
            $activity_stmt->bindParam(':user_id', $user_id);
            $activity_stmt->bindParam(':description', $activity_description);
            $activity_stmt->bindParam(':data', $activity_data);
            $activity_stmt->execute();
            
            $success_message = 'تم إرسال طلب السحب بنجاح! سيتم مراجعته خلال 24-48 ساعة';
            
            // تحديث معلومات المستخدم
            $user_info['balance'] -= $amount;
            
        } catch (PDOException $e) {
            logError("خطأ في طلب السحب: " . $e->getMessage());
            $error_message = 'حدث خطأ في إرسال طلب السحب';
        }
    }
}

// جلب تاريخ المدفوعات
$payments_query = "SELECT * FROM payments WHERE user_id = :user_id ORDER BY requested_at DESC";
$payments_stmt = $db->prepare($payments_query);
$payments_stmt->bindParam(':user_id', $user_id);
$payments_stmt->execute();
$payments = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب إحصائيات الأرباح الشهرية
$monthly_earnings_query = "SELECT 
    DATE_FORMAT(date, '%Y-%m') as month,
    SUM(earnings) as earnings
    FROM daily_stats 
    WHERE user_id = :user_id 
    GROUP BY DATE_FORMAT(date, '%Y-%m')
    ORDER BY month DESC
    LIMIT 12";

$monthly_stmt = $db->prepare($monthly_earnings_query);
$monthly_stmt->bindParam(':user_id', $user_id);
$monthly_stmt->execute();
$monthly_earnings = $monthly_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المدفوعات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">المدفوعات والأرباح</h1>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- بطاقات الأرصدة -->
                <div class="row mb-4">
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            الرصيد المتاح
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo CURRENCY_SYMBOL . number_format($user_info['balance'], 2); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-wallet fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي الأرباح
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo CURRENCY_SYMBOL . number_format($user_info['total_earnings'], 2); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            الحد الأدنى للسحب
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo CURRENCY_SYMBOL . number_format(MIN_PAYOUT, 2); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-hand-holding-usd fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- طلب سحب جديد -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">طلب سحب جديد</h6>
                            </div>
                            <div class="card-body">
                                <?php if ($user_info['balance'] >= MIN_PAYOUT): ?>
                                    <form method="POST">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">المبلغ</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                                <input type="number" class="form-control" id="amount" name="amount" 
                                                       min="<?php echo MIN_PAYOUT; ?>" 
                                                       max="<?php echo $user_info['balance']; ?>" 
                                                       step="0.01" required>
                                            </div>
                                            <small class="text-muted">
                                                الحد الأدنى: <?php echo CURRENCY_SYMBOL . MIN_PAYOUT; ?> | 
                                                المتاح: <?php echo CURRENCY_SYMBOL . number_format($user_info['balance'], 2); ?>
                                            </small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="method" class="form-label">طريقة الدفع</label>
                                            <select class="form-control" id="method" name="method" required>
                                                <option value="">اختر طريقة الدفع</option>
                                                <option value="paypal" <?php echo $user_info['payment_method'] == 'paypal' ? 'selected' : ''; ?>>PayPal</option>
                                                <option value="bank_transfer" <?php echo $user_info['payment_method'] == 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                                                <option value="payoneer" <?php echo $user_info['payment_method'] == 'payoneer' ? 'selected' : ''; ?>>Payoneer</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="payment_details" class="form-label">تفاصيل الدفع</label>
                                            <textarea class="form-control" id="payment_details" name="payment_details" 
                                                      rows="3" required placeholder="أدخل تفاصيل الدفع (بريد PayPal، رقم الحساب البنكي، إلخ)"><?php echo htmlspecialchars($user_info['payment_details']); ?></textarea>
                                        </div>

                                        <div class="mb-3">
                                            <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                                            <textarea class="form-control" id="notes" name="notes" 
                                                      rows="2" placeholder="أي ملاحظات إضافية"></textarea>
                                        </div>

                                        <button type="submit" name="request_payout" class="btn btn-success w-100">
                                            <i class="fas fa-paper-plane me-2"></i>
                                            إرسال طلب السحب
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-exclamation-circle fa-3x text-warning mb-3"></i>
                                        <h5>رصيدك أقل من الحد الأدنى للسحب</h5>
                                        <p class="text-muted">
                                            تحتاج إلى <?php echo CURRENCY_SYMBOL . number_format(MIN_PAYOUT - $user_info['balance'], 2); ?> إضافية للوصول للحد الأدنى
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- رسم بياني للأرباح -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">الأرباح الشهرية</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="earningsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تاريخ المدفوعات -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">تاريخ المدفوعات</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($payments)): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>المبلغ</th>
                                            <th>الطريقة</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الطلب</th>
                                            <th>تاريخ المعالجة</th>
                                            <th>الملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payments as $payment): ?>
                                        <tr>
                                            <td><?php echo $payment['id']; ?></td>
                                            <td><?php echo CURRENCY_SYMBOL . number_format($payment['amount'], 2); ?></td>
                                            <td>
                                                <?php
                                                $methods = [
                                                    'paypal' => 'PayPal',
                                                    'bank_transfer' => 'تحويل بنكي',
                                                    'payoneer' => 'Payoneer'
                                                ];
                                                echo $methods[$payment['method']] ?? $payment['method'];
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'pending' => 'warning',
                                                    'processing' => 'info',
                                                    'completed' => 'success',
                                                    'failed' => 'danger',
                                                    'cancelled' => 'secondary'
                                                ];
                                                $status_text = [
                                                    'pending' => 'قيد الانتظار',
                                                    'processing' => 'قيد المعالجة',
                                                    'completed' => 'مكتمل',
                                                    'failed' => 'فشل',
                                                    'cancelled' => 'ملغي'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_classes[$payment['status']]; ?>">
                                                    <?php echo $status_text[$payment['status']]; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($payment['requested_at'])); ?></td>
                                            <td>
                                                <?php echo $payment['processed_at'] ? date('Y-m-d H:i', strtotime($payment['processed_at'])) : '-'; ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($payment['notes'] ?: '-'); ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                <h5>لا توجد مدفوعات سابقة</h5>
                                <p class="text-muted">ستظهر هنا جميع طلبات السحب التي قمت بإرسالها</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // رسم بياني للأرباح الشهرية
        const ctx = document.getElementById('earningsChart').getContext('2d');
        const monthlyData = <?php echo json_encode(array_reverse($monthly_earnings)); ?>;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: monthlyData.map(item => item.month),
                datasets: [{
                    label: 'الأرباح الشهرية',
                    data: monthlyData.map(item => parseFloat(item.earnings)),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'الأرباح الشهرية'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '<?php echo CURRENCY_SYMBOL; ?>' + value.toFixed(2);
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
