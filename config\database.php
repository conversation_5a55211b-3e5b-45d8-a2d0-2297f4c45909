<?php
/**
 * إعدادات قاعدة البيانات
 */

class Database {
    private $host = 'sql303.infinityfree.com';
    private $db_name = 'if0_39395085_q12';
    private $username = 'if0_39395085';
    private $password = 'Qweeee12';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}
?>
