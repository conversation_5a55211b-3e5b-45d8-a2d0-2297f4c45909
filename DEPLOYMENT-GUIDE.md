# دليل رفع المشروع على InfinityFree

## معلومات الاستضافة
- **الرابط**: https://q12.ct.ws
- **خادم قاعدة البيانات**: sql303.infinityfree.com
- **اسم قاعدة البيانات**: if0_39395085_q12
- **اسم المستخدم**: if0_39395085
- **كلمة المرور**: Qweeee12

## خطوات الرفع

### 1. رفع الملفات
1. اضغط جميع ملفات المشروع في ملف ZIP
2. اذهب إلى لوحة تحكم InfinityFree
3. افتح File Manager
4. ارفع الملف المضغوط إلى مجلد `htdocs`
5. استخرج الملفات في `htdocs`

### 2. إعداد قاعدة البيانات
1. اذهب إلى MySQL Databases في لوحة التحكم
2. تأكد من إنشاء قاعدة البيانات `if0_39395085_q12`
3. سجل معلومات الاتصال

### 3. تشغيل التثبيت
1. اذهب إلى: https://q12.ct.ws/install.php
2. أدخل معلومات قاعدة البيانات:
   - **خادم قاعدة البيانات**: sql303.infinityfree.com
   - **اسم قاعدة البيانات**: if0_39395085_q12
   - **اسم المستخدم**: if0_39395085
   - **كلمة المرور**: Qweeee12
3. أدخل معلومات المدير:
   - **البريد الإلكتروني**: <EMAIL>
   - **كلمة المرور**: admin123 (غيرها لاحقاً)
4. **رابط الموقع**: https://q12.ct.ws
5. اضغط "بدء التثبيت"

### 4. تسجيل الدخول
1. اذهب إلى: https://q12.ct.ws/auth/login.php
2. سجل دخول بـ:
   - **البريد**: <EMAIL>
   - **كلمة المرور**: admin123

### 5. إعدادات ما بعد التثبيت

#### أ. تغيير كلمة مرور المدير
1. اذهب إلى الملف الشخصي
2. غير كلمة المرور إلى كلمة قوية

#### ب. إعداد Postback
1. اذهب إلى الإدارة > Postback
2. عيّن كلمة مرور قوية للـ Postback
3. أضف IPs المسموحة:
   ```
   ************
   127.0.0.1
   ```

#### ج. إضافة الشبكات
1. اذهب إلى الإدارة > الشبكات
2. أضف CPALead:
   - **الاسم**: CPALead
   - **الموقع**: https://cpalead.com
   - **Postback URL**: https://q12.ct.ws/tracking/postback.php

### 6. اختبار النظام

#### أ. اختبار Postback
1. اذهب إلى: https://q12.ct.ws/admin/postback/test.php
2. جرب اختبار بسيط
3. تأكد من عمل النظام

#### ب. إضافة عرض تجريبي
1. اذهب إلى الإدارة > العروض
2. أضف عرض تجريبي
3. أنشئ رابط تتبع
4. اختبر النقر والتحويل

### 7. URLs مهمة

- **الموقع الرئيسي**: https://q12.ct.ws
- **تسجيل الدخول**: https://q12.ct.ws/auth/login.php
- **لوحة الإدارة**: https://q12.ct.ws/admin/dashboard.php
- **Postback URL**: https://q12.ct.ws/tracking/postback.php
- **API**: https://q12.ct.ws/api/stats.php

### 8. إعدادات CPALead

#### Postback URL للـ CPALead:
```
https://q12.ct.ws/tracking/postback.php?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&payout={payout}&lead_id={lead_id}&country_iso={country_iso}&password=YOUR_PASSWORD&virtual_currency={virtual_currency}
```

#### مثال على رابط التتبع:
```
https://q12.ct.ws/tracking/click.php?t=TRACKING_ID&subid=YOUR_SUBID
```

### 9. نصائح للاستضافة المجانية

#### أ. قيود InfinityFree:
- **مساحة التخزين**: 5GB
- **عرض النطاق**: غير محدود
- **قواعد البيانات**: 400 قاعدة بيانات
- **حجم قاعدة البيانات**: 1GB لكل قاعدة

#### ب. تحسينات الأداء:
- استخدم ضغط الملفات (.htaccess موجود)
- قلل من استعلامات قاعدة البيانات
- استخدم التخزين المؤقت عند الإمكان

#### ج. النسخ الاحتياطي:
- اعمل نسخة احتياطية من قاعدة البيانات أسبوعياً
- احفظ نسخة من الملفات محلياً

### 10. استكشاف الأخطاء

#### أ. مشاكل شائعة:
- **خطأ 500**: تحقق من ملف .htaccess
- **خطأ قاعدة البيانات**: تأكد من معلومات الاتصال
- **Postback لا يعمل**: تحقق من IP والكلمة السرية

#### ب. ملفات السجلات:
- **أخطاء PHP**: logs/php_errors.log
- **أخطاء النظام**: logs/error.log
- **نشاطات المستخدمين**: جدول activity_logs

### 11. الأمان

#### أ. تأمين الملفات:
- ملف .htaccess يحمي ملفات الإعدادات
- مجلد logs محمي من الوصول المباشر

#### ب. كلمات المرور:
- غير كلمة مرور المدير الافتراضية
- استخدم كلمة مرور قوية للـ Postback
- غير SECRET_KEY في config.php

### 12. الدعم
- **الوثائق**: docs/postback-documentation.md
- **أمثلة**: examples/postback-example.php
- **اختبار**: admin/postback/test.php

---

**ملاحظة مهمة**: تأكد من تغيير جميع كلمات المرور الافتراضية قبل الاستخدام في الإنتاج!
