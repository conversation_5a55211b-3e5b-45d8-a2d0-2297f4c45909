<?php
/**
 * إصلاح سريع للصفحة الرئيسية
 */

echo "<h2>🔧 إصلاح الصفحة الرئيسية</h2>";

try {
    require_once 'config/config.php';
    
    echo "<h3>1. إنشاء الجداول المطلوبة:</h3>";
    
    $database = new Database();
    $db = $database->getConnection();
    
    // إنشاء جدول الشبكات إذا لم يكن موجوداً
    $networks_table = "CREATE TABLE IF NOT EXISTS networks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        website VARCHAR(255),
        api_endpoint VARCHAR(255),
        api_key VARCHAR(255),
        api_secret VARCHAR(255),
        postback_url VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        commission_rate DECIMAL(5,2) DEFAULT 0.00,
        payment_terms TEXT,
        contact_info TEXT,
        logo VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($networks_table);
    echo "✅ جدول الشبكات جاهز<br>";
    
    // إنشاء جدول العروض إذا لم يكن موجوداً
    $offers_table = "CREATE TABLE IF NOT EXISTS offers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        network_id INT DEFAULT 1,
        external_id VARCHAR(100),
        title VARCHAR(255) NOT NULL,
        description TEXT,
        preview_url VARCHAR(500),
        payout DECIMAL(10,2) NOT NULL,
        type ENUM('cpa', 'cpl', 'cps', 'cpi', 'cpc') DEFAULT 'cpa',
        category VARCHAR(100),
        countries TEXT,
        allowed_traffic TEXT,
        restrictions TEXT,
        status ENUM('active', 'inactive', 'paused') DEFAULT 'active',
        cap_daily INT DEFAULT 0,
        cap_monthly INT DEFAULT 0,
        conversion_flow TEXT,
        tracking_url VARCHAR(500),
        external_offer_url TEXT,
        external_image_url TEXT,
        image VARCHAR(255),
        is_external BOOLEAN DEFAULT FALSE,
        external_tracking BOOLEAN DEFAULT FALSE,
        start_date DATE,
        end_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_type (type),
        INDEX idx_is_external (is_external),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($offers_table);
    echo "✅ جدول العروض جاهز<br>";
    
    // إنشاء جدول النقرات إذا لم يكن موجوداً
    $clicks_table = "CREATE TABLE IF NOT EXISTS clicks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tracking_id VARCHAR(32) NOT NULL,
        user_id INT NOT NULL,
        offer_id INT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        referer VARCHAR(500),
        country VARCHAR(50),
        city VARCHAR(100),
        device_type ENUM('desktop', 'mobile', 'tablet'),
        browser VARCHAR(50),
        os VARCHAR(50),
        sub_id VARCHAR(100),
        sub_id2 VARCHAR(100),
        sub_id3 VARCHAR(100),
        source VARCHAR(100),
        campaign VARCHAR(100),
        idfa VARCHAR(100),
        gaid VARCHAR(100),
        is_unique BOOLEAN DEFAULT TRUE,
        clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_tracking_id (tracking_id),
        INDEX idx_user_offer (user_id, offer_id),
        INDEX idx_clicked_at (clicked_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($clicks_table);
    echo "✅ جدول النقرات جاهز<br>";
    
    echo "<h3>2. إدراج البيانات الأساسية:</h3>";
    
    // إدراج شبكة افتراضية
    $network_check = $db->query("SELECT COUNT(*) FROM networks")->fetchColumn();
    if ($network_check == 0) {
        $insert_network = "INSERT INTO networks (name, description, website, status) VALUES 
        ('CPALead', 'شبكة CPA رائدة مع عروض عالية الجودة', 'https://cpalead.com', 'active'),
        ('شبكة محلية', 'شبكة محلية للعروض المخصصة', 'http://localhost', 'active')";
        $db->exec($insert_network);
        echo "✅ تم إدراج الشبكات الأساسية<br>";
    } else {
        echo "✅ الشبكات موجودة بالفعل<br>";
    }
    
    // إدراج عروض تجريبية
    $offers_check = $db->query("SELECT COUNT(*) FROM offers")->fetchColumn();
    if ($offers_check == 0) {
        $insert_offers = "INSERT INTO offers (network_id, title, description, payout, type, category, countries, status) VALUES 
        (1, 'عرض تجريبي 1', 'عرض تجريبي للاختبار', 5.00, 'cpa', 'تجريبي', 'جميع البلدان', 'active'),
        (1, 'عرض تجريبي 2', 'عرض آخر للاختبار', 10.00, 'cpl', 'تجريبي', 'جميع البلدان', 'active'),
        (2, 'عرض محلي', 'عرض محلي للاختبار', 15.00, 'cpa', 'محلي', 'جميع البلدان', 'active')";
        $db->exec($insert_offers);
        echo "✅ تم إدراج العروض التجريبية<br>";
    } else {
        echo "✅ العروض موجودة بالفعل<br>";
    }
    
    echo "<h3>3. إنشاء الملفات المطلوبة:</h3>";
    
    // إنشاء مجلد assets إذا لم يكن موجوداً
    if (!is_dir('assets')) {
        mkdir('assets', 0755, true);
        echo "✅ تم إنشاء مجلد assets<br>";
    }
    
    if (!is_dir('assets/css')) {
        mkdir('assets/css', 0755, true);
        echo "✅ تم إنشاء مجلد assets/css<br>";
    }
    
    if (!is_dir('assets/js')) {
        mkdir('assets/js', 0755, true);
        echo "✅ تم إنشاء مجلد assets/js<br>";
    }
    
    // إنشاء ملف CSS أساسي
    if (!file_exists('assets/css/style.css')) {
        $css_content = "/* ملف CSS أساسي */
.border-left-primary { border-left: 4px solid #007bff !important; }
.border-left-success { border-left: 4px solid #28a745 !important; }
.border-left-info { border-left: 4px solid #17a2b8 !important; }
.border-left-warning { border-left: 4px solid #ffc107 !important; }
.text-gray-800 { color: #5a5c69 !important; }
.text-gray-300 { color: #dddfeb !important; }
.temp-mail-card { padding: 15px; border: 1px solid #e3e6f0; border-radius: 8px; }
.temp-mail-icon { width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; }
.sidebar .nav-link.active { background-color: #007bff; color: white; }
.sidebar .nav-link { color: #333; }
.sidebar .nav-link:hover { background-color: #f8f9fa; }";
        
        file_put_contents('assets/css/style.css', $css_content);
        echo "✅ تم إنشاء ملف CSS<br>";
    }
    
    // إنشاء ملف JavaScript أساسي
    if (!file_exists('assets/js/main.js')) {
        $js_content = "// ملف JavaScript أساسي
console.log('CPA System loaded successfully');

// إخفاء التنبيهات تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            var closeBtn = alert.querySelector('.btn-close');
            if (closeBtn) {
                setTimeout(function() {
                    closeBtn.click();
                }, 5000);
            }
        });
    }, 1000);
});";
        
        file_put_contents('assets/js/main.js', $js_content);
        echo "✅ تم إنشاء ملف JavaScript<br>";
    }
    
    // إنشاء ملف footer.php إذا لم يكن موجوداً
    if (!file_exists('includes/footer.php')) {
        $footer_content = '<footer class="footer mt-auto py-3 bg-light">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                <span class="text-muted">&copy; ' . date('Y') . ' ' . (defined('SITE_NAME') ? SITE_NAME : 'CPA System') . '. جميع الحقوق محفوظة.</span>
            </div>
            <div class="col-md-6 text-end">
                <span class="text-muted">نسخة 1.0</span>
            </div>
        </div>
    </div>
</footer>';
        
        file_put_contents('includes/footer.php', $footer_content);
        echo "✅ تم إنشاء ملف footer.php<br>";
    }
    
    echo "<hr>";
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    
    echo "<h4>🧪 اختبار النظام:</h4>";
    echo "<p><a href='test-homepage.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 اختبار الصفحة الرئيسية</a>";
    echo "<a href='auth/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 تسجيل الدخول</a>";
    echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 الصفحة الرئيسية</a></p>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
    echo "<h5>✅ النظام جاهز للاستخدام</h5>";
    echo "<p>تم إنشاء جميع الجداول والملفات المطلوبة. يمكنك الآن:</p>";
    echo "<ol>";
    echo "<li>تسجيل الدخول باستخدام أي من الحسابات المتاحة</li>";
    echo "<li>الوصول للصفحة الرئيسية</li>";
    echo "<li>استكشاف العروض المتاحة</li>";
    echo "<li>إدارة النظام (للمدراء)</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>❌ خطأ في الإصلاح:</h3>";
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;'>";
    echo "<strong>رسالة الخطأ:</strong> " . $e->getMessage() . "<br>";
    echo "</div>";
    
    echo "<h4>🔧 خطوات الإصلاح اليدوي:</h4>";
    echo "<ol>";
    echo "<li>تأكد من عمل قاعدة البيانات: <a href='test-database-final.php'>اختبار قاعدة البيانات</a></li>";
    echo "<li>تأكد من وجود المستخدمين: <a href='fix-users.php'>إصلاح المستخدمين</a></li>";
    echo "<li>اختبر تسجيل الدخول: <a href='test-login.php'>اختبار تسجيل الدخول</a></li>";
    echo "</ol>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3, h4, h5 {
    color: #333;
}
a {
    text-decoration: none;
}
a:hover {
    opacity: 0.8;
}
</style>
