<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-check-circle me-2"></i>الخطوة 1: فحص المتطلبات</h4>
    </div>
    <div class="card-body">
        <p class="text-muted">سيتم فحص متطلبات النظام للتأكد من إمكانية التثبيت</p>
        
        <?php
        $requirements = [
            'PHP Version >= 7.4' => [
                'status' => version_compare(PHP_VERSION, '7.4.0', '>='),
                'current' => PHP_VERSION,
                'required' => '7.4.0+'
            ],
            'PDO Extension' => [
                'status' => extension_loaded('pdo'),
                'current' => extension_loaded('pdo') ? 'مثبت' : 'غير مثبت',
                'required' => 'مطلوب'
            ],
            'PDO MySQL Extension' => [
                'status' => extension_loaded('pdo_mysql'),
                'current' => extension_loaded('pdo_mysql') ? 'مثبت' : 'غير مثبت',
                'required' => 'مطلوب'
            ],
            'cURL Extension' => [
                'status' => extension_loaded('curl'),
                'current' => extension_loaded('curl') ? 'مثبت' : 'غير مثبت',
                'required' => 'مطلوب'
            ],
            'JSON Extension' => [
                'status' => extension_loaded('json'),
                'current' => extension_loaded('json') ? 'مثبت' : 'غير مثبت',
                'required' => 'مطلوب'
            ],
            'OpenSSL Extension' => [
                'status' => extension_loaded('openssl'),
                'current' => extension_loaded('openssl') ? 'مثبت' : 'غير مثبت',
                'required' => 'مطلوب'
            ],
            'Config Directory Writable' => [
                'status' => is_writable('config/'),
                'current' => is_writable('config/') ? 'قابل للكتابة' : 'غير قابل للكتابة',
                'required' => 'قابل للكتابة'
            ],
            'Logs Directory' => [
                'status' => is_writable('logs/') || mkdir('logs/', 0755, true),
                'current' => is_dir('logs/') && is_writable('logs/') ? 'موجود وقابل للكتابة' : 'سيتم إنشاؤه',
                'required' => 'قابل للكتابة'
            ],
            'Cache Directory' => [
                'status' => is_writable('cache/') || mkdir('cache/', 0755, true),
                'current' => is_dir('cache/') && is_writable('cache/') ? 'موجود وقابل للكتابة' : 'سيتم إنشاؤه',
                'required' => 'قابل للكتابة'
            ],
            'Backups Directory' => [
                'status' => is_writable('backups/') || mkdir('backups/', 0755, true),
                'current' => is_dir('backups/') && is_writable('backups/') ? 'موجود وقابل للكتابة' : 'سيتم إنشاؤه',
                'required' => 'قابل للكتابة'
            ]
        ];
        
        $all_passed = true;
        ?>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>المتطلب</th>
                        <th>الحالة الحالية</th>
                        <th>المطلوب</th>
                        <th>النتيجة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($requirements as $requirement => $info): ?>
                        <?php if (!$info['status']) $all_passed = false; ?>
                        <tr>
                            <td><?php echo $requirement; ?></td>
                            <td><?php echo $info['current']; ?></td>
                            <td><?php echo $info['required']; ?></td>
                            <td>
                                <?php if ($info['status']): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i> متوفر</span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i> غير متوفر</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($all_passed): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>ممتاز!</strong> جميع المتطلبات متوفرة. يمكنك المتابعة للخطوة التالية.
            </div>
            
            <form method="POST">
                <div class="text-end">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>المتابعة للخطوة التالية
                    </button>
                </div>
            </form>
        <?php else: ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير!</strong> بعض المتطلبات غير متوفرة. يرجى إصلاحها قبل المتابعة.
            </div>
            
            <div class="text-end">
                <a href="installer.php?step=1" class="btn btn-warning">
                    <i class="fas fa-refresh me-2"></i>إعادة الفحص
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
