<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$ipProtection = new IPProtection($database);

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $settings = [
        'ip_protection_enabled' => isset($_POST['ip_protection_enabled']) ? '1' : '0',
        'ip_protection_days' => intval($_POST['ip_protection_days']),
        'ip_protection_per_offer' => isset($_POST['ip_protection_per_offer']) ? '1' : '0',
        'ip_protection_global' => isset($_POST['ip_protection_global']) ? '1' : '0',
        'ip_protection_whitelist' => sanitize($_POST['ip_protection_whitelist']),
        'ip_protection_reset_hour' => intval($_POST['ip_protection_reset_hour'])
    ];
    
    if ($ipProtection->updateSettings($settings)) {
        $_SESSION['success'] = 'تم تحديث إعدادات حماية IP بنجاح!';
    } else {
        $_SESSION['error'] = 'حدث خطأ في تحديث الإعدادات';
    }
    
    header('Location: ip-protection.php');
    exit();
}

// معالجة الإجراءات
if (isset($_GET['action'])) {
    $action = sanitize($_GET['action']);
    
    switch ($action) {
        case 'clean_expired':
            $cleaned = $ipProtection->cleanExpiredProtections();
            $_SESSION['success'] = "تم حذف {$cleaned} سجل منتهي الصلاحية";
            break;
            
        case 'reset_all':
            $reset = $ipProtection->resetAllProtections();
            $_SESSION['success'] = "تم إعادة تعيين {$reset} سجل حماية";
            break;
            
        case 'reset_offer':
            $offer_id = intval($_GET['offer_id']);
            if ($offer_id > 0) {
                $reset = $ipProtection->resetOfferProtection($offer_id);
                $_SESSION['success'] = "تم إعادة تعيين {$reset} سجل للعرض";
            }
            break;
    }
    
    header('Location: ip-protection.php');
    exit();
}

// جلب الإحصائيات
$stats = $ipProtection->getProtectionStats();

// جلب العروض للإدارة
$offers_query = "SELECT id, title FROM offers ORDER BY title";
$offers_stmt = $db->prepare($offers_query);
$offers_stmt->execute();
$offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب أحدث سجلات الحماية
$recent_query = "SELECT ip.*, o.title as offer_title, u.username 
                FROM ip_protection ip
                LEFT JOIN offers o ON ip.offer_id = o.id
                LEFT JOIN users u ON ip.user_id = u.id
                WHERE ip.blocked_until > NOW()
                ORDER BY ip.created_at DESC
                LIMIT 20";
$recent_stmt = $db->prepare($recent_query);
$recent_stmt->execute();
$recent_protections = $recent_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات حماية IP - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إعدادات حماية IP</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="?action=clean_expired" class="btn btn-outline-warning btn-sm" 
                               onclick="return confirm('هل تريد حذف السجلات المنتهية الصلاحية؟')">
                                <i class="fas fa-broom me-1"></i>تنظيف منتهية الصلاحية
                            </a>
                            <a href="?action=reset_all" class="btn btn-outline-danger btn-sm"
                               onclick="return confirm('هل تريد إعادة تعيين جميع سجلات الحماية؟')">
                                <i class="fas fa-redo me-1"></i>إعادة تعيين الكل
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- الإحصائيات -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إحصائيات الحماية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-12 mb-3">
                                        <h4 class="text-primary"><?php echo number_format($stats['active_ips'] ?? 0); ?></h4>
                                        <small class="text-muted">IPs محمية حالياً</small>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <h4 class="text-warning"><?php echo number_format($stats['total_protections'] ?? 0); ?></h4>
                                        <small class="text-muted">إجمالي سجلات الحماية</small>
                                    </div>
                                </div>
                                
                                <?php if (!empty($stats['by_type'])): ?>
                                    <hr>
                                    <h6>حسب النوع:</h6>
                                    <?php foreach ($stats['by_type'] as $type): ?>
                                        <div class="d-flex justify-content-between">
                                            <span><?php echo $type['protection_type'] === 'offer' ? 'حماية العروض' : 'حماية عامة'; ?></span>
                                            <strong><?php echo number_format($type['count']); ?></strong>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإعدادات -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إعدادات الحماية</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="ip_protection_enabled" 
                                                       name="ip_protection_enabled" 
                                                       <?php echo ($stats['settings']['ip_protection_enabled'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="ip_protection_enabled">
                                                    <strong>تفعيل حماية IP</strong>
                                                </label>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="ip_protection_days" class="form-label">عدد أيام الحماية</label>
                                                <input type="number" class="form-control" id="ip_protection_days" 
                                                       name="ip_protection_days" min="1" max="365"
                                                       value="<?php echo htmlspecialchars($stats['settings']['ip_protection_days'] ?? '7'); ?>">
                                                <small class="text-muted">العرض يختفي لهذه المدة بعد النقر عليه</small>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="ip_protection_reset_hour" class="form-label">ساعة إعادة التعيين</label>
                                                <select class="form-control" id="ip_protection_reset_hour" name="ip_protection_reset_hour">
                                                    <?php for ($i = 0; $i < 24; $i++): ?>
                                                        <option value="<?php echo $i; ?>" 
                                                                <?php echo ($stats['settings']['ip_protection_reset_hour'] ?? '0') == $i ? 'selected' : ''; ?>>
                                                            <?php echo sprintf('%02d:00', $i); ?>
                                                        </option>
                                                    <?php endfor; ?>
                                                </select>
                                                <small class="text-muted">ساعة إعادة تعيين الحماية يومياً</small>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="ip_protection_per_offer" 
                                                       name="ip_protection_per_offer"
                                                       <?php echo ($stats['settings']['ip_protection_per_offer'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="ip_protection_per_offer">
                                                    حماية لكل عرض منفصل
                                                </label>
                                            </div>
                                            
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="ip_protection_global" 
                                                       name="ip_protection_global"
                                                       <?php echo ($stats['settings']['ip_protection_global'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="ip_protection_global">
                                                    حماية عامة لجميع العروض
                                                </label>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="ip_protection_whitelist" class="form-label">IPs مستثناة من الحماية</label>
                                                <textarea class="form-control" id="ip_protection_whitelist" 
                                                          name="ip_protection_whitelist" rows="3"
                                                          placeholder="***********, ********"><?php echo htmlspecialchars($stats['settings']['ip_protection_whitelist'] ?? ''); ?></textarea>
                                                <small class="text-muted">IPs مفصولة بفاصلة، هذه العناوين لن تخضع للحماية</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- سجلات الحماية الأخيرة -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">سجلات الحماية النشطة</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_protections)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-shield-alt fa-3x mb-3"></i>
                                <p>لا توجد سجلات حماية نشطة حالياً</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>IP Address</th>
                                            <th>العرض</th>
                                            <th>المستخدم</th>
                                            <th>النوع</th>
                                            <th>عدد النقرات</th>
                                            <th>محمي حتى</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_protections as $protection): ?>
                                            <tr>
                                                <td><code><?php echo htmlspecialchars($protection['ip_address']); ?></code></td>
                                                <td>
                                                    <?php if ($protection['offer_title']): ?>
                                                        <?php echo htmlspecialchars($protection['offer_title']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">حماية عامة</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($protection['username'] ?: 'غير محدد'); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $protection['protection_type'] === 'offer' ? 'primary' : 'warning'; ?>">
                                                        <?php echo $protection['protection_type'] === 'offer' ? 'عرض' : 'عام'; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo number_format($protection['click_count']); ?></td>
                                                <td>
                                                    <?php 
                                                    $remaining = strtotime($protection['blocked_until']) - time();
                                                    if ($remaining > 0) {
                                                        $hours = floor($remaining / 3600);
                                                        $minutes = floor(($remaining % 3600) / 60);
                                                        echo "{$hours}س {$minutes}د";
                                                    } else {
                                                        echo '<span class="text-danger">منتهي</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($protection['offer_id']): ?>
                                                        <a href="?action=reset_offer&offer_id=<?php echo $protection['offer_id']; ?>" 
                                                           class="btn btn-sm btn-outline-danger"
                                                           onclick="return confirm('إعادة تعيين حماية هذا العرض؟')">
                                                            <i class="fas fa-undo"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        // تحديث الصفحة كل دقيقة لإظهار الوقت المتبقي
        setInterval(function() {
            location.reload();
        }, 60000);
    </script>
</body>
</html>
