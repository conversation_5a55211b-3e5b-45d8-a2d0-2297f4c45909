<?php
/**
 * ملف تنظيف ملفات التثبيت
 * يجب تشغيله بعد اكتمال التثبيت لحذف ملفات التثبيت لأسباب أمنية
 */

// التحقق من وجود ملف القفل
if (!file_exists('config/installed.lock')) {
    die('النظام غير مثبت بعد. لا يمكن تنظيف ملفات التثبيت.');
}

$files_to_delete = [
    'installer.php',
    'install.php',
    'cleanup-installer.php', // هذا الملف نفسه
    'installer-steps/',
];

$deleted_files = [];
$failed_files = [];

foreach ($files_to_delete as $file) {
    if (is_file($file)) {
        if (unlink($file)) {
            $deleted_files[] = $file;
        } else {
            $failed_files[] = $file;
        }
    } elseif (is_dir($file)) {
        if (deleteDirectory($file)) {
            $deleted_files[] = $file;
        } else {
            $failed_files[] = $file;
        }
    }
}

function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف ملفات التثبيت</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4><i class="fas fa-broom me-2"></i>تنظيف ملفات التثبيت</h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($deleted_files)): ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>تم حذف الملفات التالية بنجاح:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($deleted_files as $file): ?>
                                        <li><code><?php echo htmlspecialchars($file); ?></code></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($failed_files)): ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>فشل في حذف الملفات التالية:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($failed_files as $file): ?>
                                        <li><code><?php echo htmlspecialchars($file); ?></code></li>
                                    <?php endforeach; ?>
                                </ul>
                                <p class="mt-2 mb-0">يرجى حذف هذه الملفات يدوياً من الخادم.</p>
                            </div>
                        <?php endif; ?>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>تم تنظيف ملفات التثبيت</h6>
                            <p class="mb-0">تم حذف ملفات التثبيت لأسباب أمنية. النظام جاهز للاستخدام الآن.</p>
                        </div>

                        <div class="text-center">
                            <a href="index.php" class="btn btn-primary me-2">
                                <i class="fas fa-home me-1"></i>الصفحة الرئيسية
                            </a>
                            <a href="auth/login.php" class="btn btn-success">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
