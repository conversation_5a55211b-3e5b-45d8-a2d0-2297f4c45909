<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$usernameGenerator = new UsernameGenerator($database);

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitize($_POST['action'] ?? '');
    
    switch ($action) {
        case 'generate_bulk':
            $count = intval($_POST['count'] ?? 10);
            $count = min(max($count, 1), 100); // بين 1 و 100
            
            $generated_usernames = $usernameGenerator->generateMultipleUsernames($count);
            $_SESSION['generated_usernames'] = $generated_usernames;
            $_SESSION['success'] = "تم توليد {$count} اسم مستخدم جديد";
            break;
            
        case 'create_accounts':
            if (isset($_SESSION['generated_usernames']) && !empty($_SESSION['generated_usernames'])) {
                $created_count = 0;
                $default_password = $usernameGenerator->generateRandomPassword(8);
                
                foreach ($_SESSION['generated_usernames'] as $username) {
                    if ($usernameGenerator->isUsernameAvailable($username)) {
                        try {
                            $hashed_password = password_hash($default_password, PASSWORD_DEFAULT);
                            $api_key = hash('sha256', uniqid(rand(), true));
                            
                            $insert_query = "INSERT INTO users (username, password, api_key, is_auto_generated, ip_address, user_agent) 
                                            VALUES (:username, :password, :api_key, 1, :ip_address, :user_agent)";
                            
                            $insert_stmt = $db->prepare($insert_query);
                            $insert_stmt->bindParam(':username', $username);
                            $insert_stmt->bindParam(':password', $hashed_password);
                            $insert_stmt->bindParam(':api_key', $api_key);
                            $insert_stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
                            $insert_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
                            
                            if ($insert_stmt->execute()) {
                                $created_count++;
                            }
                            
                        } catch (PDOException $e) {
                            logError("خطأ في إنشاء حساب تلقائي: " . $e->getMessage());
                        }
                    }
                }
                
                $_SESSION['success'] = "تم إنشاء {$created_count} حساب بكلمة المرور: {$default_password}";
                unset($_SESSION['generated_usernames']);
            }
            break;
    }
    
    header('Location: username-generator.php');
    exit();
}

// جلب الإحصائيات
$stats = $usernameGenerator->getGeneratedUsernamesStats();

// جلب أحدث المستخدمين المولدين تلقائياً
$recent_query = "SELECT username, created_at, login_count, last_login 
                FROM users 
                WHERE is_auto_generated = 1 
                ORDER BY created_at DESC 
                LIMIT 20";
$recent_stmt = $db->prepare($recent_query);
$recent_stmt->execute();
$recent_generated = $recent_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد أسماء المستخدمين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">مولد أسماء المستخدمين</h1>
                </div>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- الإحصائيات -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إحصائيات الأسماء المولدة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-12 mb-3">
                                        <h4 class="text-primary"><?php echo number_format($stats['total_generated'] ?? 0); ?></h4>
                                        <small class="text-muted">إجمالي الأسماء المولدة</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-success"><?php echo number_format($stats['generated_today'] ?? 0); ?></h5>
                                        <small class="text-muted">اليوم</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-info"><?php echo number_format($stats['generated_week'] ?? 0); ?></h5>
                                        <small class="text-muted">هذا الأسبوع</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- مولد الأسماء -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">توليد أسماء مستخدمين جديدة</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="generate_bulk">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="count" class="form-label">عدد الأسماء المطلوبة</label>
                                                <input type="number" class="form-control" id="count" name="count" 
                                                       value="10" min="1" max="100">
                                                <small class="text-muted">بين 1 و 100 اسم</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6 d-flex align-items-end">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-magic me-1"></i>توليد الأسماء
                                            </button>
                                        </div>
                                    </div>
                                </form>
                                
                                <?php if (isset($_SESSION['generated_usernames']) && !empty($_SESSION['generated_usernames'])): ?>
                                    <hr>
                                    <h6>الأسماء المولدة:</h6>
                                    <div class="row">
                                        <?php foreach ($_SESSION['generated_usernames'] as $username): ?>
                                            <div class="col-md-3 mb-2">
                                                <span class="badge bg-light text-dark p-2 w-100"><?php echo htmlspecialchars($username); ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="create_accounts">
                                            <button type="submit" class="btn btn-success" 
                                                    onclick="return confirm('هل تريد إنشاء حسابات لهذه الأسماء؟')">
                                                <i class="fas fa-user-plus me-1"></i>إنشاء حسابات
                                            </button>
                                        </form>
                                        <button type="button" class="btn btn-secondary" onclick="copyUsernames()">
                                            <i class="fas fa-copy me-1"></i>نسخ الأسماء
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أحدث الأسماء المولدة -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أحدث الأسماء المولدة تلقائياً</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_generated)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>لا توجد أسماء مولدة تلقائياً حتى الآن</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>اسم المستخدم</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>عدد تسجيلات الدخول</th>
                                            <th>آخر تسجيل دخول</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_generated as $user): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                                    <span class="badge bg-info ms-1">مولد تلقائياً</span>
                                                </td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $user['login_count'] > 0 ? 'success' : 'secondary'; ?>">
                                                        <?php echo number_format($user['login_count']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($user['last_login']): ?>
                                                        <?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">لم يسجل دخول</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($user['login_count'] > 0): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">لم يستخدم</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        function copyUsernames() {
            <?php if (isset($_SESSION['generated_usernames'])): ?>
                const usernames = <?php echo json_encode($_SESSION['generated_usernames']); ?>;
                const text = usernames.join('\n');
                
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(function() {
                        alert('تم نسخ الأسماء إلى الحافظة');
                    });
                } else {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('تم نسخ الأسماء إلى الحافظة');
                }
            <?php endif; ?>
        }
    </script>
</body>
</html>
