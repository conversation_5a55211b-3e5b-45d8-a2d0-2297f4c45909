<?php
require_once '../config/config.php';

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: register.php');
    exit();
}

// التحقق من البيانات المرسلة
$first_name = sanitize($_POST['first_name'] ?? '');
$last_name = sanitize($_POST['last_name'] ?? '');
$username = sanitize($_POST['username'] ?? '');
$email = sanitize($_POST['email'] ?? '');
$phone = sanitize($_POST['phone'] ?? '');
$country = sanitize($_POST['country'] ?? '');
$password = $_POST['password'] ?? '';
$confirm_password = $_POST['confirm_password'] ?? '';
$terms = isset($_POST['terms']);

// التحقق من وجود البيانات المطلوبة
if (empty($first_name) || empty($last_name) || empty($username) || empty($email) || empty($password) || empty($country)) {
    $_SESSION['error'] = 'يرجى ملء جميع الحقول المطلوبة';
    header('Location: register.php');
    exit();
}

// التحقق من الموافقة على الشروط
if (!$terms) {
    $_SESSION['error'] = 'يجب الموافقة على الشروط والأحكام';
    header('Location: register.php');
    exit();
}

// التحقق من صحة البريد الإلكتروني
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $_SESSION['error'] = 'يرجى إدخال بريد إلكتروني صحيح';
    header('Location: register.php');
    exit();
}

// التحقق من اسم المستخدم
if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $username)) {
    $_SESSION['error'] = 'اسم المستخدم يجب أن يكون 3-20 حرف ويحتوي على أحرف إنجليزية وأرقام و _ فقط';
    header('Location: register.php');
    exit();
}

// التحقق من كلمة المرور
if (strlen($password) < 8) {
    $_SESSION['error'] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    header('Location: register.php');
    exit();
}

// التحقق من تطابق كلمة المرور
if ($password !== $confirm_password) {
    $_SESSION['error'] = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    header('Location: register.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // التحقق من عدم وجود المستخدم مسبقاً
    $check_query = "SELECT id FROM users WHERE email = :email OR username = :username";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':email', $email);
    $check_stmt->bindParam(':username', $username);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        $_SESSION['error'] = 'البريد الإلكتروني أو اسم المستخدم مستخدم بالفعل';
        header('Location: register.php');
        exit();
    }

    // تشفير كلمة المرور
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // توليد مفتاح API
    $api_key = generateToken();

    // إدراج المستخدم الجديد
    $insert_query = "INSERT INTO users (username, email, password, first_name, last_name, phone, country, api_key, status) 
                     VALUES (:username, :email, :password, :first_name, :last_name, :phone, :country, :api_key, 'active')";
    
    $insert_stmt = $db->prepare($insert_query);
    $insert_stmt->bindParam(':username', $username);
    $insert_stmt->bindParam(':email', $email);
    $insert_stmt->bindParam(':password', $hashed_password);
    $insert_stmt->bindParam(':first_name', $first_name);
    $insert_stmt->bindParam(':last_name', $last_name);
    $insert_stmt->bindParam(':phone', $phone);
    $insert_stmt->bindParam(':country', $country);
    $insert_stmt->bindParam(':api_key', $api_key);
    
    if ($insert_stmt->execute()) {
        $user_id = $db->lastInsertId();
        
        // تسجيل النشاط
        $log_query = "INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) 
                      VALUES (:user_id, 'register', 'تسجيل حساب جديد', :ip, :user_agent)";
        $log_stmt = $db->prepare($log_query);
        $log_stmt->bindParam(':user_id', $user_id);
        $log_stmt->bindParam(':ip', $_SERVER['REMOTE_ADDR']);
        $log_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT']);
        $log_stmt->execute();

        $_SESSION['success'] = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
        header('Location: login.php');
        exit();
        
    } else {
        $_SESSION['error'] = 'حدث خطأ في إنشاء الحساب. يرجى المحاولة مرة أخرى';
        header('Location: register.php');
        exit();
    }

} catch (PDOException $e) {
    logError("خطأ في التسجيل: " . $e->getMessage());
    $_SESSION['error'] = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى';
    header('Location: register.php');
    exit();
}
?>
