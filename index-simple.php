<?php
/**
 * صفحة رئيسية مبسطة للاختبار
 */

require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// جلب معلومات المستخدم
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];
$role = $_SESSION['role'];

// جلب إحصائيات بسيطة
try {
    $stats_query = "SELECT COUNT(*) as total_offers FROM offers WHERE status = 'active'";
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    $offers_query = "SELECT id, title, payout, type FROM offers WHERE status = 'active' ORDER BY payout DESC LIMIT 5";
    $offers_stmt = $db->prepare($offers_query);
    $offers_stmt->execute();
    $offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $stats = ['total_offers' => 0];
    $offers = [];
    $error_message = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .border-left-primary { border-left: 4px solid #007bff !important; }
        .border-left-success { border-left: 4px solid #28a745 !important; }
        .border-left-info { border-left: 4px solid #17a2b8 !important; }
        .border-left-warning { border-left: 4px solid #ffc107 !important; }
        .text-gray-800 { color: #5a5c69 !important; }
        .text-gray-300 { color: #dddfeb !important; }
        .sidebar .nav-link.active { background-color: #007bff; color: white; }
        .sidebar .nav-link { color: #333; }
        .sidebar .nav-link:hover { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="index.php">
            <i class="fas fa-chart-line me-2"></i>
            <?php echo SITE_NAME; ?>
        </a>
        
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <div class="dropdown">
                    <a class="nav-link px-3 dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($username); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="offers/">
                                <i class="fas fa-bullhorn me-2"></i>
                                العروض
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">مرحباً <?php echo htmlspecialchars($username); ?>!</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <span class="badge bg-<?php echo $role == 'admin' ? 'danger' : 'primary'; ?> fs-6">
                                <?php echo $role == 'admin' ? '👑 مدير' : '👤 ناشر'; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذير</h5>
                        <p>حدث خطأ في تحميل البيانات: <?php echo htmlspecialchars($error_message); ?></p>
                        <p><a href="fix-homepage.php" class="btn btn-primary">🔧 إصلاح المشكلة</a></p>
                    </div>
                <?php endif; ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي العروض
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_offers']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            حالة النظام
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            ✅ يعمل
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            معرف المستخدم
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            #<?php echo $user_id; ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            الوقت
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo date('H:i'); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العروض المتاحة -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">العروض المتاحة</h6>
                    </div>
                    <div class="card-body">
                        <?php if (count($offers) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>اسم العرض</th>
                                            <th>العمولة</th>
                                            <th>النوع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($offers as $offer): ?>
                                        <tr>
                                            <td><?php echo $offer['id']; ?></td>
                                            <td><?php echo htmlspecialchars($offer['title']); ?></td>
                                            <td><?php echo CURRENCY_SYMBOL . number_format($offer['payout'], 2); ?></td>
                                            <td><?php echo htmlspecialchars($offer['type']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle me-2"></i>لا توجد عروض متاحة</h5>
                                <p>لم يتم العثور على عروض نشطة في النظام.</p>
                                <a href="fix-homepage.php" class="btn btn-primary">🔧 إضافة عروض تجريبية</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- أدوات الإدارة -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أدوات النظام</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="test-homepage.php" class="btn btn-info w-100">
                                    <i class="fas fa-vial me-1"></i>اختبار النظام
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="fix-homepage.php" class="btn btn-warning w-100">
                                    <i class="fas fa-wrench me-1"></i>إصلاح المشاكل
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="fix-users.php" class="btn btn-success w-100">
                                    <i class="fas fa-users me-1"></i>إدارة المستخدمين
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="index.php" class="btn btn-primary w-100">
                                    <i class="fas fa-home me-1"></i>الصفحة الكاملة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
