<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="index.php">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'offers') !== false ? 'active' : ''; ?>" href="offers/">
                    <i class="fas fa-bullhorn me-2"></i>
                    العروض
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'offerwall') !== false ? 'active' : ''; ?>" href="offerwall/">
                    <i class="fas fa-gift me-2"></i>
                    Offerwall
                    <span class="badge bg-success ms-2">جديد</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'temp-mail') !== false ? 'active' : ''; ?>" href="temp-mail.php">
                    <i class="fas fa-envelope me-2"></i>
                    البريد المؤقت
                    <span class="badge bg-info ms-2">مفيد</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'tracking') !== false ? 'active' : ''; ?>" href="tracking/">
                    <i class="fas fa-link me-2"></i>
                    التتبع
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'reports') !== false && !strpos($_SERVER['PHP_SELF'], 'username-stats') ? 'active' : ''; ?>" href="reports/">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير
                </a>
            </li>

            <?php if (isAdmin()): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'username-stats') !== false ? 'active' : ''; ?>" href="admin/reports/username-stats.php">
                    <i class="fas fa-chart-line me-2"></i>
                    إحصائيات أسماء المستخدمين
                </a>
            </li>
            <?php endif; ?>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'payments') !== false ? 'active' : ''; ?>" href="payments/">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    المدفوعات
                </a>
            </li>
        </ul>

        <?php if (isAdmin()): ?>
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>الإدارة</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'admin/users') !== false && !strpos($_SERVER['PHP_SELF'], 'username-generator') ? 'active' : ''; ?>" href="admin/users/">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'username-generator') !== false ? 'active' : ''; ?>" href="admin/users/username-generator.php">
                    <i class="fas fa-magic me-2"></i>
                    مولد أسماء المستخدمين
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'admin/notifications') !== false ? 'active' : ''; ?>" href="admin/notifications/">
                    <i class="fas fa-bell me-2"></i>
                    إدارة الإشعارات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'admin/networks') !== false ? 'active' : ''; ?>" href="admin/networks/">
                    <i class="fas fa-network-wired me-2"></i>
                    الشبكات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'admin/offers') !== false ? 'active' : ''; ?>" href="admin/offers/">
                    <i class="fas fa-plus-circle me-2"></i>
                    إدارة العروض
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'admin/settings') !== false && !strpos($_SERVER['PHP_SELF'], 'ip-protection') ? 'active' : ''; ?>" href="admin/settings/">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات النظام
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'ip-protection') !== false ? 'active' : ''; ?>" href="admin/settings/ip-protection.php">
                    <i class="fas fa-shield-alt me-2"></i>
                    حماية IP
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'ip-quality') !== false ? 'active' : ''; ?>" href="admin/settings/ip-quality.php">
                    <i class="fas fa-search-location me-2"></i>
                    جودة IP
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'notifications') !== false && strpos($_SERVER['PHP_SELF'], 'settings') !== false ? 'active' : ''; ?>" href="admin/settings/notifications.php">
                    <i class="fas fa-bell-slash me-2"></i>
                    إعدادات الإشعارات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'admin/logs') !== false ? 'active' : ''; ?>" href="admin/logs/">
                    <i class="fas fa-file-alt me-2"></i>
                    سجل النشاطات
                </a>
            </li>
        </ul>
        <?php endif; ?>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>الأدوات</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="tools/link-generator.php">
                    <i class="fas fa-magic me-2"></i>
                    مولد الروابط
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="tools/postback.php">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Postback URLs
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="tools/api.php">
                    <i class="fas fa-code me-2"></i>
                    API
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>المساعدة</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="help/documentation.php">
                    <i class="fas fa-book me-2"></i>
                    الدليل
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="help/support.php">
                    <i class="fas fa-life-ring me-2"></i>
                    الدعم الفني
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="help/faq.php">
                    <i class="fas fa-question-circle me-2"></i>
                    الأسئلة الشائعة
                </a>
            </li>
        </ul>

        <!-- Widget البريد المؤقت -->
        <?php if (!isAdmin()): ?>
            <div class="mt-4 px-3">
                <?php include 'temp-mail-widget.php'; ?>
            </div>
        <?php endif; ?>
    </div>
</nav>
