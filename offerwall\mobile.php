<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$subid = $user_id . '_mobile_' . time();

// بناء رابط Offerwall للجوال
$offerwall_url = "https://fastrsrvr.com/list/191?subid=" . urlencode($subid) . "&mobile=1";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Offerwall Mobile - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }
        .mobile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .mobile-content {
            margin-top: 80px;
            height: calc(100vh - 80px);
        }
        .offerwall-frame {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        .loading-screen {
            position: fixed;
            top: 80px;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999;
        }
    </style>
</head>
<body>
    <!-- Header للجوال -->
    <div class="mobile-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h6 class="mb-0">
                    <i class="fas fa-gift me-2"></i>
                    Offerwall
                </h6>
                <small>مرحباً <?php echo htmlspecialchars($_SESSION['username']); ?></small>
            </div>
            <div>
                <button class="btn btn-sm btn-light" onclick="refreshOfferwall()">
                    <i class="fas fa-sync"></i>
                </button>
                <a href="../" class="btn btn-sm btn-light ms-1">
                    <i class="fas fa-home"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- شاشة التحميل -->
    <div class="loading-screen" id="loadingScreen">
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="text-muted">جاري تحميل العروض...</p>
        </div>
    </div>

    <!-- محتوى Offerwall -->
    <div class="mobile-content">
        <iframe 
            id="offerwallFrame"
            sandbox="allow-popups allow-same-origin allow-scripts allow-top-navigation-by-user-activation allow-popups-to-escape-sandbox" 
            src="<?php echo htmlspecialchars($offerwall_url); ?>" 
            class="offerwall-frame"
            onload="hideLoading()">
        </iframe>
    </div>

    <script>
        function hideLoading() {
            document.getElementById('loadingScreen').style.display = 'none';
        }

        function refreshOfferwall() {
            document.getElementById('loadingScreen').style.display = 'flex';
            document.getElementById('offerwallFrame').src = document.getElementById('offerwallFrame').src;
        }

        // منع التكبير/التصغير
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });

        document.addEventListener('gesturechange', function (e) {
            e.preventDefault();
        });

        document.addEventListener('gestureend', function (e) {
            e.preventDefault();
        });
    </script>
</body>
</html>
