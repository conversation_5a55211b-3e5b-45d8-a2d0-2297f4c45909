<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$notificationManager = new NotificationManager($database);

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitize($_POST['action'] ?? '');
    
    switch ($action) {
        case 'create_notification':
            $notification_data = [
                'title' => sanitize($_POST['title']),
                'message' => sanitize($_POST['message']),
                'type' => sanitize($_POST['type']),
                'target_audience' => sanitize($_POST['target_audience']),
                'target_users' => $_POST['target_users'] ?? null,
                'offer_id' => !empty($_POST['offer_id']) ? intval($_POST['offer_id']) : null,
                'priority' => sanitize($_POST['priority']),
                'show_popup' => isset($_POST['show_popup']) ? 1 : 0,
                'show_banner' => isset($_POST['show_banner']) ? 1 : 0,
                'show_sidebar' => isset($_POST['show_sidebar']) ? 1 : 0,
                'auto_hide' => isset($_POST['auto_hide']) ? 1 : 0,
                'hide_after_seconds' => intval($_POST['hide_after_seconds']),
                'start_date' => !empty($_POST['start_date']) ? $_POST['start_date'] : date('Y-m-d H:i:s'),
                'end_date' => !empty($_POST['end_date']) ? $_POST['end_date'] : null,
                'created_by' => $_SESSION['user_id']
            ];
            
            if ($notificationManager->createNotification($notification_data)) {
                $_SESSION['success'] = 'تم إنشاء الإشعار بنجاح!';
            } else {
                $_SESSION['error'] = 'حدث خطأ في إنشاء الإشعار';
            }
            break;
            
        case 'toggle_notification':
            $notification_id = intval($_POST['notification_id']);
            $is_active = intval($_POST['is_active']);
            
            $toggle_query = "UPDATE notifications SET is_active = :is_active WHERE id = :id";
            $toggle_stmt = $db->prepare($toggle_query);
            $toggle_stmt->bindParam(':is_active', $is_active);
            $toggle_stmt->bindParam(':id', $notification_id);
            
            if ($toggle_stmt->execute()) {
                $_SESSION['success'] = $is_active ? 'تم تفعيل الإشعار' : 'تم إلغاء تفعيل الإشعار';
            } else {
                $_SESSION['error'] = 'حدث خطأ في تحديث الإشعار';
            }
            break;
            
        case 'delete_notification':
            $notification_id = intval($_POST['notification_id']);
            
            $delete_query = "DELETE FROM notifications WHERE id = :id";
            $delete_stmt = $db->prepare($delete_query);
            $delete_stmt->bindParam(':id', $notification_id);
            
            if ($delete_stmt->execute()) {
                $_SESSION['success'] = 'تم حذف الإشعار بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ في حذف الإشعار';
            }
            break;
    }
    
    header('Location: index.php');
    exit();
}

// جلب الإشعارات
$notifications_query = "SELECT n.*, u.username as created_by_name, o.title as offer_title,
                               (SELECT COUNT(*) FROM user_notifications un WHERE un.notification_id = n.id) as total_recipients,
                               (SELECT COUNT(*) FROM user_notifications un WHERE un.notification_id = n.id AND un.is_read = 1) as read_count
                        FROM notifications n
                        LEFT JOIN users u ON n.created_by = u.id
                        LEFT JOIN offers o ON n.offer_id = o.id
                        ORDER BY n.created_at DESC
                        LIMIT 50";

$notifications_stmt = $db->prepare($notifications_query);
$notifications_stmt->execute();
$notifications = $notifications_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب العروض للاختيار
$offers_query = "SELECT id, title FROM offers WHERE status = 'active' ORDER BY title";
$offers_stmt = $db->prepare($offers_query);
$offers_stmt->execute();
$offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب المستخدمين للاختيار المخصص
$users_query = "SELECT id, username FROM users WHERE status = 'active' ORDER BY username LIMIT 100";
$users_stmt = $db->prepare($users_query);
$users_stmt->execute();
$users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإشعارات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة الإشعارات</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createNotificationModal">
                            <i class="fas fa-plus me-1"></i>إشعار جديد
                        </button>
                    </div>
                </div>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- قائمة الإشعارات -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">الإشعارات الحالية</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($notifications)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-bell-slash fa-3x mb-3"></i>
                                <p>لا توجد إشعارات حتى الآن</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createNotificationModal">
                                    إنشاء أول إشعار
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>العنوان</th>
                                            <th>النوع</th>
                                            <th>الجمهور المستهدف</th>
                                            <th>المستلمين</th>
                                            <th>معدل القراءة</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($notifications as $notification): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($notification['title']); ?></strong>
                                                    <?php if ($notification['offer_title']): ?>
                                                        <br><small class="text-muted">العرض: <?php echo htmlspecialchars($notification['offer_title']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $type_classes = [
                                                        'info' => 'bg-info',
                                                        'success' => 'bg-success',
                                                        'warning' => 'bg-warning',
                                                        'danger' => 'bg-danger',
                                                        'offer' => 'bg-primary',
                                                        'system' => 'bg-secondary'
                                                    ];
                                                    $type_names = [
                                                        'info' => 'معلوماتي',
                                                        'success' => 'نجاح',
                                                        'warning' => 'تحذير',
                                                        'danger' => 'خطر',
                                                        'offer' => 'عرض',
                                                        'system' => 'نظام'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $type_classes[$notification['type']]; ?>">
                                                        <?php echo $type_names[$notification['type']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                    $audience_names = [
                                                        'all' => 'الجميع',
                                                        'active' => 'النشطين',
                                                        'inactive' => 'غير النشطين',
                                                        'new' => 'الجدد',
                                                        'custom' => 'مخصص'
                                                    ];
                                                    echo $audience_names[$notification['target_audience']];
                                                    ?>
                                                </td>
                                                <td><?php echo number_format($notification['total_recipients']); ?></td>
                                                <td>
                                                    <?php if ($notification['total_recipients'] > 0): ?>
                                                        <?php $read_rate = ($notification['read_count'] / $notification['total_recipients']) * 100; ?>
                                                        <span class="badge bg-<?php echo $read_rate >= 50 ? 'success' : ($read_rate >= 25 ? 'warning' : 'danger'); ?>">
                                                            <?php echo number_format($read_rate, 1); ?>%
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="toggle_notification">
                                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                        <input type="hidden" name="is_active" value="<?php echo $notification['is_active'] ? '0' : '1'; ?>">
                                                        <button type="submit" class="btn btn-sm btn-<?php echo $notification['is_active'] ? 'success' : 'secondary'; ?>">
                                                            <?php echo $notification['is_active'] ? 'نشط' : 'معطل'; ?>
                                                        </button>
                                                    </form>
                                                </td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-info" 
                                                                data-bs-toggle="modal" data-bs-target="#viewNotificationModal"
                                                                onclick="viewNotification(<?php echo htmlspecialchars(json_encode($notification)); ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <form method="POST" style="display: inline;" 
                                                              onsubmit="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                                            <input type="hidden" name="action" value="delete_notification">
                                                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                            <button type="submit" class="btn btn-outline-danger">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal إنشاء إشعار جديد -->
    <div class="modal fade" id="createNotificationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء إشعار جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="create_notification">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">عنوان الإشعار</label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="type" class="form-label">نوع الإشعار</label>
                                    <select class="form-control" id="type" name="type" required>
                                        <option value="info">معلوماتي</option>
                                        <option value="success">نجاح</option>
                                        <option value="warning">تحذير</option>
                                        <option value="danger">خطر</option>
                                        <option value="offer">عرض</option>
                                        <option value="system">نظام</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="priority" class="form-label">الأولوية</label>
                                    <select class="form-control" id="priority" name="priority">
                                        <option value="low">منخفضة</option>
                                        <option value="medium" selected>متوسطة</option>
                                        <option value="high">عالية</option>
                                        <option value="urgent">عاجلة</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="target_audience" class="form-label">الجمهور المستهدف</label>
                                    <select class="form-control" id="target_audience" name="target_audience" onchange="toggleCustomUsers()">
                                        <option value="all">جميع المستخدمين</option>
                                        <option value="active">المستخدمين النشطين</option>
                                        <option value="inactive">المستخدمين غير النشطين</option>
                                        <option value="new">المستخدمين الجدد</option>
                                        <option value="custom">مخصص</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3" id="customUsersDiv" style="display: none;">
                                    <label for="target_users" class="form-label">اختيار المستخدمين</label>
                                    <select class="form-control" id="target_users" name="target_users[]" multiple size="5">
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>"><?php echo htmlspecialchars($user['username']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="offer_id" class="form-label">ربط بعرض (اختياري)</label>
                                    <select class="form-control" id="offer_id" name="offer_id">
                                        <option value="">بدون ربط</option>
                                        <?php foreach ($offers as $offer): ?>
                                            <option value="<?php echo $offer['id']; ?>"><?php echo htmlspecialchars($offer['title']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="message" class="form-label">نص الإشعار</label>
                                    <textarea class="form-control" id="message" name="message" rows="4" required></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">خيارات العرض</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="show_popup" name="show_popup">
                                        <label class="form-check-label" for="show_popup">إشعار منبثق</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="show_banner" name="show_banner" checked>
                                        <label class="form-check-label" for="show_banner">بانر علوي</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="show_sidebar" name="show_sidebar" checked>
                                        <label class="form-check-label" for="show_sidebar">شريط جانبي</label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="auto_hide" name="auto_hide" checked>
                                        <label class="form-check-label" for="auto_hide">إخفاء تلقائي</label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="hide_after_seconds" class="form-label">مدة الإخفاء (ثواني)</label>
                                    <input type="number" class="form-control" id="hide_after_seconds" name="hide_after_seconds" value="10" min="3" max="60">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">تاريخ البداية</label>
                                    <input type="datetime-local" class="form-control" id="start_date" name="start_date">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">تاريخ الانتهاء</label>
                                    <input type="datetime-local" class="form-control" id="end_date" name="end_date">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إنشاء الإشعار</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal عرض الإشعار -->
    <div class="modal fade" id="viewNotificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الإشعار</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="notificationDetails">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        function toggleCustomUsers() {
            const targetAudience = document.getElementById('target_audience').value;
            const customUsersDiv = document.getElementById('customUsersDiv');
            
            if (targetAudience === 'custom') {
                customUsersDiv.style.display = 'block';
            } else {
                customUsersDiv.style.display = 'none';
            }
        }
        
        function viewNotification(notification) {
            const details = document.getElementById('notificationDetails');
            const typeNames = {
                'info': 'معلوماتي',
                'success': 'نجاح',
                'warning': 'تحذير',
                'danger': 'خطر',
                'offer': 'عرض',
                'system': 'نظام'
            };
            
            const audienceNames = {
                'all': 'الجميع',
                'active': 'النشطين',
                'inactive': 'غير النشطين',
                'new': 'الجدد',
                'custom': 'مخصص'
            };
            
            details.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>العنوان:</strong><br>
                        ${notification.title}<br><br>
                        
                        <strong>النوع:</strong><br>
                        ${typeNames[notification.type]}<br><br>
                        
                        <strong>الجمهور المستهدف:</strong><br>
                        ${audienceNames[notification.target_audience]}<br><br>
                        
                        <strong>الأولوية:</strong><br>
                        ${notification.priority}<br><br>
                    </div>
                    <div class="col-md-6">
                        <strong>خيارات العرض:</strong><br>
                        ${notification.show_popup ? '✓' : '✗'} إشعار منبثق<br>
                        ${notification.show_banner ? '✓' : '✗'} بانر علوي<br>
                        ${notification.show_sidebar ? '✓' : '✗'} شريط جانبي<br><br>
                        
                        <strong>إخفاء تلقائي:</strong><br>
                        ${notification.auto_hide ? 'نعم (' + notification.hide_after_seconds + ' ثانية)' : 'لا'}<br><br>
                        
                        <strong>تاريخ الإنشاء:</strong><br>
                        ${notification.created_at}<br><br>
                    </div>
                </div>
                
                <strong>نص الإشعار:</strong><br>
                <div class="alert alert-light">${notification.message}</div>
            `;
        }
    </script>
</body>
</html>
