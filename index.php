<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// تضمين widget جودة IP
require_once 'includes/ip-quality-widget.php';
$user_ip = $_SERVER['REMOTE_ADDR'];

// جلب إحصائيات المستخدم
$user_id = $_SESSION['user_id'];
$stats_query = "SELECT 
    COUNT(DISTINCT o.id) as total_offers,
    COALESCE(SUM(c.clicks), 0) as total_clicks,
    COALESCE(SUM(c.conversions), 0) as total_conversions,
    COALESCE(SUM(c.earnings), 0) as total_earnings
FROM offers o 
LEFT JOIN clicks c ON o.id = c.offer_id 
WHERE o.status = 'active' AND (c.user_id = :user_id OR c.user_id IS NULL)";

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->bindParam(':user_id', $user_id);
$stats_stmt->execute();
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// جلب العروض المتاحة
$offers_query = "SELECT o.*, n.name as network_name 
FROM offers o 
JOIN networks n ON o.network_id = n.id 
WHERE o.status = 'active' 
ORDER BY o.payout DESC 
LIMIT 10";

$offers_stmt = $db->prepare($offers_query);
$offers_stmt->execute();
$offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة التحكم</h1>
                </div>

                <!-- رسالة ترحيب للحسابات الجديدة -->
                <?php if (isset($_GET['welcome']) && isset($_SESSION['new_account'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <h5><i class="fas fa-party-horn me-2"></i>مرحباً بك!</h5>
                        <p class="mb-2">تم إنشاء حسابك بنجاح. إليك بيانات تسجيل الدخول:</p>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>اسم المستخدم:</strong>
                                <code class="bg-white p-1 rounded"><?php echo htmlspecialchars($_SESSION['new_account']['username']); ?></code>
                            </div>
                            <div class="col-md-6">
                                <strong>كلمة المرور:</strong>
                                <code class="bg-white p-1 rounded"><?php echo htmlspecialchars($_SESSION['new_account']['password']); ?></code>
                            </div>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            <i class="fas fa-info-circle me-1"></i>
                            احفظ هذه البيانات في مكان آمن للدخول مرة أخرى
                        </small>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['new_account']); ?>
                <?php endif; ?>

                <!-- رابط سريع للـ Offerwall -->
                <div class="alert alert-info mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="alert-heading mb-1">
                                <i class="fas fa-gift me-2"></i>
                                اربح المزيد مع Offerwall!
                            </h5>
                            <p class="mb-0">أكمل العروض السريعة واربح عمولات فورية من CPALead</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="offerwall/" class="btn btn-success">
                                <i class="fas fa-external-link-alt me-1"></i>
                                افتح Offerwall
                            </a>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي العروض
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_offers']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            إجمالي النقرات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_clicks']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            إجمالي التحويلات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_conversions']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            إجمالي الأرباح
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo CURRENCY_SYMBOL . number_format($stats['total_earnings'], 2); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- العروض المتاحة -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أفضل العروض المتاحة</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>اسم العرض</th>
                                        <th>الشبكة</th>
                                        <th>العمولة</th>
                                        <th>النوع</th>
                                        <th>البلدان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($offers as $offer): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($offer['title']); ?></td>
                                        <td><?php echo htmlspecialchars($offer['network_name']); ?></td>
                                        <td><?php echo CURRENCY_SYMBOL . number_format($offer['payout'], 2); ?></td>
                                        <td><?php echo htmlspecialchars($offer['type']); ?></td>
                                        <td><?php echo htmlspecialchars($offer['countries']); ?></td>
                                        <td>
                                            <a href="offers/view.php?id=<?php echo $offer['id']; ?>" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a href="tracking/generate.php?offer_id=<?php echo $offer['id']; ?>" class="btn btn-success btn-sm">
                                                <i class="fas fa-link"></i> رابط
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    </div>

                    <!-- Widget جودة IP -->
                    <div class="col-lg-4">
                        <?php echo renderIPQualityWidget($user_ip, $database, true); ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
