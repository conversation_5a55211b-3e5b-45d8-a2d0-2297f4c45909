# نظام CPA Marketing - دليل التشغيل

## نظرة عامة
نظام شامل لإدارة عروض CPA (Cost Per Action) يدعم الشبكات المختلفة مثل CPALead وMaxBounty وغيرها. يوفر النظام إدارة كاملة للعروض والمستخدمين والتتبع والمدفوعات.

## المتطلبات
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مكتبات PHP: PDO, JSON, cURL

## التثبيت

### 1. إعد<PERSON> قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE cpa_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل
mysql -u username -p cpa_system < database/schema.sql
```

### 2. إعداد الملفات
```bash
# نسخ الملفات إلى مجلد الويب
cp -r * /var/www/html/cpa/

# تعديل صلاحيات المجلدات
chmod 755 uploads/
chmod 755 logs/
chmod 644 config/config.php
```

### 3. إعداد التكوين
قم بتعديل ملف `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'cpa_system';
private $username = 'your_db_username';
private $password = 'your_db_password';
```

قم بتعديل ملف `config/config.php`:
```php
define('SITE_URL', 'http://yourdomain.com/cpa');
define('ADMIN_EMAIL', '<EMAIL>');
define('SECRET_KEY', 'your-unique-secret-key');
```

## الميزات الرئيسية

### 1. إدارة المستخدمين
- تسجيل وتسجيل دخول آمن
- أدوار مختلفة (مدير، ناشر)
- إدارة الملفات الشخصية
- نظام صلاحيات متقدم

### 2. إدارة الشبكات والعروض
- دعم شبكات متعددة (CPALead, MaxBounty, إلخ)
- إضافة وتعديل العروض
- فلترة وبحث متقدم
- إدارة حالة العروض

### 3. نظام التتبع
- توليد روابط تتبع فريدة
- تتبع النقرات والتحويلات
- معلومات مفصلة عن الزوار
- دعم Sub IDs والحملات

### 4. التقارير والإحصائيات
- إحصائيات يومية وشهرية
- تقارير مفصلة للأداء
- رسوم بيانية تفاعلية
- تصدير البيانات

### 5. نظام المدفوعات
- إدارة الأرباح والأرصدة
- طلبات السحب
- طرق دفع متعددة
- تتبع المدفوعات

## الاستخدام

### تسجيل الدخول الأول
- المدير الافتراضي: `<EMAIL>`
- كلمة المرور: `password` (يجب تغييرها)

### إضافة شبكة جديدة
1. اذهب إلى "الإدارة" > "الشبكات"
2. انقر "إضافة شبكة جديدة"
3. أدخل معلومات الشبكة وAPI إذا توفر
4. احفظ الإعدادات

### إضافة عرض جديد
1. اذهب إلى "الإدارة" > "العروض"
2. انقر "إضافة عرض جديد"
3. اختر الشبكة وأدخل تفاصيل العرض
4. حدد العمولة والبلدان المستهدفة
5. احفظ العرض

### إنشاء رابط تتبع
1. اذهب إلى "العروض"
2. انقر "رابط" بجانب العرض المطلوب
3. أدخل معاملات التتبع (اختياري)
4. انسخ الرابط واستخدمه في حملاتك

## API Documentation

### إنشاء رابط تتبع
```
POST /api/tracking/create
{
    "offer_id": 123,
    "sub_id": "campaign1",
    "source": "facebook"
}
```

### جلب الإحصائيات
```
GET /api/stats?user_id=123&date_from=2024-01-01&date_to=2024-01-31
```

### Postback URL (متوافق مع CPALead)
```
GET /tracking/postback.php?click_id={click_id}&transaction_id={transaction_id}&payout={payout}&status=approved
```

### Postback URL متقدم (CPALead)
```
GET /tracking/postback.php?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&payout={payout}&lead_id={lead_id}&country_iso={country_iso}&password={password}&virtual_currency={virtual_currency}
```

### المعاملات المدعومة
- `click_id` - معرف النقرة
- `subid`, `subid2`, `subid3` - معرفات فرعية للتتبع
- `campaign_id`, `campaign_name` - معلومات الحملة
- `lead_id` - معرف العميل المحتمل
- `gateway_id` - معرف البوابة
- `payout` - قيمة العمولة
- `virtual_currency` - العملة الافتراضية
- `country_iso` - رمز البلد
- `password` - كلمة مرور الحماية
- `idfa` - Apple IDFA للتطبيقات
- `gaid` - Google Advertising ID للتطبيقات

## الأمان

### إعدادات الأمان المطلوبة
1. تغيير `SECRET_KEY` في config.php
2. استخدام HTTPS في الإنتاج
3. تحديث كلمات مرور قاعدة البيانات
4. تفعيل firewall للخادم

### حماية الملفات
```apache
# .htaccess في مجلد config/
<Files "*">
    Order allow,deny
    Deny from all
</Files>
```

## النسخ الاحتياطي

### نسخ احتياطي لقاعدة البيانات
```bash
mysqldump -u username -p cpa_system > backup_$(date +%Y%m%d).sql
```

### نسخ احتياطي للملفات
```bash
tar -czf cpa_backup_$(date +%Y%m%d).tar.gz /path/to/cpa/
```

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في الاتصال بقاعدة البيانات**: تحقق من إعدادات database.php
2. **صفحة بيضاء**: فعّل عرض الأخطاء في PHP
3. **روابط التتبع لا تعمل**: تحقق من mod_rewrite في Apache

### سجلات الأخطاء
- سجل PHP: `/var/log/apache2/error.log`
- سجل النظام: `logs/error.log`
- سجل النشاطات: جدول `activity_logs`

## التطوير والتخصيص

### إضافة شبكة جديدة
1. أضف الشبكة في جدول `networks`
2. قم بتطوير API integration إذا لزم الأمر
3. اختبر التكامل مع postback URLs

### تخصيص التصميم
- ملفات CSS في `assets/css/`
- ملفات JavaScript في `assets/js/`
- القوالب في مجلدات منفصلة

### إضافة ميزات جديدة
1. خطط للميزة وقاعدة البيانات المطلوبة
2. أنشئ الملفات المطلوبة
3. اختبر الميزة بدقة
4. وثق الميزة الجديدة

## الدعم والمساهمة

### الحصول على المساعدة
- راجع الوثائق أولاً
- تحقق من سجلات الأخطاء
- اتصل بالدعم الفني

### المساهمة في التطوير
1. Fork المشروع
2. أنشئ branch للميزة الجديدة
3. اختبر التغييرات
4. أرسل Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## معلومات الإصدار
- الإصدار: 1.0.0
- تاريخ الإصدار: 2024-01-01
- المطور: فريق التطوير

## التحديثات المستقبلية
- دعم المزيد من الشبكات
- تطبيق موبايل
- تحليلات متقدمة
- ذكاء اصطناعي للتوصيات
