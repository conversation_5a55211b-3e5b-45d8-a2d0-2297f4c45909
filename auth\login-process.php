<?php
require_once '../config/config.php';

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: login.php');
    exit();
}

$username = trim($_POST['username'] ?? '');
$password = $_POST['password'] ?? '';

if (empty($username) || empty($password)) {
    $_SESSION['error'] = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    header('Location: login.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // البحث عن المستخدم
    $query = "SELECT id, username, password, role, status FROM users WHERE username = :username";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    if ($stmt->rowCount() === 1) {
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // التحقق من حالة الحساب
        if ($user['status'] !== 'active') {
            $_SESSION['error'] = 'الحساب غير نشط';
            header('Location: login.php');
            exit();
        }
        
        // التحقق من كلمة المرور
        if (password_verify($password, $user['password'])) {
            // تسجيل دخول ناجح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            
            // تحديث آخر تسجيل دخول وعداد تسجيل الدخول
            $update_query = "UPDATE users SET 
                            last_login = NOW(), 
                            login_count = login_count + 1, 
                            ip_address = :ip_address, 
                            user_agent = :user_agent 
                            WHERE id = :user_id";
            
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(':user_id', $user['id']);
            $update_stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
            $update_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
            $update_stmt->execute();
            
            // تسجيل النشاط
            $activity_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                              VALUES (:user_id, 'user_login', 'تسجيل دخول المستخدم', :data)";
            
            $activity_data = json_encode([
                'username' => $username,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);

            $activity_stmt = $db->prepare($activity_query);
            $activity_stmt->bindParam(':user_id', $user['id']);
            $activity_stmt->bindParam(':data', $activity_data);
            $activity_stmt->execute();
            
            // إعادة توجيه للصفحة الرئيسية
            header('Location: ../index.php');
            exit();
            
        } else {
            $_SESSION['error'] = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    } else {
        $_SESSION['error'] = 'اسم المستخدم أو كلمة المرور غير صحيحة';
    }
    
} catch (PDOException $e) {
    logError("خطأ في تسجيل الدخول: " . $e->getMessage());
    $_SESSION['error'] = 'حدث خطأ في النظام';
}

// إعادة توجيه في حالة الفشل
header('Location: login.php');
exit();
?>
