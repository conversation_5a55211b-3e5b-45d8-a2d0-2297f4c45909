<?php
/**
 * إصلاح جميع روابط القائمة الجانبية
 */

echo "<h2>🔧 إصلاح روابط القائمة الجانبية</h2>";

try {
    require_once 'config/config.php';
    
    echo "<h3>1. إنشاء المجلدات المطلوبة:</h3>";
    
    $directories = [
        // المجلدات الأساسية
        'offers', 'tracking', 'reports', 'payments', 'offerwall',
        'tools', 'help',
        
        // مجلدات الإدارة
        'admin', 'admin/users', 'admin/offers', 'admin/networks', 
        'admin/settings', 'admin/logs', 'admin/notifications', 'admin/reports',
        
        // مجلدات فرعية للإعدادات
        'admin/settings/ip-protection', 'admin/settings/ip-quality', 'admin/settings/notifications'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "✅ تم إنشاء مجلد: $dir<br>";
        } else {
            echo "✅ المجلد موجود: $dir<br>";
        }
    }
    
    echo "<h3>2. إنشاء قالب الصفحة الأساسي:</h3>";
    
    function createPageTemplate($title, $icon, $description, $backPath = "../index.php", $isAdmin = false) {
        $adminCheck = $isAdmin ? '
if (!isLoggedIn() || !isAdmin()) { 
    header("Location: ' . ($backPath == "../index.php" ? "../" : "../../") . 'auth/login.php"); 
    exit(); 
}' : '
if (!isLoggedIn()) { 
    header("Location: ' . ($backPath == "../index.php" ? "../" : "../../") . 'auth/login.php"); 
    exit(); 
}';

        $configPath = $backPath == "../index.php" ? "../config/config.php" : 
                     ($backPath == "../../index.php" ? "../../config/config.php" : "../config/config.php");
        
        return '<?php
require_once "' . $configPath . '";
' . $adminCheck . '
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $title . ' - ' . (defined('SITE_NAME') ? SITE_NAME : 'CPA System') . '</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .feature-card {
            transition: transform 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .coming-soon-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-gradient text-white text-center py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="fas fa-' . $icon . ' fa-3x mb-3"></i>
                        <h2 class="mb-0">' . $title . '</h2>
                        <p class="mb-0 opacity-75">' . $description . '</p>
                    </div>
                    <div class="card-body p-5">
                        <div class="position-relative">
                            <div class="coming-soon-badge">
                                <i class="fas fa-clock me-1"></i>قريباً
                            </div>
                            
                            <div class="alert alert-info border-0 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle fa-2x text-info me-3"></i>
                                    <div>
                                        <h5 class="alert-heading mb-1">هذه الميزة قيد التطوير</h5>
                                        <p class="mb-0">نعمل بجد لإنجاز هذه الصفحة. ستكون متاحة قريباً مع جميع الميزات المطلوبة.</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-6 mb-3">
                                    <div class="feature-card card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-rocket fa-2x text-primary mb-3"></i>
                                            <h6>قريباً جداً</h6>
                                            <small class="text-muted">نعمل على إنجازها</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="feature-card card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-star fa-2x text-warning mb-3"></i>
                                            <h6>ميزات رائعة</h6>
                                            <small class="text-muted">ستحب النتيجة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <a href="' . $backPath . '" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-arrow-right me-2"></i>العودة للرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
    }
    
    echo "<h3>3. إنشاء الصفحات الأساسية:</h3>";
    
    $basicPages = [
        'offers/index.php' => createPageTemplate('العروض', 'bullhorn', 'إدارة وعرض جميع العروض المتاحة'),
        'tracking/index.php' => createPageTemplate('التتبع', 'link', 'إنشاء وإدارة روابط التتبع'),
        'reports/index.php' => createPageTemplate('التقارير', 'chart-bar', 'عرض الإحصائيات والتقارير المفصلة'),
        'payments/index.php' => createPageTemplate('المدفوعات', 'money-bill-wave', 'إدارة الأرباح وطلب المدفوعات'),
        'offerwall/index.php' => createPageTemplate('Offerwall', 'gift', 'إكمال العروض السريعة وربح عمولات فورية'),
        'temp-mail.php' => createPageTemplate('البريد المؤقت', 'envelope', 'مجموعة من أفضل مواقع البريد المؤقت', 'index.php')
    ];
    
    foreach ($basicPages as $file => $content) {
        if (!file_exists($file)) {
            file_put_contents($file, $content);
            echo "✅ تم إنشاء: $file<br>";
        } else {
            echo "✅ موجود: $file<br>";
        }
    }
    
    echo "<h3>4. إنشاء صفحات الإدارة:</h3>";
    
    $adminPages = [
        'admin/users/index.php' => createPageTemplate('إدارة المستخدمين', 'users', 'إدارة حسابات المستخدمين والصلاحيات', '../../index.php', true),
        'admin/users/username-generator.php' => createPageTemplate('مولد أسماء المستخدمين', 'magic', 'إنشاء أسماء مستخدمين تلقائياً', '../../index.php', true),
        'admin/offers/index.php' => createPageTemplate('إدارة العروض', 'plus-circle', 'إضافة وتعديل العروض', '../../index.php', true),
        'admin/networks/index.php' => createPageTemplate('إدارة الشبكات', 'network-wired', 'إدارة شبكات CPA', '../../index.php', true),
        'admin/settings/index.php' => createPageTemplate('إعدادات النظام', 'cogs', 'إعدادات عامة للنظام', '../../index.php', true),
        'admin/logs/index.php' => createPageTemplate('سجل النشاطات', 'file-alt', 'عرض سجل أنشطة المستخدمين', '../../index.php', true),
        'admin/notifications/index.php' => createPageTemplate('إدارة الإشعارات', 'bell', 'إنشاء وإدارة الإشعارات', '../../index.php', true),
        'admin/reports/username-stats.php' => createPageTemplate('إحصائيات أسماء المستخدمين', 'chart-line', 'إحصائيات مفصلة عن أسماء المستخدمين', '../../index.php', true)
    ];
    
    foreach ($adminPages as $file => $content) {
        if (!file_exists($file)) {
            file_put_contents($file, $content);
            echo "✅ تم إنشاء: $file<br>";
        } else {
            echo "✅ موجود: $file<br>";
        }
    }
    
    echo "<h3>5. إنشاء صفحات الإعدادات المتقدمة:</h3>";
    
    $settingsPages = [
        'admin/settings/ip-protection.php' => createPageTemplate('حماية IP', 'shield-alt', 'إعدادات حماية عناوين IP', '../../index.php', true),
        'admin/settings/ip-quality.php' => createPageTemplate('جودة IP', 'search-location', 'فحص وتقييم جودة عناوين IP', '../../index.php', true),
        'admin/settings/notifications.php' => createPageTemplate('إعدادات الإشعارات', 'bell-slash', 'تخصيص إعدادات الإشعارات', '../../index.php', true)
    ];
    
    foreach ($settingsPages as $file => $content) {
        if (!file_exists($file)) {
            file_put_contents($file, $content);
            echo "✅ تم إنشاء: $file<br>";
        } else {
            echo "✅ موجود: $file<br>";
        }
    }
    
    echo "<h3>6. إنشاء صفحات الأدوات:</h3>";
    
    $toolsPages = [
        'tools/link-generator.php' => createPageTemplate('مولد الروابط', 'magic', 'إنشاء روابط تتبع مخصصة'),
        'tools/postback.php' => createPageTemplate('Postback URLs', 'exchange-alt', 'إدارة روابط Postback'),
        'tools/api.php' => createPageTemplate('واجهة برمجة التطبيقات', 'code', 'وثائق وأدوات API')
    ];
    
    foreach ($toolsPages as $file => $content) {
        if (!file_exists($file)) {
            file_put_contents($file, $content);
            echo "✅ تم إنشاء: $file<br>";
        } else {
            echo "✅ موجود: $file<br>";
        }
    }
    
    echo "<h3>7. إنشاء صفحات المساعدة:</h3>";
    
    $helpPages = [
        'help/documentation.php' => createPageTemplate('الدليل', 'book', 'دليل شامل لاستخدام النظام'),
        'help/support.php' => createPageTemplate('الدعم الفني', 'life-ring', 'الحصول على المساعدة والدعم'),
        'help/faq.php' => createPageTemplate('الأسئلة الشائعة', 'question-circle', 'إجابات للأسئلة الأكثر شيوعاً')
    ];
    
    foreach ($helpPages as $file => $content) {
        if (!file_exists($file)) {
            file_put_contents($file, $content);
            echo "✅ تم إنشاء: $file<br>";
        } else {
            echo "✅ موجود: $file<br>";
        }
    }
    
    echo "<hr>";
    echo "<h3>🎉 تم إصلاح جميع روابط القائمة الجانبية!</h3>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
    echo "<h5>✅ جميع الروابط تعمل الآن!</h5>";
    echo "<ul>";
    echo "<li>✅ تم إنشاء جميع المجلدات المطلوبة</li>";
    echo "<li>✅ تم إنشاء جميع الصفحات المفقودة</li>";
    echo "<li>✅ صفحات جميلة مع تصميم احترافي</li>";
    echo "<li>✅ رسائل 'قريباً' واضحة ومفيدة</li>";
    echo "<li>✅ أزرار العودة للرئيسية</li>";
    echo "<li>✅ حماية للصفحات الإدارية</li>";
    echo "<li>✅ لا توجد أخطاء 404 بعد الآن</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h4>🧪 اختبار الروابط:</h4>";
    echo "<div class='row'>";
    
    echo "<div class='col-md-4'>";
    echo "<h6>الصفحات الأساسية:</h6>";
    echo "<ul>";
    echo "<li><a href='offers/' target='_blank'>العروض</a></li>";
    echo "<li><a href='tracking/' target='_blank'>التتبع</a></li>";
    echo "<li><a href='reports/' target='_blank'>التقارير</a></li>";
    echo "<li><a href='payments/' target='_blank'>المدفوعات</a></li>";
    echo "<li><a href='offerwall/' target='_blank'>Offerwall</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<h6>الأدوات:</h6>";
    echo "<ul>";
    echo "<li><a href='tools/link-generator.php' target='_blank'>مولد الروابط</a></li>";
    echo "<li><a href='tools/postback.php' target='_blank'>Postback URLs</a></li>";
    echo "<li><a href='tools/api.php' target='_blank'>API</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='col-md-4'>";
    echo "<h6>المساعدة:</h6>";
    echo "<ul>";
    echo "<li><a href='help/documentation.php' target='_blank'>الدليل</a></li>";
    echo "<li><a href='help/support.php' target='_blank'>الدعم الفني</a></li>";
    echo "<li><a href='help/faq.php' target='_blank'>الأسئلة الشائعة</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<h4>🔐 صفحات الإدارة (للمدراء فقط):</h4>";
    echo "<div class='row'>";

    echo "<div class='col-md-6'>";
    echo "<h6>إدارة المحتوى:</h6>";
    echo "<ul>";
    echo "<li><a href='admin/users/' target='_blank'>إدارة المستخدمين</a></li>";
    echo "<li><a href='admin/offers/' target='_blank'>إدارة العروض</a></li>";
    echo "<li><a href='admin/networks/' target='_blank'>إدارة الشبكات</a></li>";
    echo "<li><a href='admin/notifications/' target='_blank'>إدارة الإشعارات</a></li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<h6>الإعدادات والتقارير:</h6>";
    echo "<ul>";
    echo "<li><a href='admin/settings/' target='_blank'>إعدادات النظام</a></li>";
    echo "<li><a href='admin/settings/ip-protection.php' target='_blank'>حماية IP</a></li>";
    echo "<li><a href='admin/settings/ip-quality.php' target='_blank'>جودة IP</a></li>";
    echo "<li><a href='admin/logs/' target='_blank'>سجل النشاطات</a></li>";
    echo "</ul>";
    echo "</div>";

    echo "</div>";

    echo "<div class='text-center mt-4'>";
    echo "<a href='index.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 18px; margin: 10px;'>🏠 العودة للصفحة الرئيسية</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>❌ خطأ في الإصلاح:</h3>";
    echo "<div style='color: red; background: #ffe6e6; padding: 15px; border: 1px solid red; border-radius: 8px;'>";
    echo "<strong>رسالة الخطأ:</strong> " . $e->getMessage() . "<br>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3, h4, h5, h6 {
    color: #333;
}
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}
.col-md-4 {
    flex: 0 0 33.333333%;
    padding: 0 15px;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
ul {
    padding-left: 20px;
}
</style>
