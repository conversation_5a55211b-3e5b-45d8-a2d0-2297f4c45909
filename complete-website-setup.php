<?php
/**
 * إعداد الموقع الكامل - ربط جميع الملفات والمكونات
 */

echo "<h1>🚀 إعداد الموقع الكامل</h1>";
echo "<p>جاري إنشاء وربط جميع ملفات النظام...</p>";

try {
    require_once 'config/config.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<div class='progress-container'>";
    echo "<div class='progress-bar' id='progressBar'>0%</div>";
    echo "</div>";
    
    echo "<div id='log'>";
    
    // 1. إنشاء جميع الجداول المطلوبة
    echo "<h3>📊 1. إنشاء قاعدة البيانات الكاملة:</h3>";
    
    $tables = [
        // جدول المستخدمين المحدث
        "users" => "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) DEFAULT NULL,
            first_name VARCHAR(50) DEFAULT NULL,
            last_name VARCHAR(50) DEFAULT NULL,
            role ENUM('admin', 'publisher') DEFAULT 'publisher',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            balance DECIMAL(10,2) DEFAULT 0.00,
            total_earnings DECIMAL(10,2) DEFAULT 0.00,
            phone VARCHAR(20),
            country VARCHAR(50),
            payment_method ENUM('paypal', 'bank_transfer', 'payoneer') DEFAULT 'paypal',
            payment_details TEXT,
            api_key VARCHAR(64) UNIQUE,
            is_auto_generated BOOLEAN DEFAULT FALSE,
            login_count INT DEFAULT 0,
            ip_address VARCHAR(45),
            user_agent TEXT,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_is_auto_generated (is_auto_generated)
        )",
        
        // جدول الشبكات
        "networks" => "CREATE TABLE IF NOT EXISTS networks (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            website VARCHAR(255),
            api_endpoint VARCHAR(255),
            api_key VARCHAR(255),
            api_secret VARCHAR(255),
            postback_url VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            commission_rate DECIMAL(5,2) DEFAULT 0.00,
            payment_terms TEXT,
            contact_info TEXT,
            logo VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // جدول العروض المحدث
        "offers" => "CREATE TABLE IF NOT EXISTS offers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            network_id INT DEFAULT 1,
            external_id VARCHAR(100),
            title VARCHAR(255) NOT NULL,
            description TEXT,
            preview_url VARCHAR(500),
            payout DECIMAL(10,2) NOT NULL,
            type ENUM('cpa', 'cpl', 'cps', 'cpi', 'cpc') DEFAULT 'cpa',
            category VARCHAR(100),
            countries TEXT,
            allowed_traffic TEXT,
            restrictions TEXT,
            status ENUM('active', 'inactive', 'paused') DEFAULT 'active',
            cap_daily INT DEFAULT 0,
            cap_monthly INT DEFAULT 0,
            conversion_flow TEXT,
            tracking_url VARCHAR(500),
            external_offer_url TEXT,
            external_image_url TEXT,
            image VARCHAR(255),
            is_external BOOLEAN DEFAULT FALSE,
            external_tracking BOOLEAN DEFAULT FALSE,
            start_date DATE,
            end_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_type (type),
            INDEX idx_is_external (is_external),
            INDEX idx_created_at (created_at)
        )",
        
        // جدول روابط التتبع
        "tracking_links" => "CREATE TABLE IF NOT EXISTS tracking_links (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            offer_id INT NOT NULL,
            tracking_id VARCHAR(32) UNIQUE NOT NULL,
            original_url VARCHAR(500) NOT NULL,
            tracking_url VARCHAR(500) NOT NULL,
            sub_id VARCHAR(100),
            source VARCHAR(100),
            campaign VARCHAR(100),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_offer_id (offer_id),
            INDEX idx_tracking_id (tracking_id)
        )",
        
        // جدول النقرات
        "clicks" => "CREATE TABLE IF NOT EXISTS clicks (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tracking_id VARCHAR(32) NOT NULL,
            user_id INT NOT NULL,
            offer_id INT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            referer VARCHAR(500),
            country VARCHAR(50),
            city VARCHAR(100),
            device_type ENUM('desktop', 'mobile', 'tablet'),
            browser VARCHAR(50),
            os VARCHAR(50),
            sub_id VARCHAR(100),
            sub_id2 VARCHAR(100),
            sub_id3 VARCHAR(100),
            source VARCHAR(100),
            campaign VARCHAR(100),
            idfa VARCHAR(100),
            gaid VARCHAR(100),
            is_unique BOOLEAN DEFAULT TRUE,
            clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_tracking_id (tracking_id),
            INDEX idx_user_offer (user_id, offer_id),
            INDEX idx_clicked_at (clicked_at)
        )",
        
        // جدول التحويلات
        "conversions" => "CREATE TABLE IF NOT EXISTS conversions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tracking_id VARCHAR(32) NOT NULL,
            user_id INT NOT NULL,
            offer_id INT NOT NULL,
            click_id INT,
            transaction_id VARCHAR(100),
            lead_id VARCHAR(100),
            campaign_id VARCHAR(100),
            campaign_name VARCHAR(255),
            gateway_id VARCHAR(100),
            payout DECIMAL(10,2) NOT NULL,
            revenue DECIMAL(10,2),
            virtual_currency DECIMAL(10,2) DEFAULT 0,
            status ENUM('pending', 'approved', 'rejected', 'reversed') DEFAULT 'pending',
            ip_address VARCHAR(45),
            country VARCHAR(50),
            country_iso VARCHAR(2),
            sub_id VARCHAR(100),
            sub_id2 VARCHAR(100),
            sub_id3 VARCHAR(100),
            source VARCHAR(100),
            campaign VARCHAR(100),
            idfa VARCHAR(100),
            gaid VARCHAR(100),
            postback_password VARCHAR(255),
            conversion_data TEXT,
            approved_at TIMESTAMP NULL,
            converted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_tracking_id (tracking_id),
            INDEX idx_user_offer (user_id, offer_id),
            INDEX idx_status (status),
            INDEX idx_converted_at (converted_at),
            INDEX idx_lead_id (lead_id),
            INDEX idx_transaction_id (transaction_id)
        )",
        
        // جدول المدفوعات
        "payments" => "CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            method ENUM('paypal', 'bank_transfer', 'payoneer') NOT NULL,
            status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
            transaction_id VARCHAR(100),
            payment_details TEXT,
            notes TEXT,
            requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            completed_at TIMESTAMP NULL,
            INDEX idx_user_status (user_id, status),
            INDEX idx_requested_at (requested_at)
        )",
        
        // جدول الإحصائيات اليومية
        "daily_stats" => "CREATE TABLE IF NOT EXISTS daily_stats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            offer_id INT NOT NULL,
            date DATE NOT NULL,
            clicks INT DEFAULT 0,
            unique_clicks INT DEFAULT 0,
            conversions INT DEFAULT 0,
            earnings DECIMAL(10,2) DEFAULT 0.00,
            revenue DECIMAL(10,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_offer_date (user_id, offer_id, date),
            INDEX idx_date (date),
            INDEX idx_user_date (user_id, date)
        )",
        
        // جدول سجل النشاطات
        "activity_logs" => "CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            data JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_action (user_id, action),
            INDEX idx_created_at (created_at)
        )",
        
        // جدول الإعدادات
        "settings" => "CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )"
    ];
    
    $progress = 0;
    $totalSteps = count($tables) + 50; // تقدير للخطوات الإضافية
    
    foreach ($tables as $tableName => $sql) {
        $db->exec($sql);
        $progress++;
        $percentage = round(($progress / $totalSteps) * 100);
        echo "<script>updateProgress($percentage, 'تم إنشاء جدول $tableName');</script>";
        echo "✅ جدول $tableName<br>";
        flush();
        usleep(100000); // 0.1 ثانية
    }
    
    echo "<h3>📁 2. إنشاء هيكل المجلدات:</h3>";
    
    $directories = [
        'assets', 'assets/css', 'assets/js', 'assets/images', 'assets/uploads',
        'includes', 'auth', 'config', 'database',
        'offers', 'tracking', 'reports', 'payments', 'offerwall',
        'admin', 'admin/users', 'admin/offers', 'admin/networks', 
        'admin/settings', 'admin/logs', 'admin/notifications', 'admin/reports',
        'tools', 'help', 'api', 'postback',
        'temp', 'logs', 'backup'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            $progress++;
            $percentage = round(($progress / $totalSteps) * 100);
            echo "<script>updateProgress($percentage, 'تم إنشاء مجلد $dir');</script>";
            echo "✅ مجلد: $dir<br>";
            flush();
            usleep(50000);
        }
    }
    
    echo "<h3>🔧 3. إنشاء الملفات الأساسية:</h3>";

    // إنشاء ملف CSS محسن
    if (!file_exists('assets/css/style.css')) {
        $css_content = '/* نظام CPA - ملف الأنماط الرئيسي */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

body {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
}

/* تحسينات الشريط الجانبي */
.sidebar {
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: #333;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 2px 10px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(0,123,255,0.1);
    color: var(--primary-color);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 2px 5px rgba(0,123,255,0.3);
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* تحسينات الأزرار */
.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسينات الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
    font-weight: 600;
}

/* تحسينات النماذج */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* تحسينات الإحصائيات */
.border-left-primary { border-left: 4px solid var(--primary-color) !important; }
.border-left-success { border-left: 4px solid var(--success-color) !important; }
.border-left-info { border-left: 4px solid var(--info-color) !important; }
.border-left-warning { border-left: 4px solid var(--warning-color) !important; }

.text-gray-800 { color: #5a5c69 !important; }
.text-gray-300 { color: #dddfeb !important; }

/* تحسينات البريد المؤقت */
.temp-mail-card {
    padding: 20px;
    border: 1px solid #e3e6f0;
    border-radius: 12px;
    background: white;
    transition: all 0.3s ease;
}

.temp-mail-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(0,123,255,0.1);
}

.temp-mail-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .sidebar .nav-link {
        padding: 10px 15px;
        margin: 1px 5px;
    }

    .temp-mail-card {
        padding: 15px;
    }

    .card {
        margin-bottom: 20px;
    }
}

/* تحسينات الألوان الداكنة */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1a1a1a;
        --dark-color: #ffffff;
    }
}';

        file_put_contents('assets/css/style.css', $css_content);
        $progress++;
        $percentage = round(($progress / $totalSteps) * 100);
        echo "<script>updateProgress($percentage, 'تم إنشاء ملف CSS');</script>";
        echo "✅ ملف CSS محسن<br>";
        flush();
    }

    // إنشاء ملف JavaScript محسن
    if (!file_exists('assets/js/main.js')) {
        $js_content = '// نظام CPA - ملف JavaScript الرئيسي
document.addEventListener("DOMContentLoaded", function() {
    console.log("🚀 نظام CPA تم تحميله بنجاح");

    // تفعيل التأثيرات البصرية
    initAnimations();

    // تفعيل إدارة الإشعارات
    initNotifications();

    // تفعيل إدارة النماذج
    initForms();

    // تفعيل إدارة الجداول
    initTables();
});

// تفعيل التأثيرات البصرية
function initAnimations() {
    // إضافة تأثير fade-in للبطاقات
    const cards = document.querySelectorAll(".card");
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + "s";
        card.classList.add("fade-in-up");
    });

    // تأثير hover للأزرار
    const buttons = document.querySelectorAll(".btn");
    buttons.forEach(button => {
        button.addEventListener("mouseenter", function() {
            this.style.transform = "translateY(-2px)";
        });

        button.addEventListener("mouseleave", function() {
            this.style.transform = "translateY(0)";
        });
    });
}

// إدارة الإشعارات
function initNotifications() {
    // إخفاء الإشعارات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll(".alert-dismissible");
    alerts.forEach(alert => {
        setTimeout(() => {
            const closeBtn = alert.querySelector(".btn-close");
            if (closeBtn) {
                closeBtn.click();
            }
        }, 5000);
    });
}

// إدارة النماذج
function initForms() {
    // تحسين تجربة النماذج
    const forms = document.querySelectorAll("form");
    forms.forEach(form => {
        form.addEventListener("submit", function(e) {
            const submitBtn = this.querySelector("button[type=submit]");
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin me-2\"></i>جاري المعالجة...";
            }
        });
    });

    // تحسين حقول الإدخال
    const inputs = document.querySelectorAll(".form-control");
    inputs.forEach(input => {
        input.addEventListener("focus", function() {
            this.parentElement.classList.add("focused");
        });

        input.addEventListener("blur", function() {
            this.parentElement.classList.remove("focused");
        });
    });
}

// إدارة الجداول
function initTables() {
    // إضافة تأثيرات للجداول
    const tables = document.querySelectorAll(".table");
    tables.forEach(table => {
        const rows = table.querySelectorAll("tbody tr");
        rows.forEach(row => {
            row.addEventListener("mouseenter", function() {
                this.style.backgroundColor = "rgba(0,123,255,0.05)";
            });

            row.addEventListener("mouseleave", function() {
                this.style.backgroundColor = "";
            });
        });
    });
}

// دوال مساعدة
function showNotification(message, type = "info") {
    const notification = document.createElement("div");
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function formatNumber(num) {
    return new Intl.NumberFormat("ar-EG").format(num);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat("ar-EG", {
        style: "currency",
        currency: "USD"
    }).format(amount);
}

// تصدير الدوال للاستخدام العام
window.CPA = {
    showNotification,
    formatNumber,
    formatCurrency
};';

        file_put_contents('assets/js/main.js', $js_content);
        $progress++;
        $percentage = round(($progress / $totalSteps) * 100);
        echo "<script>updateProgress($percentage, 'تم إنشاء ملف JavaScript');</script>";
        echo "✅ ملف JavaScript محسن<br>";
        flush();
    }

    echo "<h3>📄 4. إنشاء الصفحات الكاملة:</h3>";

    // سيتم إكمال إنشاء الصفحات في الجزء التالي...

    echo "</div>"; // إغلاق div log
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

.progress-container {
    width: 100%;
    background-color: #e0e0e0;
    border-radius: 25px;
    margin: 20px 0;
    overflow: hidden;
}

.progress-bar {
    height: 30px;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 25px;
    text-align: center;
    line-height: 30px;
    color: white;
    font-weight: bold;
    width: 0%;
    transition: width 0.3s ease;
}

#log {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-height: 500px;
    overflow-y: auto;
}

.error {
    background: #ffebee;
    color: #c62828;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #f44336;
    margin: 10px 0;
}

h1, h3 {
    color: #333;
}

h1 {
    text-align: center;
    color: #2196F3;
}
</style>

<script>
function updateProgress(percentage, message) {
    const progressBar = document.getElementById('progressBar');
    progressBar.style.width = percentage + '%';
    progressBar.textContent = percentage + '%';
    
    if (message) {
        console.log(message);
    }
}
</script>
