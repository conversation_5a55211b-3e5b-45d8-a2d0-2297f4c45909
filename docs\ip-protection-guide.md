# دليل نظام حماية IP للعروض

## نظرة عامة
نظام حماية IP هو آلية أمان متقدمة تمنع المستخدمين من النقر على نفس العرض عدة مرات من نفس عنوان IP، مما يحمي من الاحتيال ويضمن جودة الزيارات.

## الميزات الرئيسية

### 1. حماية متعددة المستويات
- **حماية لكل عرض**: منع النقر المتكرر على عرض محدد
- **حماية عامة**: منع النقر على جميع العروض لفترة محددة
- **قائمة بيضاء**: استثناء IPs معينة من الحماية

### 2. إعدادات قابلة للتخصيص
- **مدة الحماية**: من 1 إلى 365 يوم
- **ساعة إعادة التعيين**: تحديد وقت يومي لإعادة تعيين الحماية
- **تفعيل/إلغاء تفعيل**: تحكم كامل في النظام

### 3. إدارة متقدمة
- **إحصائيات مفصلة**: عدد IPs المحمية والسجلات النشطة
- **تنظيف تلقائي**: حذف السجلات المنتهية الصلاحية
- **إعادة تعيين**: إمكانية إعادة تعيين الحماية للعروض أو بشكل عام

## كيفية عمل النظام

### 1. عند النقر على العرض
```
المستخدم ينقر على العرض
    ↓
فحص حماية IP
    ↓
إذا كان محمي → رسالة خطأ
إذا لم يكن محمي → متابعة
    ↓
تسجيل النقرة
    ↓
إضافة حماية IP للمدة المحددة
```

### 2. آلية الفحص
```php
// فحص الحماية
if ($ipProtection->isBlocked($user_ip, $offer_id, $user_id)) {
    // IP محمي - منع الوصول
    $protection_info = $ipProtection->getProtectionInfo($user_ip, $offer_id);
    $remaining_hours = ceil($protection_info['remaining_time'] / 3600);
    die("يرجى المحاولة بعد {$remaining_hours} ساعة");
}

// إضافة الحماية بعد النقر
$ipProtection->addProtection($user_ip, $offer_id, $user_id);
```

## الإعدادات المتاحة

### 1. الإعدادات الأساسية

#### تفعيل حماية IP
- **القيمة**: تشغيل/إيقاف
- **الوصف**: تفعيل أو إلغاء تفعيل النظام بالكامل
- **الافتراضي**: مفعل

#### عدد أيام الحماية
- **النطاق**: 1-365 يوم
- **الوصف**: المدة التي يختفي فيها العرض بعد النقر عليه
- **الافتراضي**: 7 أيام

#### ساعة إعادة التعيين
- **النطاق**: 0-23 (24 ساعة)
- **الوصف**: الساعة اليومية لإعادة تعيين الحماية
- **الافتراضي**: 00:00 (منتصف الليل)

### 2. أنواع الحماية

#### حماية لكل عرض منفصل
```
IP: *********** + العرض #123 → محمي لمدة 7 أيام
IP: *********** + العرض #456 → غير محمي (يمكن النقر)
```

#### حماية عامة لجميع العروض
```
IP: *********** → محمي من جميع العروض لمدة 7 أيام
```

### 3. القائمة البيضاء
```
***********00, *********, ***********
```
- IPs مفصولة بفاصلة
- هذه العناوين لا تخضع للحماية
- مفيدة للاختبار أو المديرين

## إدارة النظام

### 1. الوصول للإعدادات
```
الإدارة → حماية IP
URL: /admin/settings/ip-protection.php
```

### 2. الإحصائيات المتاحة
- **IPs محمية حالياً**: عدد عناوين IP المحمية
- **إجمالي سجلات الحماية**: العدد الكلي للسجلات النشطة
- **حسب النوع**: توزيع الحماية (عرض/عام)

### 3. الإجراءات المتاحة

#### تنظيف منتهية الصلاحية
```php
$cleaned = $ipProtection->cleanExpiredProtections();
```
- حذف السجلات المنتهية الصلاحية
- تحسين أداء قاعدة البيانات

#### إعادة تعيين الكل
```php
$reset = $ipProtection->resetAllProtections();
```
- حذف جميع سجلات الحماية
- إعادة تعيين النظام بالكامل

#### إعادة تعيين عرض محدد
```php
$reset = $ipProtection->resetOfferProtection($offer_id);
```
- حذف حماية عرض معين فقط

## التكامل مع النظام

### 1. في صفحة العروض
```php
// فلترة العروض المحمية
foreach ($all_offers as $offer) {
    if (!$ipProtection->isBlocked($user_ip, $offer['id'], $user_id)) {
        $offers[] = $offer;
    }
}
```

### 2. في نظام التتبع
```php
// فحص قبل تسجيل النقرة
if ($ipProtection->isBlocked($user_ip, $offer_id, $user_id)) {
    die('IP محمي');
}

// إضافة حماية بعد النقرة
$ipProtection->addProtection($user_ip, $offer_id, $user_id);
```

## قاعدة البيانات

### جدول ip_protection
```sql
CREATE TABLE ip_protection (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    offer_id INT,
    user_id INT,
    protection_type ENUM('offer', 'global') DEFAULT 'offer',
    blocked_until DATETIME NOT NULL,
    click_count INT DEFAULT 1,
    last_click TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### الفهارس المحسنة
```sql
INDEX idx_ip_offer (ip_address, offer_id)
INDEX idx_ip_blocked_until (ip_address, blocked_until)
INDEX idx_blocked_until (blocked_until)
```

## المهام التلقائية (Cron Jobs)

### تنظيف السجلات المنتهية
```bash
# كل ساعة
0 * * * * /usr/bin/php /path/to/site/cron/cleanup-ip-protection.php

# أو كل 6 ساعات
0 */6 * * * /usr/bin/php /path/to/site/cron/cleanup-ip-protection.php
```

### إعادة تعيين يومية (اختياري)
```bash
# كل يوم في الساعة المحددة
0 0 * * * /usr/bin/php /path/to/site/cron/reset-daily-protection.php
```

## الأمان والأداء

### 1. تحسينات الأداء
- **فهارس محسنة**: استعلامات سريعة
- **تنظيف تلقائي**: منع تراكم البيانات
- **استعلامات محسنة**: تقليل الحمل على قاعدة البيانات

### 2. الأمان
- **منع الاحتيال**: حماية من النقرات المتكررة
- **مراقبة النشاط**: تسجيل جميع الأنشطة
- **قائمة بيضاء**: استثناءات آمنة

### 3. المراقبة
```php
// مراقبة الإحصائيات
$stats = $ipProtection->getProtectionStats();

// تتبع النشاط المشبوه
if ($stats['active_ips'] > 1000) {
    // تنبيه المدير
    sendAlert('عدد كبير من IPs المحمية');
}
```

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. العروض لا تظهر للمستخدمين
**السبب**: حماية IP مفعلة والمستخدم نقر على العرض مسبقاً
**الحل**: 
- تحقق من إعدادات الحماية
- قلل مدة الحماية
- أضف IP المستخدم للقائمة البيضاء للاختبار

#### 2. أداء بطيء في صفحة العروض
**السبب**: استعلامات حماية IP بطيئة
**الحل**:
- تشغيل تنظيف السجلات المنتهية
- تحسين فهارس قاعدة البيانات
- تقليل عدد العروض المعروضة

#### 3. سجلات كثيرة في قاعدة البيانات
**السبب**: عدم تشغيل التنظيف التلقائي
**الحل**:
- إعداد Cron Job للتنظيف
- تشغيل التنظيف يدوياً
- تقليل مدة الحماية

### أدوات التشخيص

#### فحص حالة IP معين
```php
$info = $ipProtection->getProtectionInfo('***********', $offer_id);
print_r($info);
```

#### إحصائيات مفصلة
```php
$stats = $ipProtection->getProtectionStats();
echo "IPs محمية: " . $stats['active_ips'];
echo "سجلات إجمالية: " . $stats['total_protections'];
```

## أفضل الممارسات

### 1. إعداد المدة المناسبة
- **عروض عالية القيمة**: 7-30 يوم
- **عروض منخفضة القيمة**: 1-7 أيام
- **عروض تجريبية**: 1-3 أيام

### 2. استخدام القائمة البيضاء
- أضف IPs المديرين والمطورين
- استخدم للاختبار والتطوير
- لا تضع IPs عامة أو مشبوهة

### 3. المراقبة المنتظمة
- راجع الإحصائيات أسبوعياً
- نظف السجلات المنتهية شهرياً
- راقب الأنشطة المشبوهة

### 4. التوازن بين الأمان والتجربة
- لا تجعل المدة طويلة جداً
- وفر رسائل واضحة للمستخدمين
- اختبر النظام بانتظام

---

**ملاحظة**: تأكد من اختبار النظام في بيئة التطوير قبل تطبيقه في الإنتاج، وراقب تأثيره على تجربة المستخدم والإيرادات.
