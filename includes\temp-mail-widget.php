<?php
/**
 * Widget البريد المؤقت - يمكن إدراجه في أي صفحة
 */
?>

<div class="temp-mail-widget">
    <div class="card shadow-sm">
        <div class="card-header bg-gradient-primary text-white">
            <h6 class="mb-0">
                <i class="fas fa-envelope me-2"></i>
                البريد المؤقت السريع
            </h6>
        </div>
        <div class="card-body p-3">
            <p class="text-muted small mb-3">
                احصل على بريد إلكتروني مؤقت لإكمال العروض بسرعة
            </p>
            
            <div class="d-grid gap-2">
                <a href="https://temp-mail.org/en/" target="_blank" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-at me-1"></i>Temp Mail
                </a>
                <a href="https://www.run2mail.com/en" target="_blank" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-running me-1"></i>Run2Mail
                </a>
                <a href="https://temp-mail.io/en" target="_blank" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-paper-plane me-1"></i>Temp Mail IO
                </a>
            </div>
            
            <div class="text-center mt-3">
                <a href="temp-mail.php" class="btn btn-link btn-sm">
                    <i class="fas fa-arrow-right me-1"></i>المزيد من المواقع
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.temp-mail-widget {
    max-width: 300px;
}

.temp-mail-widget .card {
    border: none;
    border-radius: 10px;
}

.temp-mail-widget .card-header {
    background: linear-gradient(45deg, #4e73df, #224abe);
    border-radius: 10px 10px 0 0;
}

.temp-mail-widget .btn {
    border-radius: 6px;
    transition: all 0.2s ease;
}

.temp-mail-widget .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.temp-mail-widget .btn-link {
    color: #6c757d;
    text-decoration: none;
}

.temp-mail-widget .btn-link:hover {
    color: #495057;
    transform: none;
    box-shadow: none;
}

@media (max-width: 768px) {
    .temp-mail-widget {
        max-width: 100%;
    }
}
</style>

<script>
// تتبع النقرات على روابط البريد المؤقت في الـ widget
document.querySelectorAll('.temp-mail-widget a[target="_blank"]').forEach(function(link) {
    link.addEventListener('click', function() {
        const siteName = this.textContent.trim();
        console.log('تم النقر على موقع البريد المؤقت: ' + siteName);
        
        // يمكن إضافة تتبع Google Analytics هنا
        if (typeof gtag !== 'undefined') {
            gtag('event', 'click', {
                'event_category': 'temp_mail_widget',
                'event_label': siteName
            });
        }
    });
});
</script>
