<?php
/**
 * فئة توليد أسماء المستخدمين الإنجليزية
 */
class UsernameGenerator {
    private $db;
    
    // قوائم الكلمات الإنجليزية
    private $adjectives = [
        'amazing', 'awesome', 'bright', 'clever', 'cool', 'creative', 'dynamic', 'epic', 
        'fantastic', 'fresh', 'genius', 'golden', 'happy', 'incredible', 'lucky', 'magic', 
        'mighty', 'perfect', 'quick', 'smart', 'super', 'swift', 'unique', 'wild', 'wise',
        'active', 'bold', 'brave', 'calm', 'fast', 'free', 'great', 'kind', 'nice', 'pure',
        'rich', 'safe', 'true', 'warm', 'young', 'blue', 'green', 'red', 'silver', 'white'
    ];
    
    private $nouns = [
        'eagle', 'falcon', 'hawk', 'lion', 'tiger', 'wolf', 'bear', 'fox', 'deer', 'rabbit',
        'star', 'moon', 'sun', 'sky', 'ocean', 'river', 'mountain', 'forest', 'flower', 'tree',
        'diamond', 'gold', 'silver', 'crystal', 'pearl', 'ruby', 'emerald', 'sapphire',
        'warrior', 'knight', 'hero', 'champion', 'master', 'legend', 'king', 'queen', 'prince',
        'storm', 'thunder', 'lightning', 'fire', 'ice', 'wind', 'earth', 'water', 'shadow',
        'dream', 'hope', 'joy', 'peace', 'love', 'light', 'power', 'spirit', 'soul', 'heart'
    ];
    
    private $verbs = [
        'run', 'jump', 'fly', 'swim', 'dance', 'sing', 'play', 'win', 'shine', 'glow',
        'rise', 'soar', 'climb', 'race', 'rush', 'dash', 'zoom', 'blast', 'spark', 'flash'
    ];
    
    public function __construct($database) {
        $this->db = $database->getConnection();
    }
    
    /**
     * توليد اسم مستخدم فريد
     */
    public function generateUniqueUsername($maxAttempts = 50) {
        $attempts = 0;
        
        while ($attempts < $maxAttempts) {
            $username = $this->generateUsername();
            
            if ($this->isUsernameAvailable($username)) {
                return $username;
            }
            
            $attempts++;
        }
        
        // إذا فشل التوليد العادي، استخدم timestamp
        return $this->generateWithTimestamp();
    }
    
    /**
     * توليد اسم مستخدم عشوائي
     */
    private function generateUsername() {
        $patterns = [
            'adjective_noun',
            'noun_verb',
            'adjective_verb',
            'verb_noun',
            'adjective_adjective_noun',
            'noun_adjective'
        ];
        
        $pattern = $patterns[array_rand($patterns)];
        
        switch ($pattern) {
            case 'adjective_noun':
                return $this->getRandomAdjective() . $this->getRandomNoun();
                
            case 'noun_verb':
                return $this->getRandomNoun() . $this->getRandomVerb();
                
            case 'adjective_verb':
                return $this->getRandomAdjective() . $this->getRandomVerb();
                
            case 'verb_noun':
                return $this->getRandomVerb() . $this->getRandomNoun();
                
            case 'adjective_adjective_noun':
                return $this->getRandomAdjective() . $this->getRandomAdjective() . $this->getRandomNoun();
                
            case 'noun_adjective':
                return $this->getRandomNoun() . $this->getRandomAdjective();
                
            default:
                return $this->getRandomAdjective() . $this->getRandomNoun();
        }
    }
    
    /**
     * توليد اسم مستخدم مع timestamp عند الفشل
     */
    private function generateWithTimestamp() {
        $base = $this->getRandomAdjective() . $this->getRandomNoun();
        $timestamp = substr(time(), -4); // آخر 4 أرقام من timestamp
        
        $username = $base . $timestamp;
        
        // التأكد من عدم التكرار
        if (!$this->isUsernameAvailable($username)) {
            $username = $base . rand(1000, 9999);
        }
        
        return $username;
    }
    
    /**
     * الحصول على صفة عشوائية
     */
    private function getRandomAdjective() {
        return ucfirst($this->adjectives[array_rand($this->adjectives)]);
    }
    
    /**
     * الحصول على اسم عشوائي
     */
    private function getRandomNoun() {
        return ucfirst($this->nouns[array_rand($this->nouns)]);
    }
    
    /**
     * الحصول على فعل عشوائي
     */
    private function getRandomVerb() {
        return ucfirst($this->verbs[array_rand($this->verbs)]);
    }
    
    /**
     * التحقق من توفر اسم المستخدم
     */
    private function isUsernameAvailable($username) {
        try {
            $query = "SELECT id FROM users WHERE username = :username";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':username', $username);
            $stmt->execute();
            
            return $stmt->rowCount() === 0;
            
        } catch (PDOException $e) {
            logError("خطأ في فحص توفر اسم المستخدم: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * توليد عدة أسماء مستخدمين للاختيار
     */
    public function generateMultipleUsernames($count = 5) {
        $usernames = [];
        $attempts = 0;
        $maxAttempts = $count * 10;
        
        while (count($usernames) < $count && $attempts < $maxAttempts) {
            $username = $this->generateUsername();
            
            if ($this->isUsernameAvailable($username) && !in_array($username, $usernames)) {
                $usernames[] = $username;
            }
            
            $attempts++;
        }
        
        // إذا لم نحصل على العدد المطلوب، أكمل بأسماء مع timestamp
        while (count($usernames) < $count) {
            $username = $this->generateWithTimestamp();
            if (!in_array($username, $usernames)) {
                $usernames[] = $username;
            }
        }
        
        return $usernames;
    }
    
    /**
     * توليد كلمة مرور عشوائية
     */
    public function generateRandomPassword($length = 8) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $password;
    }
    
    /**
     * توليد كلمة مرور من كلمات إنجليزية
     */
    public function generateWordPassword() {
        $words = array_merge($this->adjectives, $this->nouns, $this->verbs);
        $word1 = $words[array_rand($words)];
        $word2 = $words[array_rand($words)];
        
        return ucfirst($word1) . ucfirst($word2);
    }
    
    /**
     * اقتراح أسماء مستخدمين بناءً على نص مدخل
     */
    public function suggestUsernames($input, $count = 3) {
        $suggestions = [];
        $cleanInput = preg_replace('/[^a-zA-Z]/', '', $input);
        
        if (strlen($cleanInput) >= 2) {
            // اقتراحات بناءً على المدخل
            $suggestions[] = ucfirst($cleanInput) . $this->getRandomNoun();
            $suggestions[] = $this->getRandomAdjective() . ucfirst($cleanInput);
            $suggestions[] = ucfirst($cleanInput) . $this->getRandomVerb();
        }
        
        // إضافة اقتراحات عشوائية إضافية
        while (count($suggestions) < $count) {
            $suggestion = $this->generateUsername();
            if (!in_array($suggestion, $suggestions)) {
                $suggestions[] = $suggestion;
            }
        }
        
        // فلترة الأسماء المتاحة فقط
        $availableSuggestions = [];
        foreach ($suggestions as $suggestion) {
            if ($this->isUsernameAvailable($suggestion)) {
                $availableSuggestions[] = $suggestion;
            }
        }
        
        return array_slice($availableSuggestions, 0, $count);
    }
    
    /**
     * إحصائيات أسماء المستخدمين المولدة تلقائياً
     */
    public function getGeneratedUsernamesStats() {
        try {
            $query = "SELECT 
                        COUNT(*) as total_generated,
                        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as generated_today,
                        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as generated_week
                      FROM users 
                      WHERE is_auto_generated = 1";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            logError("خطأ في إحصائيات أسماء المستخدمين: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public function checkPasswordStrength($password) {
        $strength = [
            'score' => 0,
            'feedback' => [],
            'level' => 'weak'
        ];
        
        $length = strlen($password);
        
        // طول كلمة المرور
        if ($length >= 8) {
            $strength['score'] += 25;
        } elseif ($length >= 6) {
            $strength['score'] += 15;
        } elseif ($length >= 4) {
            $strength['score'] += 10;
        } else {
            $strength['feedback'][] = 'كلمة المرور قصيرة جداً';
        }
        
        // وجود أحرف كبيرة وصغيرة
        if (preg_match('/[a-z]/', $password) && preg_match('/[A-Z]/', $password)) {
            $strength['score'] += 25;
        } elseif (preg_match('/[a-zA-Z]/', $password)) {
            $strength['score'] += 15;
        }
        
        // وجود أرقام
        if (preg_match('/[0-9]/', $password)) {
            $strength['score'] += 25;
        }
        
        // وجود رموز خاصة
        if (preg_match('/[^a-zA-Z0-9]/', $password)) {
            $strength['score'] += 25;
        }
        
        // تحديد مستوى القوة
        if ($strength['score'] >= 75) {
            $strength['level'] = 'strong';
        } elseif ($strength['score'] >= 50) {
            $strength['level'] = 'medium';
        } else {
            $strength['level'] = 'weak';
        }
        
        return $strength;
    }
}
?>
