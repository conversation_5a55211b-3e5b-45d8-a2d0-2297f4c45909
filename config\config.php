<?php
/**
 * الإعدادات العامة للموقع
 */

// إعدادات الموقع
define('SITE_NAME', 'CPA Marketing System');
define('SITE_URL', 'https://q12.ct.ws');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات الأمان
define('SECRET_KEY', 'your-secret-key-here-change-this');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات العملة
define('DEFAULT_CURRENCY', 'USD');
define('CURRENCY_SYMBOL', '$');

// إعدادات الدفع
define('MIN_PAYOUT', 50); // الحد الأدنى للسحب
define('PAYOUT_METHODS', ['PayPal', 'Bank Transfer', 'Payoneer']);

// إعدادات التتبع
define('TRACKING_DOMAIN', 'track.cpa-system.com');
define('CLICK_TRACKING', true);
define('CONVERSION_TRACKING', true);

// إعدادات الإشعارات
define('EMAIL_NOTIFICATIONS', true);
define('SMS_NOTIFICATIONS', false);

// إعدادات التطوير
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
define('LOG_FILE', 'logs/error.log');

// بدء الجلسة
session_start();

// تضمين ملف قاعدة البيانات
require_once 'database.php';

// دالة لتسجيل الأخطاء
function logError($message) {
    if (LOG_ERRORS) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        file_put_contents(LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
    }
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// دالة للتحقق من صلاحيات الإدارة
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// دالة لتنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// دالة لتوليد رمز عشوائي
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}
?>
