# 🚀 نظام CPA Marketing المتكامل v2.0

نظام تسويق CPA شامل ومتطور يوفر جميع الأدوات اللازمة لإدارة حملات التسويق بالعمولة بكفاءة عالية مع ميزات متقدمة للحماية والإشعارات.

## ✅ **الموقع جاهز للرفع والتثبيت!**

### 🎯 **الميزات المكتملة:**

#### 🔔 **نظام الإشعارات الشامل**
- ✅ إشعارات منبثقة، بانر، وشريط جانبي
- ✅ إشعارات تلقائية عند إكمال العروض
- ✅ إعدادات قابلة للتخصيص بالكامل
- ✅ أنواع متعددة: معلوماتي، نجاح، تحذير، خطر، عروض، نظام
- ✅ جمهور مستهدف: الجميع، النشطين، غير النشطين، الجدد، مخصص

#### 🛡️ **نظام الحماية المتطور**
- ✅ حماية IP متقدمة مع فترات قابلة للتخصيص
- ✅ فحص جودة IP مع IPQualityScore API
- ✅ منع الاحتيال والنقرات الوهمية
- ✅ تقارير مفصلة عن جودة الترافيك

#### 👥 **إدارة المستخدمين الذكية**
- ✅ تسجيل مبسط (اسم مستخدم + كلمة مرور فقط)
- ✅ توليد أسماء مستخدمين إنجليزية تلقائياً
- ✅ نظام صلاحيات متقدم (مدير/ناشر)
- ✅ لوحات تحكم منفصلة للمدير والمستخدم

#### 🎯 **إدارة العروض المتقدمة**
- ✅ إضافة وإدارة العروض يدوياً مع حقول مخصصة
- ✅ Offerwall متكامل مع عدة طرق عرض
- ✅ نظام Postback متقدم مع دعم CPALead
- ✅ تتبع النقرات والتحويلات في الوقت الفعلي

#### 📧 **أدوات مساعدة للمستخدمين**
- ✅ مواقع البريد المؤقت المدمجة (3 مواقع موثوقة)
- ✅ Widget للوصول السريع
- ✅ نصائح وإرشادات شاملة

#### 💰 **نظام المدفوعات**
- ✅ تتبع الأرباح في الوقت الفعلي
- ✅ تقارير مالية مفصلة
- ✅ معالجة تلقائية للتحويلات

## 🛠️ **التثبيت السريع**

### **الطريقة 1: التثبيت التلقائي (موصى به)**
```bash
1. رفع جميع الملفات إلى مجلد الموقع
2. زيارة: https://yourdomain.com/installer.php
3. اتباع خطوات التثبيت التفاعلية
4. إنشاء حساب المدير
5. البدء في الاستخدام!
```

### **الطريقة 2: التثبيت السريع (للخبراء)**
```bash
1. رفع الملفات
2. زيارة: https://yourdomain.com/install.php
3. إدخال بيانات قاعدة البيانات
4. إنشاء حساب المدير
5. الانتهاء!
```

### **فحص النظام**
```bash
# للتحقق من جاهزية النظام
https://yourdomain.com/system-check.php
```

## 📋 **المتطلبات**

### **متطلبات الخادم:**
```
✅ PHP 7.4+ (موصى به: 8.0+)
✅ MySQL 5.7+ أو MariaDB 10.2+
✅ Apache/Nginx مع mod_rewrite
✅ SSL Certificate (مطلوب للإنتاج)
```

### **إضافات PHP المطلوبة:**
```
✅ PDO
✅ PDO MySQL
✅ cURL
✅ JSON
✅ OpenSSL
✅ mbstring (موصى به)
```

### **صلاحيات المجلدات:**
```
📁 config/ - قابل للكتابة (755)
📁 logs/ - قابل للكتابة (755)
📁 cache/ - قابل للكتابة (755)
📁 backups/ - قابل للكتابة (755)
📁 uploads/ - قابل للكتابة (755)
```

## 🎯 **الملفات الرئيسية**

### **ملفات التثبيت:**
- `installer.php` - ملف التثبيت التفاعلي الجديد
- `install.php` - ملف التثبيت السريع
- `system-check.php` - فحص جاهزية النظام

### **ملفات التكوين:**
- `config/config.php` - إعدادات النظام الأساسية
- `config/database.php` - إعدادات قاعدة البيانات
- `database/schema.sql` - هيكل قاعدة البيانات

### **الملفات الأساسية:**
- `index.php` - الصفحة الرئيسية
- `auth/` - نظام التسجيل والدخول
- `admin/` - لوحة الإدارة
- `offers/` - إدارة العروض
- `tracking/` - نظام التتبع
- `api/` - واجهات برمجة التطبيقات

## 🔧 **التكوين بعد التثبيت**

### **إعدادات أساسية:**
```php
// config/config.php
define('SITE_NAME', 'CPA Marketing System');
define('SITE_URL', 'https://yourdomain.com');
define('ADMIN_EMAIL', '<EMAIL>');
define('CURRENCY_SYMBOL', '$');
```

### **إعدادات الحماية:**
```php
define('IP_PROTECTION_ENABLED', true);
define('IP_PROTECTION_HOURS', 24);
define('IP_QUALITY_ENABLED', true);
define('IPQUALITYSCORE_API_KEY', 'your-api-key');
```

### **إعدادات الإشعارات:**
```php
define('NOTIFICATIONS_ENABLED', true);
define('POPUP_NOTIFICATIONS', true);
define('BANNER_NOTIFICATIONS', true);
define('SIDEBAR_NOTIFICATIONS', true);
```

## 📊 **الميزات المتقدمة**

### **نظام الإشعارات:**
- 🔔 إشعارات فورية عند إكمال العروض
- 📱 عرض متعدد: منبثق، بانر، شريط جانبي
- ⚙️ إعدادات قابلة للتخصيص بالكامل
- 🎯 استهداف دقيق للمستخدمين

### **حماية IP المتطورة:**
- 🛡️ منع النقرات المتكررة من نفس IP
- ⏰ فترات حماية قابلة للتخصيص
- 📊 تقارير مفصلة عن الترافيك المحظور

### **فحص جودة IP:**
- 🔍 تكامل مع IPQualityScore API
- 📈 نقاط جودة شاملة للزوار
- 🚫 فلترة تلقائية للترافيك منخفض الجودة

### **أدوات البريد المؤقت:**
- 📧 3 مواقع موثوقة مدمجة
- 🔗 وصول سريع من داخل النظام
- 📝 نصائح وإرشادات شاملة

## 🔐 **الأمان والحماية**

### **ميزات الأمان:**
```
🔒 تشفير كلمات المرور بـ bcrypt
🛡️ حماية من SQL Injection و XSS
🔐 نظام صلاحيات متعدد المستويات
📝 تسجيل جميع العمليات الحساسة
🚫 حماية الملفات الحساسة
🔍 فحص جودة الترافيك تلقائياً
```

### **إعدادات الأمان الموصى بها:**
```php
define('DEBUG_MODE', false); // false في الإنتاج
define('FORCE_HTTPS', true);
define('SESSION_TIMEOUT', 3600);
define('MAX_LOGIN_ATTEMPTS', 5);
```

## 🔄 **الصيانة والتحديثات**

### **مهام Cron المطلوبة:**
```bash
# تنظيف حماية IP (كل ساعة)
0 * * * * /usr/bin/php /path/to/site/cron/cleanup-ip-protection.php

# تنظيف كاش جودة IP (يومياً)
0 2 * * * /usr/bin/php /path/to/site/cron/cleanup-ip-quality-cache.php

# تنظيف الإشعارات المنتهية (يومياً)
0 3 * * * /usr/bin/php /path/to/site/cron/cleanup-notifications.php
```

## 📚 **الوثائق والأدلة**

### **أدلة التثبيت:**
- 📖 [دليل التثبيت الشامل](INSTALLATION-GUIDE.md)
- 🔧 [دليل التكوين المتقدم](docs/configuration.md)

### **أدلة الاستخدام:**
- 👤 [دليل المستخدم](docs/user-guide.md)
- 👨‍💼 [دليل المدير](docs/admin-guide.md)
- 🔔 [دليل الإشعارات](docs/notifications.md)

### **أدلة التكامل:**
- 🔗 [تكامل CPALead](docs/cpalead-integration.md)
- 📧 [تكامل البريد المؤقت](docs/temp-mail-integration.md)
- 🛡️ [تكامل IPQualityScore](docs/ip-quality-integration.md)

## 🎉 **الخلاصة**

### **✅ النظام جاهز تماماً للرفع والتثبيت مع:**

```
🔔 نظام إشعارات شامل ومتقدم
🛡️ حماية IP متطورة مع فحص الجودة
👥 إدارة مستخدمين ذكية ومرنة
🎯 إدارة عروض متقدمة مع Offerwall
📧 أدوات البريد المؤقت المدمجة
💰 نظام مدفوعات متكامل
🔐 أمان متعدد الطبقات
📊 تقارير وإحصائيات مفصلة
🎨 تصميم عصري ومتجاوب
🌐 دعم كامل للغة العربية
```

### **🚀 خطوات البدء:**
1. **رفع الملفات** إلى الخادم
2. **زيارة installer.php** لبدء التثبيت
3. **إنشاء حساب المدير** الأول
4. **تكوين الإعدادات** حسب الحاجة
5. **البدء في الاستخدام** فوراً!

### **📞 الدعم:**
- 📧 البريد: <EMAIL>
- 💬 الدعم: <EMAIL>
- 📖 الوثائق: [INSTALLATION-GUIDE.md](INSTALLATION-GUIDE.md)

**🎯 النظام مكتمل ومختبر وجاهز للإنتاج!**
