<?php
require_once '../config/config.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    
    $errors = [];
    
    // التحقق من البيانات - متطلبات مبسطة جداً
    if (empty($username)) {
        $errors[] = 'اسم المستخدم مطلوب';
    }
    
    if (empty($password)) {
        $errors[] = 'كلمة المرور مطلوبة';
    }
    
    // التحقق من عدم وجود اسم المستخدم مسبقاً
    if (empty($errors)) {
        $check_query = "SELECT id FROM users WHERE username = :username";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->bindParam(':username', $username);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() > 0) {
            $errors[] = 'اسم المستخدم موجود مسبقاً';
        }
    }
    
    // إنشاء الحساب
    if (empty($errors)) {
        try {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            $insert_query = "INSERT INTO users (username, email, password, full_name, status, role, created_at) 
                            VALUES (:username, :email, :password, :full_name, 'active', 'user', NOW())";
            
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':username', $username);
            $insert_stmt->bindParam(':email', $username . '@local.com'); // بريد افتراضي
            $insert_stmt->bindParam(':password', $hashed_password);
            $insert_stmt->bindParam(':full_name', $username); // اسم كامل افتراضي
            
            if ($insert_stmt->execute()) {
                $user_id = $db->lastInsertId();
                
                // تسجيل النشاط
                $activity_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                                  VALUES (:user_id, 'user_registered', 'تسجيل مستخدم جديد', :data)";
                
                $activity_data = json_encode([
                    'username' => $username,
                    'registration_time' => date('Y-m-d H:i:s'),
                    'ip_address' => $_SERVER['REMOTE_ADDR']
                ]);

                $activity_stmt = $db->prepare($activity_query);
                $activity_stmt->bindParam(':user_id', $user_id);
                $activity_stmt->bindParam(':data', $activity_data);
                $activity_stmt->execute();
                
                $_SESSION['success'] = 'تم إنشاء الحساب بنجاح! يمكنك تسجيل الدخول الآن.';
                header('Location: login.php');
                exit();
            } else {
                $errors[] = 'حدث خطأ في إنشاء الحساب';
            }
            
        } catch (PDOException $e) {
            logError("خطأ في التسجيل: " . $e->getMessage());
            $errors[] = 'حدث خطأ في النظام';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        .register-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: bold;
            transition: transform 0.2s ease;
        }
        .btn-register:hover {
            transform: translateY(-2px);
        }
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
        }
        .form-control {
            border-left: none;
        }
        .requirements {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="register-card">
        <div class="register-header">
            <i class="fas fa-user-plus fa-3x mb-3"></i>
            <h3>إنشاء حساب جديد</h3>
            <p class="mb-0">انضم إلينا واربح المال</p>
        </div>
        
        <div class="register-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form method="POST" id="registerForm">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                               placeholder="أدخل اسم المستخدم" required>
                    </div>
                    <div class="requirements">
                        <i class="fas fa-info-circle me-1"></i>
                        يمكن استخدام حرف واحد أو أكثر
                    </div>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="أدخل كلمة المرور" required>
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="requirements">
                        <i class="fas fa-info-circle me-1"></i>
                        يمكن استخدام حرف واحد أو أكثر
                    </div>
                </div>

                <div class="d-grid mb-3">
                    <button type="submit" class="btn btn-primary btn-register">
                        <i class="fas fa-user-plus me-2"></i>
                        إنشاء الحساب
                    </button>
                </div>
            </form>

            <div class="text-center">
                <p class="text-muted mb-0">
                    لديك حساب بالفعل؟ 
                    <a href="login.php" class="text-decoration-none">سجل دخولك هنا</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // تحسين تجربة المستخدم
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
            submitBtn.disabled = true;
        });

        // التحقق من صحة البيانات أثناء الكتابة
        document.getElementById('username').addEventListener('input', function() {
            const value = this.value.trim();
            const requirements = this.parentElement.nextElementSibling;
            
            if (value.length === 0) {
                requirements.innerHTML = '<i class="fas fa-exclamation-circle me-1 text-danger"></i>اسم المستخدم مطلوب';
                requirements.className = 'requirements text-danger';
            } else {
                requirements.innerHTML = '<i class="fas fa-check-circle me-1 text-success"></i>اسم المستخدم صحيح';
                requirements.className = 'requirements text-success';
            }
        });

        document.getElementById('password').addEventListener('input', function() {
            const value = this.value;
            const requirements = this.parentElement.nextElementSibling;
            
            if (value.length === 0) {
                requirements.innerHTML = '<i class="fas fa-exclamation-circle me-1 text-danger"></i>كلمة المرور مطلوبة';
                requirements.className = 'requirements text-danger';
            } else {
                requirements.innerHTML = '<i class="fas fa-check-circle me-1 text-success"></i>كلمة المرور صحيحة';
                requirements.className = 'requirements text-success';
            }
        });
    </script>
</body>
</html>
