<?php
/**
 * مثال على سكريبت Postback مخصص
 * يمكن استخدام هذا المثال كنقطة بداية لتطوير سكريبت Postback مخصص
 * متوافق مع CPALead ومعظم شبكات CPA الأخرى
 */

// إعدادات قاعدة البيانات
define("DB_HOST", "localhost");
define("DB_PORT", "3306");
define("DB_NAME", "your_database");
define("DB_TABLE", "your_conversions_table");
define("DB_USER", "your_username");
define("DB_PASS", "your_password");

// إعدادات الأمان
$postbackPassword = "your_secure_password_here";
$allowedIPs = [
    '************',  // CPALead IP
    '127.0.0.1',     // للاختبار المحلي
    '::1'            // IPv6 localhost
];

// دالة لتسجيل الأخطاء
function logError($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    file_put_contents('postback_errors.log', $logMessage, FILE_APPEND | LOCK_EX);
}

// دالة لتنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// التحقق من IP المصدر
$clientIP = $_SERVER['REMOTE_ADDR'];
if (!in_array($clientIP, $allowedIPs)) {
    logError("Postback من IP غير مصرح: $clientIP");
    http_response_code(403);
    die("IP غير مصرح");
}

// إنشاء اتصال بقاعدة البيانات
try {
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME, DB_PORT);
    
    if ($mysqli->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $mysqli->connect_error);
    }
    
    $mysqli->set_charset("utf8mb4");
} catch (Exception $e) {
    logError("خطأ في قاعدة البيانات: " . $e->getMessage());
    http_response_code(500);
    die("خطأ في النظام");
}

// استقبال معاملات Postback
$postbackData = [
    // المعاملات الأساسية
    'click_id' => sanitize($_REQUEST['click_id'] ?? ''),
    'transaction_id' => sanitize($_REQUEST['transaction_id'] ?? ''),
    'payout' => floatval($_REQUEST['payout'] ?? 0),
    'status' => sanitize($_REQUEST['status'] ?? 'pending'),
    'password' => sanitize($_REQUEST['password'] ?? ''),
    
    // معاملات CPALead
    'campaign_id' => sanitize($_REQUEST['campaign_id'] ?? ''),
    'campaign_name' => sanitize($_REQUEST['campaign_name'] ?? ''),
    'lead_id' => sanitize($_REQUEST['lead_id'] ?? ''),
    'gateway_id' => sanitize($_REQUEST['gateway_id'] ?? ''),
    'virtual_currency' => floatval($_REQUEST['virtual_currency'] ?? 0),
    'country_iso' => sanitize($_REQUEST['country_iso'] ?? ''),
    
    // Sub IDs
    'subid' => sanitize($_REQUEST['subid'] ?? ''),
    'subid2' => sanitize($_REQUEST['subid2'] ?? ''),
    'subid3' => sanitize($_REQUEST['subid3'] ?? ''),
    
    // معرفات الأجهزة المحمولة
    'idfa' => sanitize($_REQUEST['idfa'] ?? ''),
    'gaid' => sanitize($_REQUEST['gaid'] ?? ''),
    
    // معلومات إضافية
    'ip_address' => $clientIP,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
    'timestamp' => date('Y-m-d H:i:s')
];

// التحقق من كلمة المرور
if (!empty($postbackPassword) && $postbackData['password'] !== $postbackPassword) {
    logError("كلمة مرور Postback خاطئة من IP: $clientIP");
    http_response_code(401);
    die("كلمة مرور غير صحيحة");
}

// التحقق من وجود معرف النقرة أو SubID
if (empty($postbackData['click_id']) && empty($postbackData['subid'])) {
    logError("معرف النقرة أو SubID مفقود");
    http_response_code(400);
    die("معرف النقرة أو SubID مطلوب");
}

try {
    // البحث عن النقرة في قاعدة البيانات
    $clickData = null;
    
    if (!empty($postbackData['click_id'])) {
        // البحث بـ click_id
        $query = "SELECT * FROM clicks WHERE id = ? LIMIT 1";
        $stmt = $mysqli->prepare($query);
        $stmt->bind_param('i', $postbackData['click_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $clickData = $result->fetch_assoc();
        $stmt->close();
    } elseif (!empty($postbackData['subid'])) {
        // البحث بـ subid
        $query = "SELECT * FROM clicks WHERE sub_id = ? ORDER BY clicked_at DESC LIMIT 1";
        $stmt = $mysqli->prepare($query);
        $stmt->bind_param('s', $postbackData['subid']);
        $stmt->execute();
        $result = $stmt->get_result();
        $clickData = $result->fetch_assoc();
        $stmt->close();
    }
    
    if (!$clickData) {
        logError("النقرة غير موجودة - Click ID: {$postbackData['click_id']}, SubID: {$postbackData['subid']}");
        http_response_code(404);
        die("النقرة غير موجودة");
    }
    
    // التحقق من وجود تحويل مسبق
    $existingQuery = "SELECT id FROM conversions WHERE click_id = ? LIMIT 1";
    $existingStmt = $mysqli->prepare($existingQuery);
    $existingStmt->bind_param('i', $clickData['id']);
    $existingStmt->execute();
    $existingResult = $existingStmt->get_result();
    $existingConversion = $existingResult->fetch_assoc();
    $existingStmt->close();
    
    if ($existingConversion) {
        // تحديث التحويل الموجود
        $updateQuery = "UPDATE conversions SET 
                        transaction_id = ?, 
                        lead_id = ?, 
                        campaign_id = ?, 
                        campaign_name = ?, 
                        gateway_id = ?, 
                        payout = ?, 
                        virtual_currency = ?, 
                        status = ?, 
                        country_iso = ?, 
                        conversion_data = ?,
                        updated_at = NOW()
                        WHERE click_id = ?";
        
        $conversionData = json_encode($postbackData);
        $updateStmt = $mysqli->prepare($updateQuery);
        $updateStmt->bind_param('sssssdssssi', 
            $postbackData['transaction_id'],
            $postbackData['lead_id'],
            $postbackData['campaign_id'],
            $postbackData['campaign_name'],
            $postbackData['gateway_id'],
            $postbackData['payout'],
            $postbackData['virtual_currency'],
            $postbackData['status'],
            $postbackData['country_iso'],
            $conversionData,
            $clickData['id']
        );
        
        if ($updateStmt->execute()) {
            echo "OK - تم تحديث التحويل بنجاح";
            logError("تم تحديث التحويل - Click ID: {$clickData['id']}, Lead ID: {$postbackData['lead_id']}");
        } else {
            throw new Exception("فشل في تحديث التحويل: " . $updateStmt->error);
        }
        $updateStmt->close();
        
    } else {
        // إنشاء تحويل جديد
        $insertQuery = "INSERT INTO conversions (
            click_id, user_id, offer_id, transaction_id, lead_id, 
            campaign_id, campaign_name, gateway_id, payout, virtual_currency, 
            status, country_iso, sub_id, sub_id2, sub_id3, 
            idfa, gaid, ip_address, conversion_data, converted_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $conversionData = json_encode($postbackData);
        $insertStmt = $mysqli->prepare($insertQuery);
        $insertStmt->bind_param('iiisssssdssssssss',
            $clickData['id'],
            $clickData['user_id'],
            $clickData['offer_id'],
            $postbackData['transaction_id'],
            $postbackData['lead_id'],
            $postbackData['campaign_id'],
            $postbackData['campaign_name'],
            $postbackData['gateway_id'],
            $postbackData['payout'],
            $postbackData['virtual_currency'],
            $postbackData['status'],
            $postbackData['country_iso'],
            $postbackData['subid'],
            $postbackData['subid2'],
            $postbackData['subid3'],
            $postbackData['idfa'],
            $postbackData['gaid'],
            $postbackData['ip_address'],
            $conversionData
        );
        
        if ($insertStmt->execute()) {
            $conversionId = $mysqli->insert_id;
            echo "OK - تم تسجيل التحويل بنجاح (ID: $conversionId)";
            logError("تم تسجيل تحويل جديد - Conversion ID: $conversionId, Lead ID: {$postbackData['lead_id']}");
            
            // تحديث رصيد المستخدم إذا كان التحويل معتمد
            if ($postbackData['status'] === 'approved' && $postbackData['payout'] > 0) {
                $balanceQuery = "UPDATE users SET 
                                balance = balance + ?, 
                                total_earnings = total_earnings + ? 
                                WHERE id = ?";
                $balanceStmt = $mysqli->prepare($balanceQuery);
                $balanceStmt->bind_param('ddi', 
                    $postbackData['payout'], 
                    $postbackData['payout'], 
                    $clickData['user_id']
                );
                $balanceStmt->execute();
                $balanceStmt->close();
            }
            
        } else {
            throw new Exception("فشل في إدراج التحويل: " . $insertStmt->error);
        }
        $insertStmt->close();
    }
    
} catch (Exception $e) {
    logError("خطأ في معالجة Postback: " . $e->getMessage());
    http_response_code(500);
    die("خطأ في النظام");
}

// إغلاق الاتصال
$mysqli->close();

// تسجيل نجاح العملية
logError("Postback ناجح - Click ID: {$postbackData['click_id']}, SubID: {$postbackData['subid']}, Payout: {$postbackData['payout']}");
?>
