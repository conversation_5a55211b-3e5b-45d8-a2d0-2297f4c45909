<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit();
}

$offer_id = intval($_GET['offer_id'] ?? 0);
if ($offer_id <= 0) {
    header('Location: ../offers/');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

// جلب معلومات العرض
$offer_query = "SELECT o.*, n.name as network_name 
                FROM offers o 
                JOIN networks n ON o.network_id = n.id 
                WHERE o.id = :offer_id AND o.status = 'active'";

$offer_stmt = $db->prepare($offer_query);
$offer_stmt->bindParam(':offer_id', $offer_id);
$offer_stmt->execute();

$offer = $offer_stmt->fetch(PDO::FETCH_ASSOC);

if (!$offer) {
    $_SESSION['error'] = 'العرض غير موجود أو غير متاح';
    header('Location: ../offers/');
    exit();
}

// معالجة إنشاء رابط التتبع
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $sub_id = sanitize($_POST['sub_id'] ?? '');
    $source = sanitize($_POST['source'] ?? '');
    $campaign = sanitize($_POST['campaign'] ?? '');
    
    try {
        // التحقق من وجود رابط تتبع مسبق بنفس المعاملات
        $existing_query = "SELECT tracking_id, tracking_url FROM tracking_links 
                          WHERE user_id = :user_id AND offer_id = :offer_id 
                          AND sub_id = :sub_id AND source = :source AND campaign = :campaign 
                          AND status = 'active'";
        
        $existing_stmt = $db->prepare($existing_query);
        $existing_stmt->bindParam(':user_id', $user_id);
        $existing_stmt->bindParam(':offer_id', $offer_id);
        $existing_stmt->bindParam(':sub_id', $sub_id);
        $existing_stmt->bindParam(':source', $source);
        $existing_stmt->bindParam(':campaign', $campaign);
        $existing_stmt->execute();
        
        $existing_link = $existing_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing_link) {
            $tracking_id = $existing_link['tracking_id'];
            $tracking_url = $existing_link['tracking_url'];
        } else {
            // إنشاء معرف تتبع جديد
            $tracking_id = generateToken(16);
            
            // بناء رابط التتبع
            $tracking_url = SITE_URL . "/tracking/click.php?t=" . $tracking_id;
            
            // إدراج رابط التتبع في قاعدة البيانات
            $insert_query = "INSERT INTO tracking_links (
                user_id, offer_id, tracking_id, original_url, tracking_url, 
                sub_id, source, campaign, status
            ) VALUES (
                :user_id, :offer_id, :tracking_id, :original_url, :tracking_url,
                :sub_id, :source, :campaign, 'active'
            )";
            
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':user_id', $user_id);
            $insert_stmt->bindParam(':offer_id', $offer_id);
            $insert_stmt->bindParam(':tracking_id', $tracking_id);
            $insert_stmt->bindParam(':original_url', $offer['tracking_url']);
            $insert_stmt->bindParam(':tracking_url', $tracking_url);
            $insert_stmt->bindParam(':sub_id', $sub_id);
            $insert_stmt->bindParam(':source', $source);
            $insert_stmt->bindParam(':campaign', $campaign);
            $insert_stmt->execute();
        }
        
        // تسجيل النشاط
        $activity_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                          VALUES (:user_id, 'generate_link', :description, :data)";
        
        $activity_description = "إنشاء رابط تتبع للعرض: " . $offer['title'];
        $activity_data = json_encode([
            'offer_id' => $offer_id,
            'tracking_id' => $tracking_id,
            'sub_id' => $sub_id,
            'source' => $source,
            'campaign' => $campaign
        ]);

        $activity_stmt = $db->prepare($activity_query);
        $activity_stmt->bindParam(':user_id', $user_id);
        $activity_stmt->bindParam(':description', $activity_description);
        $activity_stmt->bindParam(':data', $activity_data);
        $activity_stmt->execute();
        
        $success_message = 'تم إنشاء رابط التتبع بنجاح!';
        
    } catch (PDOException $e) {
        logError("خطأ في إنشاء رابط التتبع: " . $e->getMessage());
        $error_message = 'حدث خطأ في إنشاء رابط التتبع';
    }
}

// جلب روابط التتبع الموجودة للعرض
$links_query = "SELECT * FROM tracking_links 
                WHERE user_id = :user_id AND offer_id = :offer_id 
                ORDER BY created_at DESC";

$links_stmt = $db->prepare($links_query);
$links_stmt->bindParam(':user_id', $user_id);
$links_stmt->bindParam(':offer_id', $offer_id);
$links_stmt->execute();
$existing_links = $links_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء رابط تتبع - <?php echo htmlspecialchars($offer['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إنشاء رابط تتبع</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="../offers/" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للعروض
                        </a>
                    </div>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- معلومات العرض -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">معلومات العرض</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5><?php echo htmlspecialchars($offer['title']); ?></h5>
                                <p class="text-muted"><?php echo htmlspecialchars($offer['description']); ?></p>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>الشبكة:</strong> <?php echo htmlspecialchars($offer['network_name']); ?>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>النوع:</strong> 
                                        <span class="badge bg-info"><?php echo strtoupper($offer['type']); ?></span>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-sm-6">
                                        <strong>العمولة:</strong> 
                                        <span class="text-success"><?php echo CURRENCY_SYMBOL . number_format($offer['payout'], 2); ?></span>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>البلدان:</strong> <?php echo htmlspecialchars($offer['countries']); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <?php if ($offer['image']): ?>
                                    <img src="../uploads/offers/<?php echo $offer['image']; ?>" 
                                         class="img-fluid rounded" alt="صورة العرض" style="max-height: 150px;">
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج إنشاء رابط التتبع -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">إنشاء رابط تتبع جديد</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="sub_id" class="form-label">Sub ID (اختياري)</label>
                                    <input type="text" class="form-control" id="sub_id" name="sub_id" 
                                           placeholder="معرف فرعي للتتبع">
                                    <small class="text-muted">يمكن استخدامه لتتبع مصادر مختلفة</small>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="source" class="form-label">المصدر (اختياري)</label>
                                    <input type="text" class="form-control" id="source" name="source" 
                                           placeholder="مثل: facebook, google, website">
                                    <small class="text-muted">مصدر الزيارات</small>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="campaign" class="form-label">الحملة (اختياري)</label>
                                    <input type="text" class="form-control" id="campaign" name="campaign" 
                                           placeholder="اسم الحملة الإعلانية">
                                    <small class="text-muted">اسم الحملة للتتبع</small>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-link me-1"></i>إنشاء رابط التتبع
                            </button>
                        </form>
                    </div>
                </div>

                <!-- عرض رابط التتبع المُنشأ -->
                <?php if (isset($tracking_url)): ?>
                <div class="card shadow mb-4">
                    <div class="card-header py-3 bg-success text-white">
                        <h6 class="m-0 font-weight-bold">رابط التتبع الجديد</h6>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="text" class="form-control" id="trackingUrl" 
                                   value="<?php echo htmlspecialchars($tracking_url); ?>" readonly>
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="copyToClipboard('<?php echo htmlspecialchars($tracking_url); ?>')">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            <strong>معرف التتبع:</strong> <?php echo htmlspecialchars($tracking_id); ?>
                        </small>
                    </div>
                </div>
                <?php endif; ?>

                <!-- الروابط الموجودة -->
                <?php if (!empty($existing_links)): ?>
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">روابط التتبع الموجودة</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>معرف التتبع</th>
                                        <th>Sub ID</th>
                                        <th>المصدر</th>
                                        <th>الحملة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($existing_links as $link): ?>
                                    <tr>
                                        <td>
                                            <code><?php echo htmlspecialchars($link['tracking_id']); ?></code>
                                        </td>
                                        <td><?php echo htmlspecialchars($link['sub_id'] ?: '-'); ?></td>
                                        <td><?php echo htmlspecialchars($link['source'] ?: '-'); ?></td>
                                        <td><?php echo htmlspecialchars($link['campaign'] ?: '-'); ?></td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($link['created_at'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $link['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo $link['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="copyToClipboard('<?php echo htmlspecialchars($link['tracking_url']); ?>')">
                                                <i class="fas fa-copy"></i> نسخ
                                            </button>
                                            <a href="../reports/link_stats.php?tracking_id=<?php echo $link['tracking_id']; ?>" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-chart-bar"></i> إحصائيات
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
