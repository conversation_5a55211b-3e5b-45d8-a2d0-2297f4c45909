# 🚀 دليل التثبيت الشامل - نظام CPA Marketing

## 📋 المحتويات
1. [المتطلبات](#المتطلبات)
2. [التحضير للتثبيت](#التحضير-للتثبيت)
3. [طرق التثبيت](#طرق-التثبيت)
4. [التكوين بعد التثبيت](#التكوين-بعد-التثبيت)
5. [استكشاف الأخطاء](#استكشاف-الأخطاء)
6. [الأمان والحماية](#الأمان-والحماية)

---

## 🔧 المتطلبات

### متطلبات الخادم:
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث (أو MariaDB 10.2+)
- **Apache/Nginx**: مع mod_rewrite مفعل
- **SSL Certificate**: موصى به للإنتاج

### إضافات PHP المطلوبة:
```
✅ PDO
✅ PDO MySQL
✅ cURL
✅ JSON
✅ OpenSSL
✅ mbstring
✅ GD (اختياري للصور)
```

### صلاحيات المجلدات:
```
📁 config/ - قابل للكتابة (755)
📁 logs/ - قابل للكتابة (755)
📁 cache/ - قابل للكتابة (755)
📁 backups/ - قابل للكتابة (755)
📁 uploads/ - قابل للكتابة (755)
```

---

## 📦 التحضير للتثبيت

### 1. تحميل الملفات:
```bash
# رفع جميع ملفات النظام إلى مجلد الموقع
# التأكد من رفع المجلدات المخفية (.htaccess)
```

### 2. إنشاء قاعدة البيانات:
```sql
CREATE DATABASE cpa_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. تحضير معلومات قاعدة البيانات:
```
🔹 اسم الخادم (Host)
🔹 اسم المستخدم (Username)
🔹 كلمة المرور (Password)
🔹 اسم قاعدة البيانات (Database Name)
```

---

## 🛠️ طرق التثبيت

### الطريقة 1: التثبيت التلقائي (موصى به)

#### الخطوة 1: الوصول لملف التثبيت
```
https://yourdomain.com/installer.php
```

#### الخطوة 2: فحص المتطلبات
- سيتم فحص جميع المتطلبات تلقائياً
- إصلاح أي مشاكل قبل المتابعة

#### الخطوة 3: إعداد قاعدة البيانات
```
🔹 Host: localhost (أو عنوان الخادم)
🔹 Username: اسم مستخدم قاعدة البيانات
🔹 Password: كلمة مرور قاعدة البيانات
🔹 Database: اسم قاعدة البيانات
```

#### الخطوة 4: إنشاء حساب المدير
```
🔹 اسم المستخدم: admin (أو أي اسم تريده)
🔹 البريد الإلكتروني: <EMAIL>
🔹 كلمة المرور: كلمة مرور قوية
🔹 الاسم الكامل: اسم المدير
```

#### الخطوة 5: إعدادات الموقع
```
🔹 اسم الموقع: CPA Marketing System
🔹 رابط الموقع: https://yourdomain.com
🔹 بريد المدير: <EMAIL>
```

### الطريقة 2: التثبيت اليدوي

#### 1. تحديث ملف database.php:
```php
<?php
class Database {
    private $host = 'localhost';
    private $db_name = 'cpa_system';
    private $username = 'your_username';
    private $password = 'your_password';
    // باقي الكود...
}
?>
```

#### 2. استيراد قاعدة البيانات:
```bash
mysql -u username -p database_name < database/schema.sql
```

#### 3. تحديث ملف config.php:
```php
define('SITE_NAME', 'CPA Marketing System');
define('SITE_URL', 'https://yourdomain.com');
define('ADMIN_EMAIL', '<EMAIL>');
```

#### 4. إنشاء حساب المدير يدوياً:
```sql
INSERT INTO users (username, email, password, first_name, last_name, role, status, api_key, created_at) 
VALUES ('admin', '<EMAIL>', '$2y$10$hashed_password', 'مدير', 'النظام', 'admin', 'active', 'api_key_here', NOW());
```

---

## ⚙️ التكوين بعد التثبيت

### 1. إعدادات الأمان:
```php
// في config/config.php
define('DEBUG_MODE', false); // إيقاف في الإنتاج
define('FORCE_HTTPS', true); // إجبار HTTPS
define('SESSION_TIMEOUT', 3600); // مهلة الجلسة
```

### 2. إعدادات البريد الإلكتروني:
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

### 3. إعدادات IP Quality:
```php
define('IPQUALITYSCORE_API_KEY', 'your-api-key');
define('IP_QUALITY_ENABLED', true);
```

### 4. إعدادات الإشعارات:
```
✅ تفعيل الإشعارات المنبثقة
✅ تفعيل إشعارات البانر
✅ تفعيل أصوات الإشعارات
⏰ مدة الإخفاء التلقائي: 10 ثوان
```

### 5. إعدادات حماية IP:
```
🔒 فترة الحماية: 24 ساعة
🔒 عدد النقرات المسموحة: 1
🔒 تفعيل نظام الحماية: نعم
```

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ الاتصال بقاعدة البيانات:
```
❌ المشكلة: "Connection failed: Access denied"
✅ الحل: تحقق من بيانات قاعدة البيانات في config/database.php
```

#### 2. خطأ صلاحيات الملفات:
```
❌ المشكلة: "Permission denied"
✅ الحل: chmod 755 للمجلدات و chmod 644 للملفات
```

#### 3. خطأ في إضافات PHP:
```
❌ المشكلة: "Call to undefined function"
✅ الحل: تثبيت الإضافات المطلوبة من cPanel أو الخادم
```

#### 4. مشكلة في الـ URL Rewriting:
```
❌ المشكلة: "404 Not Found"
✅ الحل: تفعيل mod_rewrite في Apache
```

### ملفات السجلات:
```
📄 logs/error.log - أخطاء النظام
📄 logs/access.log - سجل الوصول
📄 logs/conversion.log - سجل التحويلات
```

---

## 🛡️ الأمان والحماية

### 1. حماية الملفات الحساسة:
```apache
# .htaccess في مجلد config/
<Files "*.php">
    Require all denied
</Files>
```

### 2. تحديث كلمات المرور:
```
🔑 كلمة مرور المدير: قوية ومعقدة
🔑 كلمة مرور قاعدة البيانات: فريدة وآمنة
🔑 مفاتيح API: تجديد دوري
```

### 3. إعدادات SSL:
```
✅ شهادة SSL صالحة
✅ إعادة توجيه HTTP إلى HTTPS
✅ HSTS Headers مفعلة
```

### 4. النسخ الاحتياطية:
```bash
# نسخة احتياطية يومية
0 2 * * * /usr/bin/php /path/to/site/cron/backup-database.php
```

### 5. تحديثات الأمان:
```
🔄 تحديث PHP بانتظام
🔄 تحديث MySQL/MariaDB
🔄 مراقبة ملفات السجلات
🔄 فحص الثغرات الأمنية
```

---

## 📞 الدعم والمساعدة

### في حالة مواجهة مشاكل:

1. **تحقق من ملفات السجلات**
2. **راجع دليل استكشاف الأخطاء**
3. **تأكد من المتطلبات**
4. **اتصل بالدعم الفني**

### معلومات النظام:
```
📊 الإصدار: 2.0
📅 تاريخ الإصدار: 2024
🔧 متطلبات PHP: 7.4+
🗄️ متطلبات MySQL: 5.7+
```

---

## ✅ قائمة التحقق بعد التثبيت

- [ ] تسجيل الدخول كمدير يعمل
- [ ] إنشاء عرض تجريبي
- [ ] اختبار نظام التتبع
- [ ] فحص الإشعارات
- [ ] اختبار نظام الدفع
- [ ] تكوين النسخ الاحتياطية
- [ ] إعداد مهام Cron
- [ ] اختبار الأمان
- [ ] تحسين الأداء

**🎉 مبروك! تم تثبيت النظام بنجاح**

للمساعدة والدعم: <EMAIL>
