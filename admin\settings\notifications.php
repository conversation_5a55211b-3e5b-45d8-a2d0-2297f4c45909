<?php
require_once '../../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../auth/login.php');
    exit();
}

$database = new Database();
$notificationManager = new NotificationManager($database);

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = sanitize($_POST['action']);
        
        switch ($action) {
            case 'update_settings':
                $settings = [
                    'notifications_enabled' => isset($_POST['notifications_enabled']) ? '1' : '0',
                    'popup_notifications' => isset($_POST['popup_notifications']) ? '1' : '0',
                    'banner_notifications' => isset($_POST['banner_notifications']) ? '1' : '0',
                    'sidebar_notifications' => isset($_POST['sidebar_notifications']) ? '1' : '0',
                    'auto_hide_notifications' => isset($_POST['auto_hide_notifications']) ? '1' : '0',
                    'notification_hide_delay' => intval($_POST['notification_hide_delay']),
                    'max_notifications_display' => intval($_POST['max_notifications_display']),
                    'notification_sound' => isset($_POST['notification_sound']) ? '1' : '0',
                    'offer_notifications' => isset($_POST['offer_notifications']) ? '1' : '0',
                    'system_notifications' => isset($_POST['system_notifications']) ? '1' : '0'
                ];
                
                if ($notificationManager->updateSettings($settings)) {
                    $_SESSION['success'] = 'تم تحديث إعدادات الإشعارات بنجاح!';
                } else {
                    $_SESSION['error'] = 'حدث خطأ في تحديث الإعدادات';
                }
                break;
                
            case 'test_notification':
                $test_data = [
                    'title' => 'إشعار تجريبي',
                    'message' => 'هذا إشعار تجريبي لاختبار النظام',
                    'type' => 'info',
                    'target_audience' => 'custom',
                    'target_users' => json_encode([$_SESSION['user_id']]),
                    'offer_id' => null,
                    'priority' => 'medium',
                    'show_popup' => true,
                    'show_banner' => true,
                    'show_sidebar' => true,
                    'auto_hide' => true,
                    'hide_after_seconds' => 5,
                    'start_date' => date('Y-m-d H:i:s'),
                    'end_date' => date('Y-m-d H:i:s', strtotime('+1 hour')),
                    'created_by' => $_SESSION['user_id']
                ];
                
                if ($notificationManager->createNotification($test_data)) {
                    $_SESSION['success'] = 'تم إرسال الإشعار التجريبي بنجاح!';
                } else {
                    $_SESSION['error'] = 'فشل في إرسال الإشعار التجريبي';
                }
                break;
                
            case 'clean_expired':
                $cleaned = $notificationManager->cleanExpiredNotifications();
                $_SESSION['success'] = "تم حذف {$cleaned} إشعار منتهي الصلاحية";
                break;
        }
    }
    
    header('Location: notifications.php');
    exit();
}

// جلب الإحصائيات
$stats = $notificationManager->getNotificationStats();
$settings = $notificationManager->getAllSettings();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الإشعارات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إعدادات الإشعارات</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="test_notification">
                                <button type="submit" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-vial me-1"></i>إشعار تجريبي
                                </button>
                            </form>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="clean_expired">
                                <button type="submit" class="btn btn-outline-warning btn-sm" 
                                        onclick="return confirm('حذف الإشعارات المنتهية الصلاحية؟')">
                                    <i class="fas fa-broom me-1"></i>تنظيف
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- الإحصائيات -->
                    <div class="col-lg-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إحصائيات الإشعارات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-12 mb-3">
                                        <h4 class="text-primary"><?php echo number_format($stats['total_notifications'] ?? 0); ?></h4>
                                        <small class="text-muted">إجمالي الإشعارات</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-success"><?php echo number_format($stats['active_notifications'] ?? 0); ?></h5>
                                        <small class="text-muted">نشطة</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-info"><?php echo number_format($stats['offer_notifications'] ?? 0); ?></h5>
                                        <small class="text-muted">عروض</small>
                                    </div>
                                </div>
                                
                                <hr>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>اليوم:</span>
                                    <strong><?php echo number_format($stats['notifications_today'] ?? 0); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الأسبوع:</span>
                                    <strong><?php echo number_format($stats['notifications_week'] ?? 0); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>مقروءة:</span>
                                    <strong class="text-success"><?php echo number_format($stats['read_notifications'] ?? 0); ?></strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>مخفية:</span>
                                    <strong class="text-warning"><?php echo number_format($stats['dismissed_notifications'] ?? 0); ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإعدادات -->
                    <div class="col-lg-8">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">إعدادات نظام الإشعارات</h6>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_settings">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary">الإعدادات العامة</h6>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="notifications_enabled" 
                                                       name="notifications_enabled" 
                                                       <?php echo $settings['notifications_enabled'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="notifications_enabled">
                                                    <strong>تفعيل نظام الإشعارات</strong>
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="popup_notifications" 
                                                       name="popup_notifications" 
                                                       <?php echo $settings['popup_notifications'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="popup_notifications">
                                                    إشعارات منبثقة
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="banner_notifications" 
                                                       name="banner_notifications" 
                                                       <?php echo $settings['banner_notifications'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="banner_notifications">
                                                    إشعارات البانر
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="sidebar_notifications" 
                                                       name="sidebar_notifications" 
                                                       <?php echo $settings['sidebar_notifications'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="sidebar_notifications">
                                                    إشعارات الشريط الجانبي
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="notification_sound" 
                                                       name="notification_sound" 
                                                       <?php echo $settings['notification_sound'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="notification_sound">
                                                    صوت الإشعارات
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <h6 class="text-primary">إعدادات العرض</h6>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="auto_hide_notifications" 
                                                       name="auto_hide_notifications" 
                                                       <?php echo $settings['auto_hide_notifications'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="auto_hide_notifications">
                                                    إخفاء تلقائي
                                                </label>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="notification_hide_delay" class="form-label">مدة الإخفاء (ثواني)</label>
                                                <input type="number" class="form-control" id="notification_hide_delay" 
                                                       name="notification_hide_delay" min="3" max="60"
                                                       value="<?php echo $settings['notification_hide_delay']; ?>">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="max_notifications_display" class="form-label">عدد الإشعارات المعروضة</label>
                                                <input type="number" class="form-control" id="max_notifications_display" 
                                                       name="max_notifications_display" min="1" max="20"
                                                       value="<?php echo $settings['max_notifications_display']; ?>">
                                            </div>
                                            
                                            <h6 class="text-primary mt-4">أنواع الإشعارات</h6>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="offer_notifications" 
                                                       name="offer_notifications" 
                                                       <?php echo $settings['offer_notifications'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="offer_notifications">
                                                    إشعارات العروض الجديدة
                                                </label>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="system_notifications" 
                                                       name="system_notifications" 
                                                       <?php echo $settings['system_notifications'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="system_notifications">
                                                    إشعارات النظام
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end mt-4">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معاينة الإشعارات -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">معاينة أنواع الإشعارات</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>إشعار معلوماتي</h6>
                                    <p class="mb-0">هذا مثال على إشعار معلوماتي عادي</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle me-2"></i>إشعار نجاح</h6>
                                    <p class="mb-0">هذا مثال على إشعار نجاح العملية</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>إشعار تحذير</h6>
                                    <p class="mb-0">هذا مثال على إشعار تحذيري</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="alert alert-primary">
                                    <h6><i class="fas fa-gift me-2"></i>إشعار عرض جديد</h6>
                                    <p class="mb-0">عرض جديد: اربح $50 من التطبيق الجديد!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
</body>
</html>
