<?php
require_once '../config/config.php';

// التحقق من المعاملات (متوافق مع CPALead)
$click_id = sanitize($_GET['click_id'] ?? $_POST['click_id'] ?? '');
$transaction_id = sanitize($_GET['transaction_id'] ?? $_POST['transaction_id'] ?? '');
$lead_id = sanitize($_GET['lead_id'] ?? $_POST['lead_id'] ?? '');
$campaign_id = sanitize($_GET['campaign_id'] ?? $_POST['campaign_id'] ?? '');
$campaign_name = sanitize($_GET['campaign_name'] ?? $_POST['campaign_name'] ?? '');
$gateway_id = sanitize($_GET['gateway_id'] ?? $_POST['gateway_id'] ?? '');
$payout = floatval($_GET['payout'] ?? $_POST['payout'] ?? 0);
$virtual_currency = floatval($_GET['virtual_currency'] ?? $_POST['virtual_currency'] ?? 0);
$status = sanitize($_GET['status'] ?? $_POST['status'] ?? 'pending');
$country_iso = sanitize($_GET['country_iso'] ?? $_POST['country_iso'] ?? '');
$postback_password = sanitize($_GET['password'] ?? $_POST['password'] ?? '');

// Sub IDs (متوافق مع CPALead)
$subid = sanitize($_GET['subid'] ?? $_POST['subid'] ?? '');
$subid2 = sanitize($_GET['subid2'] ?? $_POST['subid2'] ?? '');
$subid3 = sanitize($_GET['subid3'] ?? $_POST['subid3'] ?? '');

// معرفات الأجهزة المحمولة
$idfa = sanitize($_GET['idfa'] ?? $_POST['idfa'] ?? '');
$gaid = sanitize($_GET['gaid'] ?? $_POST['gaid'] ?? '');

// التحقق من وجود click_id أو subid (للتوافق مع CPALead)
if (empty($click_id) && empty($subid)) {
    http_response_code(400);
    die('معرف النقرة أو SubID مطلوب');
}

// التحقق من كلمة مرور Postback إذا كانت مطلوبة
$required_password = 'your_postback_password_here'; // يجب تغييرها
if (!empty($required_password) && $postback_password !== $required_password) {
    http_response_code(401);
    die('كلمة مرور Postback غير صحيحة');
}

// التحقق من IP المصدر (CPALead IP: ************)
$allowed_ips = ['************', '127.0.0.1', '::1']; // إضافة IPs أخرى حسب الحاجة
$client_ip = $_SERVER['REMOTE_ADDR'];
if (!in_array($client_ip, $allowed_ips)) {
    logError("Postback من IP غير مصرح: $client_ip");
    // يمكن تعطيل هذا التحقق في البداية للاختبار
    // http_response_code(403);
    // die('IP غير مصرح');
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // جلب معلومات النقرة (محدث للتوافق مع CPALead)
    if (!empty($click_id)) {
        // البحث بـ click_id
        $click_query = "SELECT c.*, tl.user_id, tl.offer_id, o.payout as offer_payout, o.title as offer_title,
                        u.username, u.email
                        FROM clicks c
                        JOIN tracking_links tl ON c.tracking_id = tl.tracking_id
                        JOIN offers o ON tl.offer_id = o.id
                        JOIN users u ON tl.user_id = u.id
                        WHERE c.id = :click_id";

        $click_stmt = $db->prepare($click_query);
        $click_stmt->bindParam(':click_id', $click_id);
        $click_stmt->execute();

        $click_data = $click_stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        // البحث بـ subid (للتوافق مع CPALead)
        $click_query = "SELECT c.*, tl.user_id, tl.offer_id, o.payout as offer_payout, o.title as offer_title,
                        u.username, u.email
                        FROM clicks c
                        JOIN tracking_links tl ON c.tracking_id = tl.tracking_id
                        JOIN offers o ON tl.offer_id = o.id
                        JOIN users u ON tl.user_id = u.id
                        WHERE c.sub_id = :subid
                        ORDER BY c.clicked_at DESC
                        LIMIT 1";

        $click_stmt = $db->prepare($click_query);
        $click_stmt->bindParam(':subid', $subid);
        $click_stmt->execute();

        $click_data = $click_stmt->fetch(PDO::FETCH_ASSOC);

        if ($click_data) {
            $click_id = $click_data['id']; // تعيين click_id للاستخدام لاحقاً
        }
    }

    if (!$click_data) {
        http_response_code(404);
        die('النقرة غير موجودة');
    }

    // التحقق من عدم وجود تحويل مسبق لنفس النقرة
    $existing_conversion_query = "SELECT id FROM conversions WHERE click_id = :click_id";
    $existing_stmt = $db->prepare($existing_conversion_query);
    $existing_stmt->bindParam(':click_id', $click_id);
    $existing_stmt->execute();

    if ($existing_stmt->rowCount() > 0) {
        // تحديث التحويل الموجود (محدث لدعم CPALead)
        $update_query = "UPDATE conversions
                        SET transaction_id = :transaction_id,
                            lead_id = :lead_id,
                            campaign_id = :campaign_id,
                            campaign_name = :campaign_name,
                            gateway_id = :gateway_id,
                            payout = :payout,
                            virtual_currency = :virtual_currency,
                            status = :status,
                            country_iso = :country_iso,
                            sub_id2 = :sub_id2,
                            sub_id3 = :sub_id3,
                            idfa = :idfa,
                            gaid = :gaid,
                            postback_password = :postback_password,
                            conversion_data = :conversion_data
                        WHERE click_id = :click_id";

        $conversion_data = json_encode([
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'timestamp' => date('Y-m-d H:i:s'),
            'postback_data' => $_REQUEST,
            'cpalead_data' => [
                'lead_id' => $lead_id,
                'campaign_id' => $campaign_id,
                'campaign_name' => $campaign_name,
                'gateway_id' => $gateway_id,
                'virtual_currency' => $virtual_currency
            ]
        ]);

        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':click_id', $click_id);
        $update_stmt->bindParam(':transaction_id', $transaction_id);
        $update_stmt->bindParam(':payout', $payout);
        $update_stmt->bindParam(':status', $status);
        $update_stmt->bindParam(':conversion_data', $conversion_data);
        $update_stmt->execute();

        echo "OK - تم تحديث التحويل";
        exit();
    }

    // استخدام عمولة العرض إذا لم يتم تمرير عمولة
    if ($payout <= 0) {
        $payout = $click_data['offer_payout'];
    }

    // حساب الإيرادات (نسبة الموقع من العمولة)
    $commission_rate = 80; // 80% للناشر، 20% للموقع
    $publisher_earnings = $payout * ($commission_rate / 100);
    $site_revenue = $payout - $publisher_earnings;

    // تسجيل التحويل
    $conversion_query = "INSERT INTO conversions (
        tracking_id, user_id, offer_id, click_id, transaction_id, payout, revenue,
        status, ip_address, country, sub_id, source, campaign, conversion_data
    ) VALUES (
        :tracking_id, :user_id, :offer_id, :click_id, :transaction_id, :payout, :revenue,
        :status, :ip_address, :country, :sub_id, :source, :campaign, :conversion_data
    )";

    $conversion_data = json_encode([
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'timestamp' => date('Y-m-d H:i:s'),
        'postback_data' => $_REQUEST,
        'publisher_earnings' => $publisher_earnings,
        'site_revenue' => $site_revenue,
        'commission_rate' => $commission_rate
    ]);

    $conversion_stmt = $db->prepare($conversion_query);
    $conversion_stmt->bindParam(':tracking_id', $click_data['tracking_id']);
    $conversion_stmt->bindParam(':user_id', $click_data['user_id']);
    $conversion_stmt->bindParam(':offer_id', $click_data['offer_id']);
    $conversion_stmt->bindParam(':click_id', $click_id);
    $conversion_stmt->bindParam(':transaction_id', $transaction_id);
    $conversion_stmt->bindParam(':payout', $publisher_earnings); // عمولة الناشر
    $conversion_stmt->bindParam(':revenue', $site_revenue); // إيرادات الموقع
    $conversion_stmt->bindParam(':status', $status);
    $conversion_stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
    $conversion_stmt->bindParam(':country', $click_data['country']);
    $conversion_stmt->bindParam(':sub_id', $click_data['sub_id']);
    $conversion_stmt->bindParam(':source', $click_data['source']);
    $conversion_stmt->bindParam(':campaign', $click_data['campaign']);
    $conversion_stmt->bindParam(':conversion_data', $conversion_data);
    
    $conversion_stmt->execute();
    $conversion_id = $db->lastInsertId();

    // تحديث رصيد المستخدم إذا كان التحويل معتمد
    if ($status === 'approved') {
        $balance_query = "UPDATE users 
                         SET balance = balance + :earnings, 
                             total_earnings = total_earnings + :earnings 
                         WHERE id = :user_id";
        
        $balance_stmt = $db->prepare($balance_query);
        $balance_stmt->bindParam(':earnings', $publisher_earnings);
        $balance_stmt->bindParam(':user_id', $click_data['user_id']);
        $balance_stmt->execute();
    }

    // تحديث الإحصائيات اليومية
    updateDailyStats($db, $click_data['user_id'], $click_data['offer_id'], 'conversion', $publisher_earnings);

    // تسجيل النشاط
    $activity_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                      VALUES (:user_id, 'conversion', :description, :data)";
    
    $activity_description = "تحويل جديد للعرض: " . $click_data['offer_title'];
    $activity_data = json_encode([
        'conversion_id' => $conversion_id,
        'offer_id' => $click_data['offer_id'],
        'payout' => $publisher_earnings,
        'status' => $status,
        'transaction_id' => $transaction_id
    ]);

    $activity_stmt = $db->prepare($activity_query);
    $activity_stmt->bindParam(':user_id', $click_data['user_id']);
    $activity_stmt->bindParam(':description', $activity_description);
    $activity_stmt->bindParam(':data', $activity_data);
    $activity_stmt->execute();

    // إرسال إشعار بالبريد الإلكتروني (اختياري)
    if (EMAIL_NOTIFICATIONS && $status === 'approved') {
        sendConversionNotification($click_data, $publisher_earnings, $transaction_id);
    }

    echo "OK - تم تسجيل التحويل بنجاح";

} catch (PDOException $e) {
    logError("خطأ في postback: " . $e->getMessage());
    http_response_code(500);
    die('خطأ في النظام');
}

// دالة لتحديث الإحصائيات اليومية
function updateDailyStats($db, $user_id, $offer_id, $type, $earnings = 0) {
    $today = date('Y-m-d');
    
    // التحقق من وجود سجل لليوم
    $check_query = "SELECT id FROM daily_stats 
                    WHERE user_id = :user_id AND offer_id = :offer_id AND date = :date";
    
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':user_id', $user_id);
    $check_stmt->bindParam(':offer_id', $offer_id);
    $check_stmt->bindParam(':date', $today);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        // تحديث السجل الموجود
        $update_query = "UPDATE daily_stats 
                        SET conversions = conversions + 1, earnings = earnings + :earnings
                        WHERE user_id = :user_id AND offer_id = :offer_id AND date = :date";
        
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':earnings', $earnings);
        $update_stmt->bindParam(':user_id', $user_id);
        $update_stmt->bindParam(':offer_id', $offer_id);
        $update_stmt->bindParam(':date', $today);
        $update_stmt->execute();
    } else {
        // إنشاء سجل جديد
        $insert_query = "INSERT INTO daily_stats (user_id, offer_id, date, conversions, earnings) 
                        VALUES (:user_id, :offer_id, :date, 1, :earnings)";
        
        $insert_stmt = $db->prepare($insert_query);
        $insert_stmt->bindParam(':user_id', $user_id);
        $insert_stmt->bindParam(':offer_id', $offer_id);
        $insert_stmt->bindParam(':date', $today);
        $insert_stmt->bindParam(':earnings', $earnings);
        $insert_stmt->execute();
    }
}

// دالة لإرسال إشعار التحويل
function sendConversionNotification($click_data, $earnings, $transaction_id) {
    $to = $click_data['email'];
    $subject = "تحويل جديد - " . SITE_NAME;
    
    $message = "
    <html>
    <head>
        <title>تحويل جديد</title>
    </head>
    <body>
        <h2>مبروك! لديك تحويل جديد</h2>
        <p>عزيزي {$click_data['username']},</p>
        <p>تم تسجيل تحويل جديد لحسابك:</p>
        <ul>
            <li><strong>العرض:</strong> {$click_data['offer_title']}</li>
            <li><strong>العمولة:</strong> " . CURRENCY_SYMBOL . number_format($earnings, 2) . "</li>
            <li><strong>رقم المعاملة:</strong> {$transaction_id}</li>
            <li><strong>التاريخ:</strong> " . date('Y-m-d H:i:s') . "</li>
        </ul>
        <p>يمكنك مراجعة تفاصيل أكثر من خلال لوحة التحكم.</p>
        <p>شكراً لك،<br>فريق " . SITE_NAME . "</p>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: " . ADMIN_EMAIL . "\r\n";
    
    @mail($to, $subject, $message, $headers);
}
?>
