<?php
require_once '../config/config.php';

// التحقق من المعاملات المطلوبة
$click_id = sanitize($_GET['click_id'] ?? $_POST['click_id'] ?? '');
$transaction_id = sanitize($_GET['transaction_id'] ?? $_POST['transaction_id'] ?? '');
$payout = floatval($_GET['payout'] ?? $_POST['payout'] ?? 0);
$status = sanitize($_GET['status'] ?? $_POST['status'] ?? 'pending');

// التحقق من وجود click_id
if (empty($click_id)) {
    http_response_code(400);
    die('معرف النقرة مطلوب');
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // جلب معلومات النقرة
    $click_query = "SELECT c.*, tl.user_id, tl.offer_id, o.payout as offer_payout, o.title as offer_title,
                    u.username, u.email
                    FROM clicks c
                    JOIN tracking_links tl ON c.tracking_id = tl.tracking_id
                    JOIN offers o ON tl.offer_id = o.id
                    JOIN users u ON tl.user_id = u.id
                    WHERE c.id = :click_id";
    
    $click_stmt = $db->prepare($click_query);
    $click_stmt->bindParam(':click_id', $click_id);
    $click_stmt->execute();
    
    $click_data = $click_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$click_data) {
        http_response_code(404);
        die('النقرة غير موجودة');
    }

    // التحقق من عدم وجود تحويل مسبق لنفس النقرة
    $existing_conversion_query = "SELECT id FROM conversions WHERE click_id = :click_id";
    $existing_stmt = $db->prepare($existing_conversion_query);
    $existing_stmt->bindParam(':click_id', $click_id);
    $existing_stmt->execute();

    if ($existing_stmt->rowCount() > 0) {
        // تحديث التحويل الموجود
        $update_query = "UPDATE conversions 
                        SET transaction_id = :transaction_id, 
                            payout = :payout, 
                            status = :status,
                            conversion_data = :conversion_data
                        WHERE click_id = :click_id";
        
        $conversion_data = json_encode([
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'timestamp' => date('Y-m-d H:i:s'),
            'postback_data' => $_REQUEST
        ]);

        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':click_id', $click_id);
        $update_stmt->bindParam(':transaction_id', $transaction_id);
        $update_stmt->bindParam(':payout', $payout);
        $update_stmt->bindParam(':status', $status);
        $update_stmt->bindParam(':conversion_data', $conversion_data);
        $update_stmt->execute();

        echo "OK - تم تحديث التحويل";
        exit();
    }

    // استخدام عمولة العرض إذا لم يتم تمرير عمولة
    if ($payout <= 0) {
        $payout = $click_data['offer_payout'];
    }

    // حساب الإيرادات (نسبة الموقع من العمولة)
    $commission_rate = 80; // 80% للناشر، 20% للموقع
    $publisher_earnings = $payout * ($commission_rate / 100);
    $site_revenue = $payout - $publisher_earnings;

    // تسجيل التحويل
    $conversion_query = "INSERT INTO conversions (
        tracking_id, user_id, offer_id, click_id, transaction_id, payout, revenue,
        status, ip_address, country, sub_id, source, campaign, conversion_data
    ) VALUES (
        :tracking_id, :user_id, :offer_id, :click_id, :transaction_id, :payout, :revenue,
        :status, :ip_address, :country, :sub_id, :source, :campaign, :conversion_data
    )";

    $conversion_data = json_encode([
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'timestamp' => date('Y-m-d H:i:s'),
        'postback_data' => $_REQUEST,
        'publisher_earnings' => $publisher_earnings,
        'site_revenue' => $site_revenue,
        'commission_rate' => $commission_rate
    ]);

    $conversion_stmt = $db->prepare($conversion_query);
    $conversion_stmt->bindParam(':tracking_id', $click_data['tracking_id']);
    $conversion_stmt->bindParam(':user_id', $click_data['user_id']);
    $conversion_stmt->bindParam(':offer_id', $click_data['offer_id']);
    $conversion_stmt->bindParam(':click_id', $click_id);
    $conversion_stmt->bindParam(':transaction_id', $transaction_id);
    $conversion_stmt->bindParam(':payout', $publisher_earnings); // عمولة الناشر
    $conversion_stmt->bindParam(':revenue', $site_revenue); // إيرادات الموقع
    $conversion_stmt->bindParam(':status', $status);
    $conversion_stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
    $conversion_stmt->bindParam(':country', $click_data['country']);
    $conversion_stmt->bindParam(':sub_id', $click_data['sub_id']);
    $conversion_stmt->bindParam(':source', $click_data['source']);
    $conversion_stmt->bindParam(':campaign', $click_data['campaign']);
    $conversion_stmt->bindParam(':conversion_data', $conversion_data);
    
    $conversion_stmt->execute();
    $conversion_id = $db->lastInsertId();

    // تحديث رصيد المستخدم إذا كان التحويل معتمد
    if ($status === 'approved') {
        $balance_query = "UPDATE users 
                         SET balance = balance + :earnings, 
                             total_earnings = total_earnings + :earnings 
                         WHERE id = :user_id";
        
        $balance_stmt = $db->prepare($balance_query);
        $balance_stmt->bindParam(':earnings', $publisher_earnings);
        $balance_stmt->bindParam(':user_id', $click_data['user_id']);
        $balance_stmt->execute();
    }

    // تحديث الإحصائيات اليومية
    updateDailyStats($db, $click_data['user_id'], $click_data['offer_id'], 'conversion', $publisher_earnings);

    // تسجيل النشاط
    $activity_query = "INSERT INTO activity_logs (user_id, action, description, data) 
                      VALUES (:user_id, 'conversion', :description, :data)";
    
    $activity_description = "تحويل جديد للعرض: " . $click_data['offer_title'];
    $activity_data = json_encode([
        'conversion_id' => $conversion_id,
        'offer_id' => $click_data['offer_id'],
        'payout' => $publisher_earnings,
        'status' => $status,
        'transaction_id' => $transaction_id
    ]);

    $activity_stmt = $db->prepare($activity_query);
    $activity_stmt->bindParam(':user_id', $click_data['user_id']);
    $activity_stmt->bindParam(':description', $activity_description);
    $activity_stmt->bindParam(':data', $activity_data);
    $activity_stmt->execute();

    // إرسال إشعار بالبريد الإلكتروني (اختياري)
    if (EMAIL_NOTIFICATIONS && $status === 'approved') {
        sendConversionNotification($click_data, $publisher_earnings, $transaction_id);
    }

    echo "OK - تم تسجيل التحويل بنجاح";

} catch (PDOException $e) {
    logError("خطأ في postback: " . $e->getMessage());
    http_response_code(500);
    die('خطأ في النظام');
}

// دالة لتحديث الإحصائيات اليومية
function updateDailyStats($db, $user_id, $offer_id, $type, $earnings = 0) {
    $today = date('Y-m-d');
    
    // التحقق من وجود سجل لليوم
    $check_query = "SELECT id FROM daily_stats 
                    WHERE user_id = :user_id AND offer_id = :offer_id AND date = :date";
    
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':user_id', $user_id);
    $check_stmt->bindParam(':offer_id', $offer_id);
    $check_stmt->bindParam(':date', $today);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        // تحديث السجل الموجود
        $update_query = "UPDATE daily_stats 
                        SET conversions = conversions + 1, earnings = earnings + :earnings
                        WHERE user_id = :user_id AND offer_id = :offer_id AND date = :date";
        
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':earnings', $earnings);
        $update_stmt->bindParam(':user_id', $user_id);
        $update_stmt->bindParam(':offer_id', $offer_id);
        $update_stmt->bindParam(':date', $today);
        $update_stmt->execute();
    } else {
        // إنشاء سجل جديد
        $insert_query = "INSERT INTO daily_stats (user_id, offer_id, date, conversions, earnings) 
                        VALUES (:user_id, :offer_id, :date, 1, :earnings)";
        
        $insert_stmt = $db->prepare($insert_query);
        $insert_stmt->bindParam(':user_id', $user_id);
        $insert_stmt->bindParam(':offer_id', $offer_id);
        $insert_stmt->bindParam(':date', $today);
        $insert_stmt->bindParam(':earnings', $earnings);
        $insert_stmt->execute();
    }
}

// دالة لإرسال إشعار التحويل
function sendConversionNotification($click_data, $earnings, $transaction_id) {
    $to = $click_data['email'];
    $subject = "تحويل جديد - " . SITE_NAME;
    
    $message = "
    <html>
    <head>
        <title>تحويل جديد</title>
    </head>
    <body>
        <h2>مبروك! لديك تحويل جديد</h2>
        <p>عزيزي {$click_data['username']},</p>
        <p>تم تسجيل تحويل جديد لحسابك:</p>
        <ul>
            <li><strong>العرض:</strong> {$click_data['offer_title']}</li>
            <li><strong>العمولة:</strong> " . CURRENCY_SYMBOL . number_format($earnings, 2) . "</li>
            <li><strong>رقم المعاملة:</strong> {$transaction_id}</li>
            <li><strong>التاريخ:</strong> " . date('Y-m-d H:i:s') . "</li>
        </ul>
        <p>يمكنك مراجعة تفاصيل أكثر من خلال لوحة التحكم.</p>
        <p>شكراً لك،<br>فريق " . SITE_NAME . "</p>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: " . ADMIN_EMAIL . "\r\n";
    
    @mail($to, $subject, $message, $headers);
}
?>
