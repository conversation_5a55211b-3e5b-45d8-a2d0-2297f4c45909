<?php
/**
 * اختبار اتصال قاعدة البيانات المستقل
 * استخدم هذا الملف لاختبار بيانات قاعدة البيانات قبل التثبيت
 */

// بيانات قاعدة البيانات - هذه هي البيانات الصحيحة لحسابك
$db_host = 'sql303.infinityfree.com';
$db_username = 'if0_39395085';
$db_password = 'Qweeee12';
$db_name = 'if0_39395085_q12';

$test_results = [];

// اختبار 1: الاتصال بالخادم
try {
    $pdo = new PDO("mysql:host=$db_host", $db_username, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $test_results['server_connection'] = ['status' => 'success', 'message' => 'تم الاتصال بالخادم بنجاح'];
} catch (PDOException $e) {
    $test_results['server_connection'] = ['status' => 'error', 'message' => 'فشل الاتصال بالخادم: ' . $e->getMessage()];
}

// اختبار 2: الاتصال بقاعدة البيانات
if ($test_results['server_connection']['status'] === 'success') {
    try {
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_username, $db_password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $test_results['database_connection'] = ['status' => 'success', 'message' => 'تم الاتصال بقاعدة البيانات بنجاح'];
    } catch (PDOException $e) {
        $test_results['database_connection'] = ['status' => 'error', 'message' => 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage()];
    }
} else {
    $test_results['database_connection'] = ['status' => 'skipped', 'message' => 'تم تخطي الاختبار بسبب فشل الاتصال بالخادم'];
}

// اختبار 3: صلاحيات قاعدة البيانات
if ($test_results['database_connection']['status'] === 'success') {
    try {
        // اختبار القراءة
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        
        // اختبار إنشاء جدول
        $pdo->exec("CREATE TABLE IF NOT EXISTS test_table (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50))");
        
        // اختبار الكتابة
        $pdo->exec("INSERT INTO test_table (name) VALUES ('test')");
        
        // اختبار الحذف
        $pdo->exec("DELETE FROM test_table WHERE name = 'test'");
        
        // حذف الجدول التجريبي
        $pdo->exec("DROP TABLE test_table");
        
        $test_results['permissions'] = ['status' => 'success', 'message' => 'جميع الصلاحيات متوفرة (قراءة، كتابة، إنشاء، حذف)'];
    } catch (PDOException $e) {
        $test_results['permissions'] = ['status' => 'error', 'message' => 'مشكلة في الصلاحيات: ' . $e->getMessage()];
    }
} else {
    $test_results['permissions'] = ['status' => 'skipped', 'message' => 'تم تخطي الاختبار بسبب فشل الاتصال بقاعدة البيانات'];
}

// اختبار 4: معلومات قاعدة البيانات
if ($test_results['database_connection']['status'] === 'success') {
    try {
        $version = $pdo->query("SELECT VERSION() as version")->fetch()['version'];
        $charset = $pdo->query("SELECT @@character_set_database as charset")->fetch()['charset'];
        $collation = $pdo->query("SELECT @@collation_database as collation")->fetch()['collation'];
        
        $test_results['database_info'] = [
            'status' => 'success', 
            'message' => "إصدار MySQL: $version, ترميز: $charset, ترتيب: $collation"
        ];
    } catch (PDOException $e) {
        $test_results['database_info'] = ['status' => 'error', 'message' => 'لا يمكن الحصول على معلومات قاعدة البيانات'];
    }
} else {
    $test_results['database_info'] = ['status' => 'skipped', 'message' => 'تم تخطي الاختبار'];
}

$overall_status = ($test_results['server_connection']['status'] === 'success' && 
                  $test_results['database_connection']['status'] === 'success' && 
                  $test_results['permissions']['status'] === 'success');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header <?php echo $overall_status ? 'bg-success' : 'bg-danger'; ?> text-white">
                        <h4>
                            <i class="fas fa-<?php echo $overall_status ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            اختبار قاعدة البيانات
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-<?php echo $overall_status ? 'success' : 'danger'; ?>">
                            <h6>
                                <i class="fas fa-<?php echo $overall_status ? 'check' : 'times'; ?> me-2"></i>
                                النتيجة الإجمالية: <?php echo $overall_status ? 'نجح الاختبار' : 'فشل الاختبار'; ?>
                            </h6>
                        </div>

                        <h5>بيانات الاتصال المستخدمة:</h5>
                        <div class="bg-light p-3 rounded mb-4">
                            <code>Host: <?php echo htmlspecialchars($db_host); ?></code><br>
                            <code>Username: <?php echo htmlspecialchars($db_username); ?></code><br>
                            <code>Database: <?php echo htmlspecialchars($db_name); ?></code><br>
                            <code>Password: <?php echo str_repeat('*', strlen($db_password)); ?></code>
                        </div>

                        <h5>نتائج الاختبارات:</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الاختبار</th>
                                        <th>النتيجة</th>
                                        <th>التفاصيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($test_results as $test_name => $result): ?>
                                        <tr>
                                            <td>
                                                <?php
                                                $test_names = [
                                                    'server_connection' => 'الاتصال بالخادم',
                                                    'database_connection' => 'الاتصال بقاعدة البيانات',
                                                    'permissions' => 'صلاحيات قاعدة البيانات',
                                                    'database_info' => 'معلومات قاعدة البيانات'
                                                ];
                                                echo $test_names[$test_name] ?? $test_name;
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $badge_class = [
                                                    'success' => 'bg-success',
                                                    'error' => 'bg-danger',
                                                    'skipped' => 'bg-secondary'
                                                ];
                                                $icon = [
                                                    'success' => 'check',
                                                    'error' => 'times',
                                                    'skipped' => 'minus'
                                                ];
                                                ?>
                                                <span class="badge <?php echo $badge_class[$result['status']]; ?>">
                                                    <i class="fas fa-<?php echo $icon[$result['status']]; ?> me-1"></i>
                                                    <?php echo ucfirst($result['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($result['message']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <?php if (!$overall_status): ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>نصائح لحل المشاكل:</h6>
                                <ul class="mb-0">
                                    <li>تأكد من إنشاء قاعدة البيانات من cPanel أولاً</li>
                                    <li>تحقق من صحة اسم المستخدم وكلمة المرور</li>
                                    <li>تأكد من أن اسم قاعدة البيانات صحيح ويتضمن البادئة</li>
                                    <li>تحقق من أن المستخدم مربوط بقاعدة البيانات مع صلاحيات كاملة</li>
                                    <li>جرب الانتظار قليلاً إذا كان الخطأ "Too many connections"</li>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <div class="text-center">
                            <?php if ($overall_status): ?>
                                <a href="installer.php?step=2" class="btn btn-success btn-lg">
                                    <i class="fas fa-rocket me-2"></i>متابعة التثبيت
                                </a>
                            <?php else: ?>
                                <button class="btn btn-warning" onclick="location.reload()">
                                    <i class="fas fa-redo me-2"></i>إعادة الاختبار
                                </button>
                            <?php endif; ?>
                            
                            <a href="database-helper.php" class="btn btn-info ms-2">
                                <i class="fas fa-question-circle me-2"></i>مساعدة الإعداد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
