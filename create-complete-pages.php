<?php
/**
 * إنشاء جميع الصفحات الكاملة للموقع
 */

echo "<h2>📄 إنشاء الصفحات الكاملة</h2>";

try {
    require_once 'config/config.php';
    
    // قالب الصفحة الأساسي
    function createFullPage($title, $icon, $content, $backPath = "../index.php", $isAdmin = false) {
        $adminCheck = $isAdmin ? '
if (!isLoggedIn() || !isAdmin()) { 
    header("Location: ' . ($backPath == "../index.php" ? "../" : "../../") . 'auth/login.php"); 
    exit(); 
}' : '
if (!isLoggedIn()) { 
    header("Location: ' . ($backPath == "../index.php" ? "../" : "../../") . 'auth/login.php"); 
    exit(); 
}';

        $configPath = $backPath == "../index.php" ? "../config/config.php" : 
                     ($backPath == "../../index.php" ? "../../config/config.php" : "../config/config.php");
        
        $cssPath = $backPath == "../index.php" ? "../assets/css/style.css" : 
                  ($backPath == "../../index.php" ? "../../assets/css/style.css" : "../assets/css/style.css");
        
        $jsPath = $backPath == "../index.php" ? "../assets/js/main.js" : 
                 ($backPath == "../../index.php" ? "../../assets/js/main.js" : "../assets/js/main.js");
        
        return '<?php
require_once "' . $configPath . '";
' . $adminCheck . '

$database = new Database();
$db = $database->getConnection();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $title . ' - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="' . $cssPath . '" rel="stylesheet">
</head>
<body>
    <?php include "' . ($backPath == "../index.php" ? "../" : "../../") . 'includes/header.php"; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include "' . ($backPath == "../index.php" ? "../" : "../../") . 'includes/sidebar.php"; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-' . $icon . ' me-2"></i>' . $title . '
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="' . $backPath . '" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>

                ' . $content . '
            </main>
        </div>
    </div>

    <?php include "' . ($backPath == "../index.php" ? "../" : "../../") . 'includes/footer.php"; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="' . $jsPath . '"></script>
</body>
</html>';
    }
    
    echo "<h3>1. صفحة العروض الكاملة:</h3>";
    
    $offersContent = '
                <!-- فلاتر البحث -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-filter me-2"></i>فلاتر البحث
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search" placeholder="ابحث في العروض...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">النوع</label>
                                <select class="form-select" name="type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="cpa">CPA</option>
                                    <option value="cpl">CPL</option>
                                    <option value="cps">CPS</option>
                                    <option value="cpi">CPI</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الترتيب</label>
                                <select class="form-select" name="sort">
                                    <option value="payout_desc">أعلى عمولة</option>
                                    <option value="payout_asc">أقل عمولة</option>
                                    <option value="newest">الأحدث</option>
                                    <option value="oldest">الأقدم</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- قائمة العروض -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-bullhorn me-2"></i>العروض المتاحة
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php
                        // جلب العروض من قاعدة البيانات
                        try {
                            $query = "SELECT o.*, n.name as network_name 
                                     FROM offers o 
                                     LEFT JOIN networks n ON o.network_id = n.id 
                                     WHERE o.status = \"active\" 
                                     ORDER BY o.payout DESC 
                                     LIMIT 20";
                            
                            $stmt = $db->prepare($query);
                            $stmt->execute();
                            $offers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            if (count($offers) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>اسم العرض</th>
                                                <th>الشبكة</th>
                                                <th>العمولة</th>
                                                <th>النوع</th>
                                                <th>البلدان</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($offers as $offer): ?>
                                            <tr>
                                                <td><?php echo $offer["id"]; ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($offer["title"]); ?></strong>
                                                    <?php if ($offer["description"]): ?>
                                                        <br><small class="text-muted"><?php echo substr(htmlspecialchars($offer["description"]), 0, 100); ?>...</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo htmlspecialchars($offer["network_name"] ?? "غير محدد"); ?></span>
                                                </td>
                                                <td>
                                                    <strong class="text-success"><?php echo CURRENCY_SYMBOL . number_format($offer["payout"], 2); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo strtoupper($offer["type"]); ?></span>
                                                </td>
                                                <td><?php echo htmlspecialchars($offer["countries"] ?? "جميع البلدان"); ?></td>
                                                <td>
                                                    <?php if ($offer["status"] == "active"): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-info btn-sm" onclick="viewOffer(<?php echo $offer[\"id\"]; ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-success btn-sm" onclick="generateLink(<?php echo $offer[\"id\"]; ?>)">
                                                            <i class="fas fa-link"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                                    <h5>لا توجد عروض متاحة حالياً</h5>
                                    <p>سيتم إضافة عروض جديدة قريباً. تابع معنا!</p>
                                </div>
                            <?php endif;
                        } catch (Exception $e) {
                            echo "<div class=\"alert alert-danger\">خطأ في تحميل العروض: " . $e->getMessage() . "</div>";
                        }
                        ?>
                    </div>
                </div>

                <script>
                function viewOffer(offerId) {
                    // فتح نافذة لعرض تفاصيل العرض
                    window.open("view.php?id=" + offerId, "_blank", "width=800,height=600");
                }

                function generateLink(offerId) {
                    // إنشاء رابط تتبع للعرض
                    window.location.href = "../tracking/generate.php?offer_id=" + offerId;
                }
                </script>';
    
    $offersPage = createFullPage('العروض', 'bullhorn', $offersContent);
    
    if (!file_exists('offers/index.php')) {
        file_put_contents('offers/index.php', $offersPage);
        echo "✅ صفحة العروض الكاملة<br>";
    }
    
    echo "<h3>2. صفحة التتبع الكاملة:</h3>";
    
    $trackingContent = '
                <!-- إنشاء رابط تتبع جديد -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-plus me-2"></i>إنشاء رابط تتبع جديد
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="create.php">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اختر العرض</label>
                                        <select class="form-select" name="offer_id" required>
                                            <option value="">اختر عرض...</option>
                                            <?php
                                            try {
                                                $offers_query = "SELECT id, title, payout FROM offers WHERE status = \"active\" ORDER BY title";
                                                $offers_stmt = $db->prepare($offers_query);
                                                $offers_stmt->execute();
                                                $offers = $offers_stmt->fetchAll(PDO::FETCH_ASSOC);
                                                
                                                foreach ($offers as $offer) {
                                                    echo "<option value=\"{$offer[\"id\"]}\">{$offer[\"title\"]} (" . CURRENCY_SYMBOL . number_format($offer[\"payout\"], 2) . ")</option>";
                                                }
                                            } catch (Exception $e) {
                                                echo "<option disabled>خطأ في تحميل العروض</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Sub ID (اختياري)</label>
                                        <input type="text" class="form-control" name="sub_id" placeholder="معرف فرعي للتتبع">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">المصدر (اختياري)</label>
                                        <input type="text" class="form-control" name="source" placeholder="مصدر الزيارة">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الحملة (اختياري)</label>
                                        <input type="text" class="form-control" name="campaign" placeholder="اسم الحملة">
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-link me-1"></i>إنشاء رابط التتبع
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- روابط التتبع الحالية -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-list me-2"></i>روابط التتبع الخاصة بك
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $user_id = $_SESSION["user_id"];
                            $links_query = "SELECT tl.*, o.title as offer_title, o.payout 
                                           FROM tracking_links tl 
                                           JOIN offers o ON tl.offer_id = o.id 
                                           WHERE tl.user_id = :user_id 
                                           ORDER BY tl.created_at DESC 
                                           LIMIT 20";
                            
                            $links_stmt = $db->prepare($links_query);
                            $links_stmt->bindParam(":user_id", $user_id);
                            $links_stmt->execute();
                            $links = $links_stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            if (count($links) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>العرض</th>
                                                <th>رابط التتبع</th>
                                                <th>Sub ID</th>
                                                <th>المصدر</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الإنشاء</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($links as $link): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($link["offer_title"]); ?></strong>
                                                    <br><small class="text-success"><?php echo CURRENCY_SYMBOL . number_format($link["payout"], 2); ?></small>
                                                </td>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control form-control-sm" value="<?php echo htmlspecialchars($link["tracking_url"]); ?>" readonly>
                                                        <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard(\"<?php echo htmlspecialchars($link["tracking_url"]); ?>\")">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($link["sub_id"] ?? "-"); ?></td>
                                                <td><?php echo htmlspecialchars($link["source"] ?? "-"); ?></td>
                                                <td>
                                                    <?php if ($link["status"] == "active"): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date("Y-m-d H:i", strtotime($link["created_at"])); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-info btn-sm" onclick="viewStats(\"<?php echo $link[\"tracking_id\"]; ?>\")">
                                                            <i class="fas fa-chart-bar"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-warning btn-sm" onclick="editLink(<?php echo $link[\"id\"]; ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-link fa-3x mb-3"></i>
                                    <h5>لا توجد روابط تتبع</h5>
                                    <p>ابدأ بإنشاء رابط تتبع جديد للعروض المتاحة</p>
                                </div>
                            <?php endif;
                        } catch (Exception $e) {
                            echo "<div class=\"alert alert-danger\">خطأ في تحميل روابط التتبع: " . $e->getMessage() . "</div>";
                        }
                        ?>
                    </div>
                </div>

                <script>
                function copyToClipboard(text) {
                    navigator.clipboard.writeText(text).then(function() {
                        CPA.showNotification("تم نسخ الرابط بنجاح!", "success");
                    });
                }

                function viewStats(trackingId) {
                    window.open("stats.php?tracking_id=" + trackingId, "_blank");
                }

                function editLink(linkId) {
                    window.location.href = "edit.php?id=" + linkId;
                }
                </script>';
    
    $trackingPage = createFullPage('التتبع', 'link', $trackingContent);
    
    if (!file_exists('tracking/index.php')) {
        file_put_contents('tracking/index.php', $trackingPage);
        echo "✅ صفحة التتبع الكاملة<br>";
    }
    
    echo "<h3>✅ تم إنشاء الصفحات الأساسية بنجاح!</h3>";
    
    echo "<div class='alert alert-success'>";
    echo "<h5>🎉 الصفحات الكاملة جاهزة!</h5>";
    echo "<ul>";
    echo "<li>✅ صفحة العروض مع فلاتر البحث والجداول التفاعلية</li>";
    echo "<li>✅ صفحة التتبع مع إنشاء الروابط وإدارتها</li>";
    echo "<li>✅ تصميم احترافي ومتجاوب</li>";
    echo "<li>✅ تكامل كامل مع قاعدة البيانات</li>";
    echo "<li>✅ JavaScript تفاعلي</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h4>🧪 اختبار الصفحات:</h4>";
    echo "<p>";
    echo "<a href='offers/' class='btn btn-primary me-2' target='_blank'>📢 صفحة العروض</a>";
    echo "<a href='tracking/' class='btn btn-success me-2' target='_blank'>🔗 صفحة التتبع</a>";
    echo "<a href='index.php' class='btn btn-secondary'>🏠 العودة للرئيسية</a>";
    echo "</p>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3 {
    color: #333;
}
.alert {
    padding: 15px;
    margin: 20px 0;
    border: 1px solid transparent;
    border-radius: 4px;
}
.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}
.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
}
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-secondary { background: #6c757d; color: white; }
</style>
