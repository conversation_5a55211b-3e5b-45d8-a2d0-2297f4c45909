<?php
require_once '../config/config.php';

// التحقق من وجود معرف التتبع
$tracking_id = sanitize($_GET['t'] ?? '');
if (empty($tracking_id)) {
    http_response_code(404);
    die('رابط غير صحيح');
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // جلب معلومات رابط التتبع
    $tracking_query = "SELECT tl.*, o.tracking_url, o.title as offer_title, o.payout, o.status as offer_status,
                       u.id as user_id, u.username, u.status as user_status
                       FROM tracking_links tl
                       JOIN offers o ON tl.offer_id = o.id
                       JOIN users u ON tl.user_id = u.id
                       WHERE tl.tracking_id = :tracking_id AND tl.status = 'active'";
    
    $tracking_stmt = $db->prepare($tracking_query);
    $tracking_stmt->bindParam(':tracking_id', $tracking_id);
    $tracking_stmt->execute();
    
    $tracking_link = $tracking_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$tracking_link) {
        http_response_code(404);
        die('رابط غير موجود أو غير نشط');
    }

    // التحقق من حالة العرض والمستخدم
    if ($tracking_link['offer_status'] !== 'active') {
        http_response_code(404);
        die('العرض غير متاح حالياً');
    }

    if ($tracking_link['user_status'] !== 'active') {
        http_response_code(404);
        die('الحساب غير نشط');
    }

    // جمع معلومات النقرة
    $ip_address = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    
    // استخراج معلومات إضافية من URL
    $sub_id = sanitize($_GET['sub_id'] ?? $tracking_link['sub_id'] ?? '');
    $source = sanitize($_GET['source'] ?? $tracking_link['source'] ?? '');
    $campaign = sanitize($_GET['campaign'] ?? $tracking_link['campaign'] ?? '');

    // تحديد نوع الجهاز
    $device_type = 'desktop';
    if (preg_match('/Mobile|Android|iPhone|iPad/', $user_agent)) {
        $device_type = preg_match('/iPad/', $user_agent) ? 'tablet' : 'mobile';
    }

    // استخراج معلومات المتصفح ونظام التشغيل
    $browser = 'Unknown';
    $os = 'Unknown';
    
    // تحديد المتصفح
    if (preg_match('/Chrome/i', $user_agent)) $browser = 'Chrome';
    elseif (preg_match('/Firefox/i', $user_agent)) $browser = 'Firefox';
    elseif (preg_match('/Safari/i', $user_agent)) $browser = 'Safari';
    elseif (preg_match('/Edge/i', $user_agent)) $browser = 'Edge';
    elseif (preg_match('/Opera/i', $user_agent)) $browser = 'Opera';

    // تحديد نظام التشغيل
    if (preg_match('/Windows/i', $user_agent)) $os = 'Windows';
    elseif (preg_match('/Mac/i', $user_agent)) $os = 'macOS';
    elseif (preg_match('/Linux/i', $user_agent)) $os = 'Linux';
    elseif (preg_match('/Android/i', $user_agent)) $os = 'Android';
    elseif (preg_match('/iOS/i', $user_agent)) $os = 'iOS';

    // تحديد البلد (يمكن استخدام خدمة GeoIP)
    $country = getCountryFromIP($ip_address);
    $city = getCityFromIP($ip_address);

    // التحقق من النقرة الفريدة
    $unique_check_query = "SELECT id FROM clicks 
                          WHERE tracking_id = :tracking_id 
                          AND ip_address = :ip_address 
                          AND DATE(clicked_at) = CURDATE()";
    
    $unique_stmt = $db->prepare($unique_check_query);
    $unique_stmt->bindParam(':tracking_id', $tracking_id);
    $unique_stmt->bindParam(':ip_address', $ip_address);
    $unique_stmt->execute();
    
    $is_unique = $unique_stmt->rowCount() == 0;

    // تسجيل النقرة
    $click_query = "INSERT INTO clicks (
        tracking_id, user_id, offer_id, ip_address, user_agent, referer,
        country, city, device_type, browser, os, sub_id, source, campaign, is_unique
    ) VALUES (
        :tracking_id, :user_id, :offer_id, :ip_address, :user_agent, :referer,
        :country, :city, :device_type, :browser, :os, :sub_id, :source, :campaign, :is_unique
    )";

    $click_stmt = $db->prepare($click_query);
    $click_stmt->bindParam(':tracking_id', $tracking_id);
    $click_stmt->bindParam(':user_id', $tracking_link['user_id']);
    $click_stmt->bindParam(':offer_id', $tracking_link['offer_id']);
    $click_stmt->bindParam(':ip_address', $ip_address);
    $click_stmt->bindParam(':user_agent', $user_agent);
    $click_stmt->bindParam(':referer', $referer);
    $click_stmt->bindParam(':country', $country);
    $click_stmt->bindParam(':city', $city);
    $click_stmt->bindParam(':device_type', $device_type);
    $click_stmt->bindParam(':browser', $browser);
    $click_stmt->bindParam(':os', $os);
    $click_stmt->bindParam(':sub_id', $sub_id);
    $click_stmt->bindParam(':source', $source);
    $click_stmt->bindParam(':campaign', $campaign);
    $click_stmt->bindParam(':is_unique', $is_unique, PDO::PARAM_BOOL);
    
    $click_stmt->execute();
    $click_id = $db->lastInsertId();

    // تحديث الإحصائيات اليومية
    updateDailyStats($db, $tracking_link['user_id'], $tracking_link['offer_id'], 'click', $is_unique);

    // بناء رابط الوجهة مع معاملات التتبع
    $destination_url = $tracking_link['tracking_url'];
    
    // إضافة معاملات التتبع إلى الرابط
    $url_params = [
        'click_id' => $click_id,
        'tracking_id' => $tracking_id,
        'user_id' => $tracking_link['user_id'],
        'offer_id' => $tracking_link['offer_id']
    ];
    
    if (!empty($sub_id)) $url_params['sub_id'] = $sub_id;
    if (!empty($source)) $url_params['source'] = $source;
    if (!empty($campaign)) $url_params['campaign'] = $campaign;

    // إضافة المعاملات إلى الرابط
    $separator = strpos($destination_url, '?') !== false ? '&' : '?';
    $destination_url .= $separator . http_build_query($url_params);

    // إعادة التوجيه
    header("Location: $destination_url", true, 302);
    exit();

} catch (PDOException $e) {
    logError("خطأ في تتبع النقرة: " . $e->getMessage());
    http_response_code(500);
    die('حدث خطأ في النظام');
}

// دالة لتحديد البلد من IP
function getCountryFromIP($ip) {
    // يمكن استخدام خدمة مثل GeoIP أو API خارجي
    // هنا مثال بسيط
    if ($ip === '127.0.0.1' || $ip === '::1') {
        return 'Local';
    }
    
    // يمكن استخدام خدمة مجانية مثل ip-api.com
    try {
        $response = @file_get_contents("http://ip-api.com/json/$ip?fields=country");
        if ($response) {
            $data = json_decode($response, true);
            return $data['country'] ?? 'Unknown';
        }
    } catch (Exception $e) {
        // تجاهل الأخطاء
    }
    
    return 'Unknown';
}

// دالة لتحديد المدينة من IP
function getCityFromIP($ip) {
    if ($ip === '127.0.0.1' || $ip === '::1') {
        return 'Local';
    }
    
    try {
        $response = @file_get_contents("http://ip-api.com/json/$ip?fields=city");
        if ($response) {
            $data = json_decode($response, true);
            return $data['city'] ?? 'Unknown';
        }
    } catch (Exception $e) {
        // تجاهل الأخطاء
    }
    
    return 'Unknown';
}

// دالة لتحديث الإحصائيات اليومية
function updateDailyStats($db, $user_id, $offer_id, $type, $is_unique = true) {
    $today = date('Y-m-d');
    
    // التحقق من وجود سجل لليوم
    $check_query = "SELECT id FROM daily_stats 
                    WHERE user_id = :user_id AND offer_id = :offer_id AND date = :date";
    
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':user_id', $user_id);
    $check_stmt->bindParam(':offer_id', $offer_id);
    $check_stmt->bindParam(':date', $today);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() > 0) {
        // تحديث السجل الموجود
        if ($type === 'click') {
            $update_query = "UPDATE daily_stats 
                            SET clicks = clicks + 1" . ($is_unique ? ", unique_clicks = unique_clicks + 1" : "") . "
                            WHERE user_id = :user_id AND offer_id = :offer_id AND date = :date";
        } else {
            $update_query = "UPDATE daily_stats 
                            SET conversions = conversions + 1
                            WHERE user_id = :user_id AND offer_id = :offer_id AND date = :date";
        }
        
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':user_id', $user_id);
        $update_stmt->bindParam(':offer_id', $offer_id);
        $update_stmt->bindParam(':date', $today);
        $update_stmt->execute();
    } else {
        // إنشاء سجل جديد
        $clicks = $type === 'click' ? 1 : 0;
        $unique_clicks = ($type === 'click' && $is_unique) ? 1 : 0;
        $conversions = $type === 'conversion' ? 1 : 0;
        
        $insert_query = "INSERT INTO daily_stats (user_id, offer_id, date, clicks, unique_clicks, conversions) 
                        VALUES (:user_id, :offer_id, :date, :clicks, :unique_clicks, :conversions)";
        
        $insert_stmt = $db->prepare($insert_query);
        $insert_stmt->bindParam(':user_id', $user_id);
        $insert_stmt->bindParam(':offer_id', $offer_id);
        $insert_stmt->bindParam(':date', $today);
        $insert_stmt->bindParam(':clicks', $clicks);
        $insert_stmt->bindParam(':unique_clicks', $unique_clicks);
        $insert_stmt->bindParam(':conversions', $conversions);
        $insert_stmt->execute();
    }
}
?>
