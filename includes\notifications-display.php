<?php
/**
 * مكون عرض الإشعارات
 */

// التأكد من تسجيل الدخول
if (!isLoggedIn()) {
    return;
}

$database = new Database();
$notificationManager = new NotificationManager($database);

// التحقق من تفعيل الإشعارات
if (!$notificationManager->getSetting('notifications_enabled', true)) {
    return;
}

$user_id = $_SESSION['user_id'];
$max_display = $notificationManager->getSetting('max_notifications_display', 5);

// جلب الإشعارات للمستخدم
$notifications = $notificationManager->getUserNotifications($user_id, $max_display);
$unread_count = $notificationManager->getUnreadCount($user_id);

// إعدادات العرض
$show_popup = $notificationManager->getSetting('popup_notifications', true);
$show_banner = $notificationManager->getSetting('banner_notifications', true);
$show_sidebar = $notificationManager->getSetting('sidebar_notifications', true);
$auto_hide = $notificationManager->getSetting('auto_hide_notifications', true);
$hide_delay = $notificationManager->getSetting('notification_hide_delay', 10);
$notification_sound = $notificationManager->getSetting('notification_sound', true);

// فلترة الإشعارات حسب نوع العرض
$popup_notifications = array_filter($notifications, function($n) { return $n['show_popup'] && !$n['is_dismissed']; });
$banner_notifications = array_filter($notifications, function($n) { return $n['show_banner'] && !$n['is_dismissed']; });
$sidebar_notifications = array_filter($notifications, function($n) { return $n['show_sidebar']; });
?>

<!-- أيقونة الإشعارات في الهيدر -->
<div class="notification-icon-container">
    <div class="dropdown">
        <button class="btn btn-link position-relative" type="button" id="notificationDropdown" 
                data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-bell fa-lg text-light"></i>
            <?php if ($unread_count > 0): ?>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    <?php echo $unread_count > 99 ? '99+' : $unread_count; ?>
                </span>
            <?php endif; ?>
        </button>
        
        <div class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown">
            <div class="dropdown-header d-flex justify-content-between align-items-center">
                <span>الإشعارات</span>
                <?php if ($unread_count > 0): ?>
                    <button class="btn btn-sm btn-link p-0" onclick="markAllAsRead()">
                        تحديد الكل كمقروء
                    </button>
                <?php endif; ?>
            </div>
            
            <?php if (empty($sidebar_notifications)): ?>
                <div class="dropdown-item-text text-center text-muted py-3">
                    <i class="fas fa-bell-slash fa-2x mb-2"></i><br>
                    لا توجد إشعارات
                </div>
            <?php else: ?>
                <div class="notification-list">
                    <?php foreach (array_slice($sidebar_notifications, 0, $max_display) as $notification): ?>
                        <div class="dropdown-item notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?>" 
                             data-notification-id="<?php echo $notification['id']; ?>">
                            <div class="d-flex">
                                <div class="notification-icon me-2">
                                    <?php
                                    $icons = [
                                        'info' => 'fa-info-circle text-info',
                                        'success' => 'fa-check-circle text-success',
                                        'warning' => 'fa-exclamation-triangle text-warning',
                                        'danger' => 'fa-exclamation-circle text-danger',
                                        'offer' => 'fa-gift text-primary',
                                        'system' => 'fa-cog text-secondary'
                                    ];
                                    ?>
                                    <i class="fas <?php echo $icons[$notification['type']]; ?>"></i>
                                </div>
                                <div class="notification-content flex-grow-1">
                                    <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                    <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                    <div class="notification-time">
                                        <?php echo timeAgo($notification['created_at']); ?>
                                    </div>
                                </div>
                                <div class="notification-actions">
                                    <button class="btn btn-sm btn-link p-0" onclick="dismissNotification(<?php echo $notification['id']; ?>)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (count($sidebar_notifications) > $max_display): ?>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item-text text-center">
                        <a href="notifications/" class="btn btn-sm btn-primary">عرض جميع الإشعارات</a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- إشعارات البانر -->
<?php if ($show_banner && !empty($banner_notifications)): ?>
    <div id="bannerNotifications">
        <?php foreach (array_slice($banner_notifications, 0, 3) as $notification): ?>
            <div class="alert alert-<?php echo $notification['type'] === 'offer' ? 'primary' : $notification['type']; ?> alert-dismissible fade show banner-notification" 
                 data-notification-id="<?php echo $notification['id']; ?>"
                 <?php if ($auto_hide && $notification['auto_hide']): ?>
                     data-auto-hide="<?php echo $notification['hide_after_seconds']; ?>"
                 <?php endif; ?>>
                <div class="d-flex align-items-center">
                    <div class="me-2">
                        <?php
                        $icons = [
                            'info' => 'fa-info-circle',
                            'success' => 'fa-check-circle',
                            'warning' => 'fa-exclamation-triangle',
                            'danger' => 'fa-exclamation-circle',
                            'offer' => 'fa-gift',
                            'system' => 'fa-cog'
                        ];
                        ?>
                        <i class="fas <?php echo $icons[$notification['type']]; ?>"></i>
                    </div>
                    <div class="flex-grow-1">
                        <strong><?php echo htmlspecialchars($notification['title']); ?></strong>
                        <span class="ms-2"><?php echo htmlspecialchars($notification['message']); ?></span>
                        <?php if ($notification['offer_id']): ?>
                            <a href="offers/view.php?id=<?php echo $notification['offer_id']; ?>" class="btn btn-sm btn-light ms-2">
                                عرض التفاصيل
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <button type="button" class="btn-close" onclick="dismissNotification(<?php echo $notification['id']; ?>)"></button>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- إشعارات منبثقة -->
<?php if ($show_popup && !empty($popup_notifications)): ?>
    <div id="popupNotifications" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
        <?php foreach (array_slice($popup_notifications, 0, 2) as $notification): ?>
            <div class="toast popup-notification" data-notification-id="<?php echo $notification['id']; ?>" 
                 <?php if ($auto_hide && $notification['auto_hide']): ?>
                     data-bs-autohide="true" data-bs-delay="<?php echo $notification['hide_after_seconds'] * 1000; ?>"
                 <?php else: ?>
                     data-bs-autohide="false"
                 <?php endif; ?>>
                <div class="toast-header">
                    <i class="fas <?php echo $icons[$notification['type']]; ?> me-2"></i>
                    <strong class="me-auto"><?php echo htmlspecialchars($notification['title']); ?></strong>
                    <small><?php echo timeAgo($notification['created_at']); ?></small>
                    <button type="button" class="btn-close" onclick="dismissNotification(<?php echo $notification['id']; ?>)"></button>
                </div>
                <div class="toast-body">
                    <?php echo htmlspecialchars($notification['message']); ?>
                    <?php if ($notification['offer_id']): ?>
                        <div class="mt-2">
                            <a href="offers/view.php?id=<?php echo $notification['offer_id']; ?>" class="btn btn-sm btn-primary">
                                عرض العرض
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- أصوات الإشعارات -->
<?php if ($notification_sound && $unread_count > 0): ?>
    <audio id="notificationSound" preload="auto">
        <source src="assets/sounds/notification.mp3" type="audio/mpeg">
        <source src="assets/sounds/notification.ogg" type="audio/ogg">
    </audio>
<?php endif; ?>

<style>
.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.notification-title {
    font-weight: bold;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.notification-message {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 2px;
}

.notification-time {
    font-size: 0.7rem;
    color: #999;
}

.banner-notification {
    margin-bottom: 0;
    border-radius: 0;
}

.popup-notification {
    margin-bottom: 10px;
    min-width: 300px;
}

.notification-icon-container .badge {
    font-size: 0.6rem;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
}
</style>

<script>
// تفعيل الإشعارات المنبثقة
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل Toast notifications
    const toastElements = document.querySelectorAll('.toast');
    toastElements.forEach(function(toastElement) {
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
    });
    
    // إخفاء البانر تلقائياً
    const bannerNotifications = document.querySelectorAll('.banner-notification[data-auto-hide]');
    bannerNotifications.forEach(function(banner) {
        const hideAfter = parseInt(banner.getAttribute('data-auto-hide')) * 1000;
        setTimeout(function() {
            banner.style.display = 'none';
        }, hideAfter);
    });
    
    // تشغيل صوت الإشعار
    <?php if ($notification_sound && $unread_count > 0): ?>
        const notificationSound = document.getElementById('notificationSound');
        if (notificationSound) {
            notificationSound.play().catch(function(error) {
                console.log('Could not play notification sound:', error);
            });
        }
    <?php endif; ?>
});

// تحديد الإشعار كمقروء
function markAsRead(notificationId) {
    fetch('api/notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'mark_as_read',
            notification_id: notificationId
        })
    });
}

// إخفاء الإشعار
function dismissNotification(notificationId) {
    fetch('api/notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'dismiss',
            notification_id: notificationId
        })
    }).then(function() {
        // إخفاء الإشعار من الواجهة
        const elements = document.querySelectorAll(`[data-notification-id="${notificationId}"]`);
        elements.forEach(function(element) {
            element.style.display = 'none';
        });
        
        // تحديث عداد الإشعارات
        updateNotificationCount();
    });
}

// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    fetch('api/notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'mark_all_as_read'
        })
    }).then(function() {
        // إزالة علامة عدم القراءة من جميع الإشعارات
        const unreadItems = document.querySelectorAll('.notification-item.unread');
        unreadItems.forEach(function(item) {
            item.classList.remove('unread');
        });
        
        // تحديث عداد الإشعارات
        updateNotificationCount();
    });
}

// تحديث عداد الإشعارات
function updateNotificationCount() {
    fetch('api/notifications.php?action=get_unread_count')
        .then(response => response.json())
        .then(data => {
            const badge = document.querySelector('.notification-icon-container .badge');
            if (data.count > 0) {
                if (badge) {
                    badge.textContent = data.count > 99 ? '99+' : data.count;
                } else {
                    // إنشاء badge جديد
                    const button = document.querySelector('#notificationDropdown');
                    const newBadge = document.createElement('span');
                    newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                    newBadge.textContent = data.count > 99 ? '99+' : data.count;
                    button.appendChild(newBadge);
                }
            } else {
                if (badge) {
                    badge.remove();
                }
            }
        });
}

// النقر على إشعار لتحديده كمقروء
document.addEventListener('click', function(e) {
    const notificationItem = e.target.closest('.notification-item');
    if (notificationItem && notificationItem.classList.contains('unread')) {
        const notificationId = notificationItem.getAttribute('data-notification-id');
        markAsRead(notificationId);
        notificationItem.classList.remove('unread');
        updateNotificationCount();
    }
});
</script>

<?php
// دالة مساعدة لحساب الوقت المنقضي
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}
?>
