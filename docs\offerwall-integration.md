# دليل دمج CPALead Offerwall

## نظرة عامة
تم دمج CPALead Offerwall بالكامل مع نظام CPA الخاص بنا، مما يوفر للمستخدمين طريقة سهلة لإكمال العروض وكسب المال.

## معلومات Offerwall
- **الرابط المباشر**: https://fastrsrvr.com/list/191
- **معرف Offerwall**: 191
- **الشبكة**: CPALead

## طرق التكامل المتاحة

### 1. الرابط المباشر
```
https://fastrsrvr.com/list/191?subid=USER_ID_TIMESTAMP
```

### 2. Iframe Integration
```html
<iframe 
    sandbox='allow-popups allow-same-origin allow-scripts allow-top-navigation-by-user-activation allow-popups-to-escape-sandbox' 
    src='https://fastrsrvr.com/list/191?subid=USER_ID_TIMESTAMP' 
    style='width:100%; height:690px; border:none;' 
    frameborder='0'>
</iframe>
```

### 3. JavaScript Integration
```html
<script type='text/javascript' src='https://fastrsrvr.com/offerwall.php?bid=191'></script>
<script>
// استخدام الدالة cpld_run() لفتح Offerwall
function openOfferwall() {
    cpld_run();
}
</script>
```

### 4. Android App Integration
```java
// Import Libs
import android.content.Intent;
import android.net.Uri;

// فتح Offerwall في التطبيق
Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse('https://fastrsrvr.com/list/191?subid=USER_SUBID'));
startActivity(browserIntent);
```

## التكامل في النظام

### الملفات المضافة:
- `offerwall/index.php` - الصفحة الرئيسية للـ Offerwall
- `offerwall/mobile.php` - نسخة محسنة للجوال
- `offerwall/widget.php` - Widget قابل للتضمين
- `offerwall/offerwall.js` - مكتبة JavaScript للتحكم
- `api/track-event.php` - API لتتبع الأحداث

### قاعدة البيانات:
- `offerwall_events` - تتبع أحداث المستخدمين
- `offerwall_stats` - إحصائيات يومية للـ Offerwall

## استخدام JavaScript API

### إنشاء مدير Offerwall
```javascript
const offerwallManager = new CPAOfferwallManager({
    userId: 123,
    subId: 'custom_subid',
    theme: 'light',
    autoRefresh: true,
    refreshInterval: 300000 // 5 دقائق
});
```

### فتح Offerwall في نافذة منبثقة
```javascript
offerwallManager.openPopup(800, 600);
```

### تحميل في iframe
```javascript
offerwallManager.loadInIframe('offerwall-container', {
    width: '100%',
    height: '600px',
    showLoading: true
});
```

### استخدام الدالة المتوافقة مع CPALead
```javascript
// متوافق مع كود CPALead الأصلي
cpld_run();
```

## تتبع الأحداث

### الأحداث المتاحة:
- `popup_opened` - فتح نافذة منبثقة
- `new_window_opened` - فتح نافذة جديدة
- `iframe_loaded` - تحميل iframe
- `offer_clicked` - النقر على عرض
- `offer_completed` - إكمال عرض
- `refreshed` - تحديث Offerwall

### مثال على التتبع:
```javascript
// تتبع حدث مخصص
offerwallManager.trackEvent('custom_event', {
    customData: 'value'
});
```

## إعداد SubID

### تنسيق SubID:
```
USER_ID_TIMESTAMP
```

### مثال:
```
123_1640995200
```

### تمرير SubID مخصص:
```javascript
offerwallManager.updateSubId('my_custom_subid');
```

## Postback Integration

### إعداد Postback URL في CPALead:
```
https://q12.ct.ws/tracking/postback.php?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&payout={payout}&lead_id={lead_id}&country_iso={country_iso}&password=YOUR_PASSWORD&virtual_currency={virtual_currency}
```

### معاملات Postback المدعومة:
- `{campaign_id}` - معرف الحملة
- `{campaign_name}` - اسم الحملة
- `{subid}` - المعرف الفرعي الأساسي
- `{subid2}` - معرف فرعي إضافي
- `{subid3}` - معرف فرعي إضافي
- `{payout}` - قيمة العمولة
- `{lead_id}` - معرف العميل المحتمل
- `{country_iso}` - رمز البلد
- `{virtual_currency}` - العملة الافتراضية
- `{password}` - كلمة مرور الحماية

## الأمان

### حماية IP:
- CPALead IP: `************`
- يتم التحقق من IP المصدر في Postback

### كلمة مرور Postback:
- يجب تعيين كلمة مرور قوية
- يتم التحقق من كلمة المرور في كل Postback

## إحصائيات Offerwall

### البيانات المتتبعة:
- عدد المشاهدات اليومية
- عدد الإكمالات اليومية
- الأرباح اليومية
- أحداث المستخدمين

### الوصول للإحصائيات:
```javascript
offerwallManager.getStats().then(stats => {
    console.log('Offerwall Stats:', stats);
});
```

## التخصيص

### تخصيص المظهر:
```css
.offerwall-widget {
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.offerwall-widget:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}
```

### تخصيص الأحداث:
```javascript
offerwallManager.onOfferCompleted = function(data) {
    // معالجة مخصصة لإكمال العرض
    showCustomNotification('تم إكمال العرض!');
    updateUserBalance();
};
```

## استكشاف الأخطاء

### مشاكل شائعة:
1. **Offerwall لا يحمل**: تحقق من الاتصال بالإنترنت
2. **النوافذ المنبثقة محجوبة**: اطلب من المستخدم السماح بالنوافذ المنبثقة
3. **Postback لا يعمل**: تحقق من IP وكلمة المرور

### تسجيل الأخطاء:
```javascript
// تفعيل وضع التطوير
window.offerwallDebug = true;
```

## أمثلة عملية

### مثال 1: Widget بسيط
```html
<div id="my-offerwall"></div>
<script>
const manager = new CPAOfferwallManager({userId: 123});
manager.loadInIframe('my-offerwall');
</script>
```

### مثال 2: زر فتح Offerwall
```html
<button onclick="openOfferwall()">اربح المال الآن!</button>
<script>
function openOfferwall() {
    cpld_run();
}
</script>
```

### مثال 3: Offerwall للجوال
```html
<a href="offerwall/mobile.php" class="btn btn-success">
    افتح Offerwall للجوال
</a>
```

## الدعم والمساعدة

### الملفات المرجعية:
- `offerwall/index.php` - الصفحة الرئيسية
- `offerwall/offerwall.js` - مكتبة JavaScript
- `api/track-event.php` - API التتبع

### اختبار التكامل:
1. افتح `https://q12.ct.ws/offerwall/`
2. تحقق من تحميل العروض
3. اختبر إكمال عرض
4. تحقق من تسجيل الأحداث

---

**ملاحظة**: تأكد من إعداد Postback URL في لوحة تحكم CPALead لتلقي إشعارات التحويلات.
