<?php
/**
 * إصلاح سريع لجدول المستخدمين
 */

require_once 'config/config.php';

echo "<h2>🔧 إصلاح جدول المستخدمين</h2>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h3>1. فحص جدول المستخدمين:</h3>";
    
    // فحص وجود الجدول
    $check_table = $db->query("SHOW TABLES LIKE 'users'");
    if ($check_table->rowCount() == 0) {
        echo "❌ جدول المستخدمين غير موجود. إنشاء الجدول...<br>";
        
        // إنشاء جدول المستخدمين
        $create_table = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) DEFAULT NULL,
            first_name VARCHAR(50) DEFAULT NULL,
            last_name VARCHAR(50) DEFAULT NULL,
            role ENUM('admin', 'publisher') DEFAULT 'publisher',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            balance DECIMAL(10,2) DEFAULT 0.00,
            total_earnings DECIMAL(10,2) DEFAULT 0.00,
            phone VARCHAR(20),
            country VARCHAR(50),
            payment_method ENUM('paypal', 'bank_transfer', 'payoneer') DEFAULT 'paypal',
            payment_details TEXT,
            api_key VARCHAR(64) UNIQUE,
            is_auto_generated BOOLEAN DEFAULT FALSE,
            login_count INT DEFAULT 0,
            ip_address VARCHAR(45),
            user_agent TEXT,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_is_auto_generated (is_auto_generated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($create_table);
        echo "✅ تم إنشاء جدول المستخدمين<br>";
    } else {
        echo "✅ جدول المستخدمين موجود<br>";
    }
    
    echo "<h3>2. إنشاء المستخدمين الأساسيين:</h3>";
    
    // إنشاء مستخدم admin
    $admin_check = $db->prepare("SELECT id FROM users WHERE username = 'admin'");
    $admin_check->execute();
    
    if ($admin_check->rowCount() == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $insert_admin = $db->prepare("INSERT INTO users (username, password, email, first_name, last_name, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $insert_admin->execute(['admin', $admin_password, '<EMAIL>', 'مدير', 'النظام', 'admin', 'active']);
        echo "✅ تم إنشاء حساب المدير: username=admin, password=admin123<br>";
    } else {
        echo "✅ حساب المدير موجود بالفعل<br>";
    }
    
    // إنشاء مستخدم تجريبي
    $test_check = $db->prepare("SELECT id FROM users WHERE username = 'test'");
    $test_check->execute();
    
    if ($test_check->rowCount() == 0) {
        $test_password = password_hash('test', PASSWORD_DEFAULT);
        $insert_test = $db->prepare("INSERT INTO users (username, password, role, status) VALUES (?, ?, ?, ?)");
        $insert_test->execute(['test', $test_password, 'publisher', 'active']);
        echo "✅ تم إنشاء مستخدم تجريبي: username=test, password=test<br>";
    } else {
        echo "✅ المستخدم التجريبي موجود بالفعل<br>";
    }
    
    // إنشاء مستخدمين سريعين للاختبار
    $quick_users = [
        ['a', 'a'],
        ['b', 'b'],
        ['c', 'c'],
        ['1', '1'],
        ['x', 'x']
    ];
    
    echo "<h3>3. إنشاء مستخدمين سريعين للاختبار:</h3>";
    
    foreach ($quick_users as $user_data) {
        $username = $user_data[0];
        $password = $user_data[1];
        
        $quick_check = $db->prepare("SELECT id FROM users WHERE username = ?");
        $quick_check->execute([$username]);
        
        if ($quick_check->rowCount() == 0) {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $insert_quick = $db->prepare("INSERT INTO users (username, password, role, status, is_auto_generated) VALUES (?, ?, ?, ?, ?)");
            $insert_quick->execute([$username, $hashed_password, 'publisher', 'active', true]);
            echo "✅ تم إنشاء مستخدم سريع: username={$username}, password={$password}<br>";
        } else {
            echo "✅ المستخدم {$username} موجود بالفعل<br>";
        }
    }
    
    echo "<h3>4. عرض جميع المستخدمين:</h3>";
    
    $all_users = $db->query("SELECT id, username, role, status, is_auto_generated, created_at FROM users ORDER BY id")->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($all_users) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>ID</th><th>اسم المستخدم</th><th>الدور</th><th>الحالة</th><th>تلقائي</th><th>تاريخ الإنشاء</th>";
        echo "</tr>";
        
        foreach ($all_users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td><strong>{$user['username']}</strong></td>";
            echo "<td>" . ($user['role'] == 'admin' ? '👑 مدير' : '👤 ناشر') . "</td>";
            echo "<td>" . ($user['status'] == 'active' ? '✅ نشط' : '❌ غير نشط') . "</td>";
            echo "<td>" . ($user['is_auto_generated'] ? '🤖 تلقائي' : '👨‍💻 يدوي') . "</td>";
            echo "<td>{$user['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<hr>";
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    echo "<h4>🔑 بيانات تسجيل الدخول المتاحة:</h4>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb;'>";
    echo "<h5>👑 حساب المدير</h5>";
    echo "<strong>Username:</strong> admin<br>";
    echo "<strong>Password:</strong> admin123<br>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; border: 1px solid #bee5eb;'>";
    echo "<h5>🧪 حساب تجريبي</h5>";
    echo "<strong>Username:</strong> test<br>";
    echo "<strong>Password:</strong> test<br>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffeaa7;'>";
    echo "<h5>⚡ حسابات سريعة</h5>";
    echo "<strong>Username:</strong> a, b, c, 1, x<br>";
    echo "<strong>Password:</strong> نفس اسم المستخدم<br>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<p><a href='auth/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px;'>🔐 تسجيل الدخول</a>";
    echo "<a href='test-login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px;'>🧪 اختبار النظام</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;'>";
    echo "<strong>رسالة الخطأ:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>رقم الخطأ:</strong> " . $e->getCode() . "<br>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3, h4, h5 {
    color: #333;
}
table {
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: right;
}
</style>
