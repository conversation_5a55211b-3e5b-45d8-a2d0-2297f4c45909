/* تنسيقات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 10px 15px;
    margin: 2px 0;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: #e9ecef;
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: #e7f1ff;
}

.sidebar .nav-link .feather {
    margin-right: 4px;
    color: #999;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* الحدود الملونة للبطاقات */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* الألوان */
.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* الجداول */
.table {
    color: #858796;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #e3e6f0;
    background-color: #f8f9fc;
    font-weight: 600;
    color: #6e707e;
}

.table td {
    border-top: 1px solid #e3e6f0;
    vertical-align: middle;
}

/* الأزرار */
.btn {
    border-radius: 0.35rem;
    font-weight: 400;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
}

/* الهيدر */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* النماذج */
.form-control {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* الإشعارات */
.alert {
    border: none;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
}

/* الرسوم البيانية */
.chart-area {
    position: relative;
    height: 10rem;
    width: 100%;
}

.chart-bar {
    position: relative;
    height: 10rem;
    width: 100%;
}

.chart-pie {
    position: relative;
    height: 15rem;
    width: 100%;
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
}

/* تحسينات إضافية */
.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.rounded {
    border-radius: 0.35rem !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-uppercase {
    text-transform: uppercase !important;
}

/* تحسينات للنصوص العربية */
.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}
