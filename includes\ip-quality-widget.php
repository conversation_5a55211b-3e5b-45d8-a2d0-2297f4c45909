<?php
/**
 * Widget عرض معلومات جودة IP
 * يمكن تضمينه في أي صفحة لعرض معلومات IP المستخدم
 */

// التأكد من تحميل الفئات المطلوبة
if (!class_exists('IPQuality')) {
    require_once __DIR__ . '/IPQuality.php';
}

function renderIPQualityWidget($ip_address, $database, $show_details = true) {
    $ipQuality = new IPQuality($database);
    
    // فحص جودة IP
    $ip_info = $ipQuality->checkIPQuality($ip_address);
    
    // تحديد لون درجة الجودة
    $score = intval($ip_info['quality_score']);
    $score_class = 'danger';
    $score_icon = 'exclamation-triangle';
    
    if ($score >= 90) {
        $score_class = 'success';
        $score_icon = 'check-circle';
    } elseif ($score >= 75) {
        $score_class = 'info';
        $score_icon = 'info-circle';
    } elseif ($score >= 50) {
        $score_class = 'warning';
        $score_icon = 'exclamation-circle';
    }
    
    // تحديد التهديدات
    $threats = [];
    if ($ip_info['is_vpn']) $threats[] = 'VPN';
    if ($ip_info['is_proxy']) $threats[] = 'Proxy';
    if ($ip_info['is_tor']) $threats[] = 'Tor';
    if ($ip_info['is_crawler']) $threats[] = 'Bot';
    
    ob_start();
    ?>
    
    <div class="ip-quality-widget">
        <div class="card border-<?php echo $score_class; ?>">
            <div class="card-header bg-<?php echo $score_class; ?> text-white">
                <h6 class="mb-0">
                    <i class="fas fa-<?php echo $score_icon; ?> me-2"></i>
                    معلومات جودة IP
                </h6>
            </div>
            <div class="card-body">
                <!-- درجة الجودة -->
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="display-6 text-<?php echo $score_class; ?>"><?php echo $score; ?></div>
                            <small class="text-muted">درجة الجودة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="display-6 text-secondary"><?php echo $ip_info['fraud_score']; ?></div>
                            <small class="text-muted">درجة الاحتيال</small>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات أساسية -->
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>IP Address:</strong> 
                        <code><?php echo htmlspecialchars($ip_address); ?></code>
                    </div>
                </div>
                
                <?php if ($show_details): ?>
                <!-- الموقع الجغرافي -->
                <?php if ($ip_info['country_name']): ?>
                <div class="row mb-2">
                    <div class="col-12">
                        <i class="fas fa-map-marker-alt text-primary me-2"></i>
                        <strong>الموقع:</strong>
                        <?php echo htmlspecialchars($ip_info['city'] . ', ' . $ip_info['country_name']); ?>
                        <?php if ($ip_info['country_code']): ?>
                            <span class="badge bg-secondary ms-1"><?php echo strtoupper($ip_info['country_code']); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- مزود الخدمة -->
                <?php if ($ip_info['isp']): ?>
                <div class="row mb-2">
                    <div class="col-12">
                        <i class="fas fa-wifi text-info me-2"></i>
                        <strong>مزود الخدمة:</strong>
                        <?php echo htmlspecialchars($ip_info['isp']); ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- نوع الاتصال -->
                <?php if ($ip_info['connection_type']): ?>
                <div class="row mb-2">
                    <div class="col-12">
                        <i class="fas fa-network-wired text-secondary me-2"></i>
                        <strong>نوع الاتصال:</strong>
                        <?php echo htmlspecialchars($ip_info['connection_type']); ?>
                        <?php if ($ip_info['is_mobile']): ?>
                            <span class="badge bg-info ms-1">Mobile</span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                <?php endif; ?>
                
                <!-- التهديدات -->
                <?php if (!empty($threats)): ?>
                <div class="row mb-2">
                    <div class="col-12">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <strong>التهديدات:</strong>
                        <?php foreach ($threats as $threat): ?>
                            <span class="badge bg-danger ms-1"><?php echo $threat; ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php else: ?>
                <div class="row mb-2">
                    <div class="col-12">
                        <i class="fas fa-shield-alt text-success me-2"></i>
                        <strong>الحالة:</strong>
                        <span class="badge bg-success">نظيف</span>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- معلومات الكاش -->
                <div class="row">
                    <div class="col-12">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            آخر فحص: <?php echo date('Y-m-d H:i', strtotime($ip_info['last_checked'])); ?>
                            <?php if (isset($ip_info['is_cached']) && $ip_info['is_cached']): ?>
                                <span class="badge bg-light text-dark ms-1">مخزن مؤقتاً</span>
                            <?php endif; ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    .ip-quality-widget .card {
        transition: transform 0.2s ease-in-out;
    }
    .ip-quality-widget .card:hover {
        transform: translateY(-2px);
    }
    .ip-quality-widget .display-6 {
        font-size: 2rem;
        font-weight: bold;
    }
    </style>
    
    <?php
    return ob_get_clean();
}

/**
 * عرض Widget مبسط لجودة IP
 */
function renderSimpleIPQualityBadge($ip_address, $database) {
    $ipQuality = new IPQuality($database);
    $ip_info = $ipQuality->checkIPQuality($ip_address);
    
    $score = intval($ip_info['quality_score']);
    $badge_class = 'danger';
    
    if ($score >= 90) $badge_class = 'success';
    elseif ($score >= 75) $badge_class = 'info';
    elseif ($score >= 50) $badge_class = 'warning';
    
    $threats = [];
    if ($ip_info['is_vpn']) $threats[] = 'VPN';
    if ($ip_info['is_proxy']) $threats[] = 'Proxy';
    if ($ip_info['is_tor']) $threats[] = 'Tor';
    
    ob_start();
    ?>
    
    <span class="ip-quality-badge" title="درجة جودة IP: <?php echo $score; ?>/100">
        <span class="badge bg-<?php echo $badge_class; ?>"><?php echo $score; ?></span>
        <?php if (!empty($threats)): ?>
            <?php foreach ($threats as $threat): ?>
                <span class="badge bg-danger ms-1"><?php echo $threat; ?></span>
            <?php endforeach; ?>
        <?php endif; ?>
    </span>
    
    <?php
    return ob_get_clean();
}

/**
 * فحص سريع لجودة IP وإرجاع النتيجة
 */
function quickIPQualityCheck($ip_address, $database) {
    $ipQuality = new IPQuality($database);
    
    if (!$ipQuality->isEnabled()) {
        return [
            'allowed' => true,
            'score' => 50,
            'reason' => 'فحص جودة IP معطل'
        ];
    }
    
    $is_allowed = $ipQuality->isIPAllowed($ip_address);
    $ip_info = $ipQuality->checkIPQuality($ip_address);
    
    $reasons = [];
    if ($ip_info['quality_score'] < 75) {
        $reasons[] = "جودة منخفضة ({$ip_info['quality_score']}/100)";
    }
    if ($ip_info['is_vpn']) $reasons[] = 'VPN';
    if ($ip_info['is_tor']) $reasons[] = 'Tor';
    
    return [
        'allowed' => $is_allowed,
        'score' => $ip_info['quality_score'],
        'threats' => [
            'vpn' => $ip_info['is_vpn'],
            'proxy' => $ip_info['is_proxy'],
            'tor' => $ip_info['is_tor'],
            'crawler' => $ip_info['is_crawler']
        ],
        'location' => [
            'country' => $ip_info['country_name'],
            'city' => $ip_info['city'],
            'country_code' => $ip_info['country_code']
        ],
        'isp' => $ip_info['isp'],
        'reason' => implode(', ', $reasons)
    ];
}
?>
