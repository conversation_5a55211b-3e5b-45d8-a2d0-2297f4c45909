<?php
require_once '../config/config.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$usernameGenerator = new UsernameGenerator($database);

// معالجة التسجيل السريع
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'quick_register') {
        try {
            // توليد اسم مستخدم وكلمة مرور تلقائياً
            $username = $usernameGenerator->generateUniqueUsername();
            $password = $usernameGenerator->generateWordPassword();
            
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $api_key = hash('sha256', uniqid(rand(), true));
            
            $insert_query = "INSERT INTO users (username, password, api_key, is_auto_generated, ip_address, user_agent) 
                            VALUES (:username, :password, :api_key, 1, :ip_address, :user_agent)";
            
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':username', $username);
            $insert_stmt->bindParam(':password', $hashed_password);
            $insert_stmt->bindParam(':api_key', $api_key);
            $insert_stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
            $insert_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
            
            if ($insert_stmt->execute()) {
                $user_id = $db->lastInsertId();
                
                // تسجيل دخول تلقائي
                $_SESSION['user_id'] = $user_id;
                $_SESSION['username'] = $username;
                $_SESSION['role'] = 'publisher';
                
                // تحديث آخر تسجيل دخول
                $login_update = "UPDATE users SET last_login = NOW(), login_count = login_count + 1 WHERE id = :user_id";
                $login_stmt = $db->prepare($login_update);
                $login_stmt->bindParam(':user_id', $user_id);
                $login_stmt->execute();
                
                // حفظ بيانات الحساب في الجلسة لعرضها
                $_SESSION['new_account'] = [
                    'username' => $username,
                    'password' => $password
                ];
                
                header('Location: ../index.php?welcome=1');
                exit();
            }
            
        } catch (PDOException $e) {
            logError("خطأ في التسجيل السريع: " . $e->getMessage());
            $error = 'حدث خطأ في النظام';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل سريع - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .quick-register-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            text-align: center;
        }
        .quick-register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
        }
        .quick-register-body {
            padding: 3rem 2rem;
        }
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .btn-quick {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .btn-quick:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .features-list {
            text-align: right;
            margin: 2rem 0;
        }
        .features-list li {
            margin-bottom: 0.5rem;
            color: #666;
        }
        .features-list i {
            color: #28a745;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="quick-register-card">
        <div class="quick-register-header">
            <i class="fas fa-rocket fa-4x mb-3"></i>
            <h2>ابدأ فوراً!</h2>
            <p class="mb-0">تسجيل سريع بنقرة واحدة</p>
        </div>
        
        <div class="quick-register-body">
            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <div class="feature-icon">
                <i class="fas fa-magic"></i>
            </div>
            
            <h4 class="mb-3">تسجيل تلقائي بدون تعقيد</h4>
            <p class="text-muted mb-4">
                سنقوم بإنشاء اسم مستخدم وكلمة مرور تلقائياً لك، 
                ويمكنك البدء في الربح فوراً!
            </p>
            
            <ul class="features-list list-unstyled">
                <li><i class="fas fa-check"></i>اسم مستخدم إنجليزي فريد</li>
                <li><i class="fas fa-check"></i>كلمة مرور آمنة</li>
                <li><i class="fas fa-check"></i>تسجيل دخول تلقائي</li>
                <li><i class="fas fa-check"></i>بدء فوري في الربح</li>
                <li><i class="fas fa-check"></i>بدون بيانات شخصية</li>
            </ul>
            
            <form method="POST">
                <input type="hidden" name="action" value="quick_register">
                <button type="submit" class="btn btn-quick btn-lg w-100 mb-3">
                    <i class="fas fa-bolt me-2"></i>إنشاء حساب فوراً
                </button>
            </form>
            
            <div class="text-center">
                <small class="text-muted">أو</small><br>
                <a href="register.php" class="text-decoration-none">تسجيل مخصص</a>
                <span class="mx-2">|</span>
                <a href="login.php" class="text-decoration-none">تسجيل الدخول</a>
            </div>
            
            <div class="mt-4 p-3 bg-light rounded">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    ستحصل على بيانات حسابك بعد التسجيل مباشرة
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
