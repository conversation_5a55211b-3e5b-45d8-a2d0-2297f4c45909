<?php
/**
 * مثال على كيفية إضافة إشعار عند إنشاء عرض جديد
 * يمكن دمج هذا الكود في صفحة إنشاء العروض
 */

require_once '../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$notificationManager = new NotificationManager($database);

// معالجة إنشاء العرض
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);
    $payout = floatval($_POST['payout']);
    $countries = sanitize($_POST['countries']);
    $type = sanitize($_POST['type']);
    
    try {
        // إنشاء العرض
        $offer_query = "INSERT INTO offers (title, description, payout, countries, type, status, created_by) 
                        VALUES (:title, :description, :payout, :countries, :type, 'active', :created_by)";
        
        $offer_stmt = $db->prepare($offer_query);
        $offer_stmt->bindParam(':title', $title);
        $offer_stmt->bindParam(':description', $description);
        $offer_stmt->bindParam(':payout', $payout);
        $offer_stmt->bindParam(':countries', $countries);
        $offer_stmt->bindParam(':type', $type);
        $offer_stmt->bindParam(':created_by', $_SESSION['user_id']);
        
        if ($offer_stmt->execute()) {
            $offer_id = $db->lastInsertId();
            
            // إرسال إشعار للمستخدمين عن العرض الجديد
            if ($notificationManager->getSetting('offer_notifications', true)) {
                $notification_id = $notificationManager->createOfferNotification($offer_id, $_SESSION['user_id']);
                
                if ($notification_id) {
                    $_SESSION['success'] = 'تم إنشاء العرض بنجاح وإرسال إشعار للمستخدمين!';
                } else {
                    $_SESSION['success'] = 'تم إنشاء العرض بنجاح ولكن فشل في إرسال الإشعار';
                }
            } else {
                $_SESSION['success'] = 'تم إنشاء العرض بنجاح!';
            }
            
            // يمكن أيضاً إنشاء إشعار مخصص
            $custom_notification = [
                'title' => 'عرض جديد مضاف!',
                'message' => "تم إضافة عرض جديد: {$title} بعمولة " . CURRENCY_SYMBOL . number_format($payout, 2),
                'type' => 'offer',
                'target_audience' => 'active', // أو 'all' أو 'new'
                'target_users' => null,
                'offer_id' => $offer_id,
                'priority' => 'high',
                'show_popup' => true,
                'show_banner' => true,
                'show_sidebar' => true,
                'auto_hide' => true,
                'hide_after_seconds' => 15,
                'start_date' => date('Y-m-d H:i:s'),
                'end_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
                'created_by' => $_SESSION['user_id']
            ];
            
            // إنشاء الإشعار المخصص (اختياري)
            // $notificationManager->createNotification($custom_notification);
            
        } else {
            $_SESSION['error'] = 'حدث خطأ في إنشاء العرض';
        }
        
    } catch (PDOException $e) {
        logError("خطأ في إنشاء العرض: " . $e->getMessage());
        $_SESSION['error'] = 'حدث خطأ في النظام';
    }
    
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء عرض مع إشعار - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>إنشاء عرض جديد مع إشعار تلقائي</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($_SESSION['success'])): ?>
                            <div class="alert alert-success">
                                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($_SESSION['error'])): ?>
                            <div class="alert alert-danger">
                                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="mb-3">
                                <label for="title" class="form-label">عنوان العرض</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف العرض</label>
                                <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payout" class="form-label">العمولة</label>
                                        <input type="number" class="form-control" id="payout" name="payout" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="type" class="form-label">نوع العرض</label>
                                        <select class="form-control" id="type" name="type" required>
                                            <option value="CPA">CPA</option>
                                            <option value="CPL">CPL</option>
                                            <option value="CPI">CPI</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="countries" class="form-label">البلدان المستهدفة</label>
                                <input type="text" class="form-control" id="countries" name="countries" 
                                       placeholder="مثال: US, UK, CA" required>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-bell me-2"></i>إعدادات الإشعار</h6>
                                <p class="mb-2">عند إنشاء هذا العرض:</p>
                                <ul class="mb-0">
                                    <li>سيتم إرسال إشعار تلقائي للمستخدمين النشطين</li>
                                    <li>سيظهر الإشعار كبانر وفي الشريط الجانبي</li>
                                    <li>سيختفي الإشعار تلقائياً بعد 15 ثانية</li>
                                    <li>سيبقى الإشعار نشطاً لمدة 7 أيام</li>
                                </ul>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>إنشاء العرض وإرسال الإشعار
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- معاينة الإشعار -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>معاينة الإشعار</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-primary">
                            <h6><i class="fas fa-gift me-2"></i>عرض جديد متاح!</h6>
                            <p class="mb-0">عرض جديد: [عنوان العرض] - العمولة: $[المبلغ]</p>
                        </div>
                        
                        <small class="text-muted">
                            هذا مثال على كيف سيظهر الإشعار للمستخدمين
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
