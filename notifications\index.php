<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit();
}

$database = new Database();
$notificationManager = new NotificationManager($database);
$user_id = $_SESSION['user_id'];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = sanitize($_POST['action'] ?? '');
    
    switch ($action) {
        case 'mark_as_read':
            $notification_id = intval($_POST['notification_id']);
            $notificationManager->markAsRead($user_id, $notification_id);
            break;
            
        case 'dismiss':
            $notification_id = intval($_POST['notification_id']);
            $notificationManager->dismissNotification($user_id, $notification_id);
            break;
            
        case 'mark_all_as_read':
            $db = $database->getConnection();
            $query = "UPDATE user_notifications SET is_read = 1, read_at = NOW() WHERE user_id = :user_id AND is_read = 0";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            $_SESSION['success'] = 'تم تحديد جميع الإشعارات كمقروءة';
            break;
    }
    
    header('Location: index.php');
    exit();
}

// جلب الإشعارات مع التصفح
$page = intval($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

$filter = sanitize($_GET['filter'] ?? 'all');
$filter_clause = '';

switch ($filter) {
    case 'unread':
        $filter_clause = 'AND un.is_read = 0';
        break;
    case 'read':
        $filter_clause = 'AND un.is_read = 1';
        break;
    case 'offers':
        $filter_clause = 'AND n.type = "offer"';
        break;
    case 'system':
        $filter_clause = 'AND n.type = "system"';
        break;
}

$db = $database->getConnection();

// عدد الإشعارات الإجمالي
$count_query = "SELECT COUNT(*) 
                FROM notifications n
                INNER JOIN user_notifications un ON n.id = un.notification_id
                WHERE un.user_id = :user_id 
                AND n.is_active = 1
                {$filter_clause}";

$count_stmt = $db->prepare($count_query);
$count_stmt->bindParam(':user_id', $user_id);
$count_stmt->execute();
$total_notifications = $count_stmt->fetchColumn();

// جلب الإشعارات
$notifications_query = "SELECT n.*, un.is_read, un.is_dismissed, un.read_at, un.dismissed_at,
                               o.title as offer_title, o.payout as offer_payout
                        FROM notifications n
                        INNER JOIN user_notifications un ON n.id = un.notification_id
                        LEFT JOIN offers o ON n.offer_id = o.id
                        WHERE un.user_id = :user_id 
                        AND n.is_active = 1
                        {$filter_clause}
                        ORDER BY n.created_at DESC
                        LIMIT :limit OFFSET :offset";

$notifications_stmt = $db->prepare($notifications_query);
$notifications_stmt->bindParam(':user_id', $user_id);
$notifications_stmt->bindParam(':limit', $per_page, PDO::PARAM_INT);
$notifications_stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$notifications_stmt->execute();
$notifications = $notifications_stmt->fetchAll(PDO::FETCH_ASSOC);

// حساب عدد الصفحات
$total_pages = ceil($total_notifications / $per_page);

// إحصائيات سريعة
$unread_count = $notificationManager->getUnreadCount($user_id);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .notification-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.2s;
        }
        .notification-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .notification-item.unread {
            border-left: 4px solid #007bff;
            background-color: #f8f9ff;
        }
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        .notification-content {
            flex-grow: 1;
        }
        .notification-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .notification-message {
            color: #666;
            margin-bottom: 5px;
        }
        .notification-meta {
            font-size: 0.85rem;
            color: #999;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">الإشعارات</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <?php if ($unread_count > 0): ?>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="mark_all_as_read">
                                <button type="submit" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-check-double me-1"></i>تحديد الكل كمقروء
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary"><?php echo number_format($total_notifications); ?></h5>
                                <p class="card-text">إجمالي الإشعارات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?php echo number_format($unread_count); ?></h5>
                                <p class="card-text">غير مقروءة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?php echo number_format($total_notifications - $unread_count); ?></h5>
                                <p class="card-text">مقروءة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?php echo $total_pages; ?></h5>
                                <p class="card-text">الصفحات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <a href="?filter=all" class="btn btn-<?php echo $filter === 'all' ? 'primary' : 'outline-primary'; ?> btn-sm">
                                        الكل
                                    </a>
                                    <a href="?filter=unread" class="btn btn-<?php echo $filter === 'unread' ? 'warning' : 'outline-warning'; ?> btn-sm">
                                        غير مقروءة
                                    </a>
                                    <a href="?filter=read" class="btn btn-<?php echo $filter === 'read' ? 'success' : 'outline-success'; ?> btn-sm">
                                        مقروءة
                                    </a>
                                    <a href="?filter=offers" class="btn btn-<?php echo $filter === 'offers' ? 'info' : 'outline-info'; ?> btn-sm">
                                        العروض
                                    </a>
                                    <a href="?filter=system" class="btn btn-<?php echo $filter === 'system' ? 'secondary' : 'outline-secondary'; ?> btn-sm">
                                        النظام
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <small class="text-muted">
                                    عرض <?php echo count($notifications); ?> من <?php echo number_format($total_notifications); ?> إشعار
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الإشعارات -->
                <?php if (empty($notifications)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد إشعارات</h4>
                        <p class="text-muted">لم يتم العثور على إشعارات بالفلتر المحدد</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($notifications as $notification): ?>
                        <div class="notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?> p-3">
                            <div class="d-flex">
                                <div class="notification-icon bg-<?php echo $notification['type'] === 'offer' ? 'primary' : $notification['type']; ?> text-white me-3">
                                    <?php
                                    $icons = [
                                        'info' => 'fa-info-circle',
                                        'success' => 'fa-check-circle',
                                        'warning' => 'fa-exclamation-triangle',
                                        'danger' => 'fa-exclamation-circle',
                                        'offer' => 'fa-gift',
                                        'system' => 'fa-cog'
                                    ];
                                    ?>
                                    <i class="fas <?php echo $icons[$notification['type']]; ?>"></i>
                                </div>
                                
                                <div class="notification-content">
                                    <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                    <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                    
                                    <?php if ($notification['offer_title']): ?>
                                        <div class="mt-2">
                                            <a href="../offers/view.php?id=<?php echo $notification['offer_id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye me-1"></i>عرض العرض
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="notification-meta mt-2">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?>
                                        
                                        <?php if ($notification['is_read']): ?>
                                            <span class="badge bg-success ms-2">مقروء</span>
                                        <?php endif; ?>
                                        
                                        <?php if ($notification['is_dismissed']): ?>
                                            <span class="badge bg-secondary ms-2">مخفي</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="notification-actions">
                                    <div class="btn-group-vertical btn-group-sm">
                                        <?php if (!$notification['is_read']): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_as_read">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <button type="submit" class="btn btn-outline-success btn-sm" title="تحديد كمقروء">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <?php if (!$notification['is_dismissed']): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="dismiss">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <button type="submit" class="btn btn-outline-warning btn-sm" title="إخفاء">
                                                    <i class="fas fa-eye-slash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <!-- التصفح -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="تصفح الإشعارات">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&filter=<?php echo $filter; ?>">السابق</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&filter=<?php echo $filter; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&filter=<?php echo $filter; ?>">التالي</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
