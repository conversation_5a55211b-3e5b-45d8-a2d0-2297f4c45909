/**
 * CPALead Offerwall JavaScript Integration
 * يوفر وظائف لفتح وإدارة Offerwall
 */

class CPAOfferwallManager {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || 'https://fastrsrvr.com/list/191';
        this.userId = options.userId || null;
        this.subId = options.subId || null;
        this.theme = options.theme || 'light';
        this.autoRefresh = options.autoRefresh || false;
        this.refreshInterval = options.refreshInterval || 300000; // 5 دقائق
        
        this.init();
    }
    
    init() {
        // إنشاء SubID تلقائي إذا لم يتم توفيره
        if (!this.subId && this.userId) {
            this.subId = this.userId + '_' + Date.now();
        }
        
        // تفعيل التحديث التلقائي
        if (this.autoRefresh) {
            this.startAutoRefresh();
        }
        
        // إضافة مستمعي الأحداث
        this.addEventListeners();
    }
    
    /**
     * بناء رابط Offerwall مع المعاملات
     */
    buildOfferwallUrl(additionalParams = {}) {
        const params = new URLSearchParams();
        
        if (this.subId) {
            params.append('subid', this.subId);
        }
        
        // إضافة معاملات إضافية
        Object.keys(additionalParams).forEach(key => {
            params.append(key, additionalParams[key]);
        });
        
        return `${this.baseUrl}?${params.toString()}`;
    }
    
    /**
     * فتح Offerwall في نافذة منبثقة
     */
    openPopup(width = 800, height = 600) {
        const url = this.buildOfferwallUrl();
        const left = (screen.width - width) / 2;
        const top = (screen.height - height) / 2;
        
        const popup = window.open(
            url,
            'cpa_offerwall',
            `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
        );
        
        if (popup) {
            popup.focus();
            this.trackEvent('popup_opened');
        } else {
            alert('يرجى السماح للنوافذ المنبثقة لهذا الموقع');
        }
        
        return popup;
    }
    
    /**
     * فتح Offerwall في نافذة جديدة
     */
    openNewWindow() {
        const url = this.buildOfferwallUrl();
        const newWindow = window.open(url, '_blank');
        
        if (newWindow) {
            newWindow.focus();
            this.trackEvent('new_window_opened');
        }
        
        return newWindow;
    }
    
    /**
     * تحميل Offerwall في iframe
     */
    loadInIframe(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('Container not found:', containerId);
            return;
        }
        
        const iframe = document.createElement('iframe');
        iframe.src = this.buildOfferwallUrl();
        iframe.style.width = options.width || '100%';
        iframe.style.height = options.height || '600px';
        iframe.style.border = options.border || 'none';
        iframe.frameBorder = '0';
        iframe.sandbox = 'allow-popups allow-same-origin allow-scripts allow-top-navigation-by-user-activation allow-popups-to-escape-sandbox';
        
        // إضافة شاشة تحميل
        if (options.showLoading !== false) {
            this.showLoading(container);
            iframe.onload = () => this.hideLoading(container);
        }
        
        container.innerHTML = '';
        container.appendChild(iframe);
        
        this.trackEvent('iframe_loaded');
        return iframe;
    }
    
    /**
     * إظهار شاشة التحميل
     */
    showLoading(container) {
        const loading = document.createElement('div');
        loading.className = 'offerwall-loading';
        loading.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 200px; flex-direction: column;">
                <div style="border: 3px solid #f3f3f3; border-top: 3px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin-bottom: 15px;"></div>
                <p style="color: #666; margin: 0;">جاري تحميل العروض...</p>
            </div>
        `;
        
        // إضافة CSS للدوران
        if (!document.getElementById('offerwall-css')) {
            const style = document.createElement('style');
            style.id = 'offerwall-css';
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .offerwall-loading {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    z-index: 1000;
                }
            `;
            document.head.appendChild(style);
        }
        
        container.style.position = 'relative';
        container.appendChild(loading);
    }
    
    /**
     * إخفاء شاشة التحميل
     */
    hideLoading(container) {
        const loading = container.querySelector('.offerwall-loading');
        if (loading) {
            loading.remove();
        }
    }
    
    /**
     * تحديث Offerwall
     */
    refresh(containerId = null) {
        if (containerId) {
            const container = document.getElementById(containerId);
            const iframe = container.querySelector('iframe');
            if (iframe) {
                this.showLoading(container);
                iframe.src = iframe.src;
            }
        }
        
        this.trackEvent('refreshed');
    }
    
    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        this.refreshTimer = setInterval(() => {
            this.refresh();
        }, this.refreshInterval);
    }
    
    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }
    
    /**
     * تتبع الأحداث
     */
    trackEvent(eventName, data = {}) {
        // إرسال إحصائيات للخادم
        if (typeof fetch !== 'undefined') {
            fetch('/api/track-event.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event: eventName,
                    subid: this.subId,
                    timestamp: new Date().toISOString(),
                    data: data
                })
            }).catch(error => {
                console.log('Tracking error:', error);
            });
        }
        
        console.log('Offerwall Event:', eventName, data);
    }
    
    /**
     * إضافة مستمعي الأحداث
     */
    addEventListeners() {
        // مستمع لإغلاق النافذة
        window.addEventListener('beforeunload', () => {
            this.stopAutoRefresh();
        });
        
        // مستمع للرسائل من iframe
        window.addEventListener('message', (event) => {
            if (event.origin === 'https://fastrsrvr.com') {
                this.handleOfferwallMessage(event.data);
            }
        });
    }
    
    /**
     * معالجة رسائل من Offerwall
     */
    handleOfferwallMessage(data) {
        switch (data.type) {
            case 'offer_completed':
                this.trackEvent('offer_completed', data);
                this.onOfferCompleted(data);
                break;
            case 'offer_clicked':
                this.trackEvent('offer_clicked', data);
                break;
            default:
                console.log('Unknown message from offerwall:', data);
        }
    }
    
    /**
     * معالج إكمال العرض
     */
    onOfferCompleted(data) {
        // يمكن تخصيص هذه الدالة
        console.log('Offer completed:', data);
        
        // إظهار إشعار للمستخدم
        if (typeof showNotification === 'function') {
            showNotification('تم إكمال العرض بنجاح!', 'success');
        }
    }
    
    /**
     * تحديث SubID
     */
    updateSubId(newSubId) {
        this.subId = newSubId;
        this.trackEvent('subid_updated', { newSubId });
    }
    
    /**
     * الحصول على إحصائيات
     */
    async getStats() {
        try {
            const response = await fetch(`/api/offerwall-stats.php?subid=${this.subId}`);
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching stats:', error);
            return null;
        }
    }
}

// دالة مساعدة لفتح Offerwall (متوافقة مع CPALead)
function cpld_run(options = {}) {
    if (typeof window.offerwallManager === 'undefined') {
        window.offerwallManager = new CPAOfferwallManager(options);
    }
    
    return window.offerwallManager.openPopup();
}

// تصدير للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CPAOfferwallManager;
}

// إضافة للنطاق العام
window.CPAOfferwallManager = CPAOfferwallManager;
window.cpld_run = cpld_run;
