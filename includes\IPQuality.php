<?php
/**
 * فئة إدارة جودة IP
 * تدعم خدمات IPQualityScore و ProxyCheck
 */
class IPQuality {
    private $db;
    private $settings;
    
    public function __construct($database) {
        $this->db = $database->getConnection();
        $this->loadSettings();
    }
    
    /**
     * تحميل إعدادات جودة IP
     */
    private function loadSettings() {
        $query = "SELECT setting_key, setting_value FROM settings 
                  WHERE setting_key LIKE 'ip_quality_%'";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $this->settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->settings[$row['setting_key']] = $row['setting_value'];
        }
        
        // القيم الافتراضية
        $defaults = [
            'ip_quality_enabled' => '1',
            'ip_quality_api_key' => '',
            'ip_quality_service' => 'ipqualityscore',
            'ip_quality_min_score' => '75',
            'ip_quality_block_vpn' => '1',
            'ip_quality_block_tor' => '1',
            'ip_quality_cache_hours' => '24'
        ];
        
        foreach ($defaults as $key => $value) {
            if (!isset($this->settings[$key])) {
                $this->settings[$key] = $value;
            }
        }
    }
    
    /**
     * التحقق من تفعيل فحص جودة IP
     */
    public function isEnabled() {
        return $this->settings['ip_quality_enabled'] === '1' && 
               !empty($this->settings['ip_quality_api_key']);
    }
    
    /**
     * فحص جودة IP
     */
    public function checkIPQuality($ip_address) {
        if (!$this->isEnabled()) {
            return $this->getDefaultResult($ip_address);
        }
        
        // التحقق من الكاش أولاً
        $cached_result = $this->getCachedResult($ip_address);
        if ($cached_result) {
            return $cached_result;
        }
        
        // فحص جديد من API
        $api_result = $this->fetchFromAPI($ip_address);
        if ($api_result) {
            $this->cacheResult($ip_address, $api_result);
            return $api_result;
        }
        
        return $this->getDefaultResult($ip_address);
    }
    
    /**
     * الحصول على النتيجة المخزنة مؤقتاً
     */
    private function getCachedResult($ip_address) {
        $query = "SELECT * FROM ip_quality_cache 
                  WHERE ip_address = :ip AND expires_at > NOW()";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':ip', $ip_address);
        $stmt->execute();
        
        $cached = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($cached) {
            return $this->formatResult($cached);
        }
        
        return null;
    }
    
    /**
     * جلب البيانات من API
     */
    private function fetchFromAPI($ip_address) {
        $service = $this->settings['ip_quality_service'];
        $api_key = $this->settings['ip_quality_api_key'];
        
        try {
            switch ($service) {
                case 'ipqualityscore':
                    return $this->fetchFromIPQualityScore($ip_address, $api_key);
                case 'proxycheck':
                    return $this->fetchFromProxyCheck($ip_address, $api_key);
                default:
                    return null;
            }
        } catch (Exception $e) {
            logError("خطأ في فحص جودة IP: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * فحص من IPQualityScore
     */
    private function fetchFromIPQualityScore($ip_address, $api_key) {
        $url = "https://ipqualityscore.com/api/json/ip/{$api_key}/{$ip_address}";
        $params = [
            'strictness' => 1,
            'allow_public_access_points' => 'true',
            'fast' => 'true',
            'mobile' => 'true'
        ];
        
        $url .= '?' . http_build_query($params);
        
        $response = $this->makeAPIRequest($url);
        if (!$response) return null;
        
        $data = json_decode($response, true);
        if (!$data || !$data['success']) return null;
        
        return [
            'quality_score' => max(0, 100 - intval($data['fraud_score'] ?? 0)),
            'fraud_score' => intval($data['fraud_score'] ?? 0),
            'country_code' => $data['country_code'] ?? '',
            'country_name' => $data['country'] ?? '',
            'region' => $data['region'] ?? '',
            'city' => $data['city'] ?? '',
            'isp' => $data['ISP'] ?? '',
            'organization' => $data['organization'] ?? '',
            'is_vpn' => $data['vpn'] ?? false,
            'is_proxy' => $data['proxy'] ?? false,
            'is_tor' => $data['tor'] ?? false,
            'is_crawler' => $data['bot_status'] ?? false,
            'is_mobile' => $data['mobile'] ?? false,
            'connection_type' => $data['connection_type'] ?? '',
            'abuse_velocity' => $data['abuse_velocity'] ?? '',
            'timezone' => $data['timezone'] ?? '',
            'api_response' => $data
        ];
    }
    
    /**
     * فحص من ProxyCheck
     */
    private function fetchFromProxyCheck($ip_address, $api_key) {
        $url = "https://proxycheck.io/v2/{$ip_address}";
        $params = [
            'key' => $api_key,
            'vpn' => 1,
            'asn' => 1,
            'node' => 1,
            'time' => 1,
            'inf' => 0,
            'risk' => 1,
            'port' => 1,
            'seen' => 1,
            'days' => 7,
            'tag' => 'cpa-system'
        ];
        
        $url .= '?' . http_build_query($params);
        
        $response = $this->makeAPIRequest($url);
        if (!$response) return null;
        
        $data = json_decode($response, true);
        if (!$data || $data['status'] !== 'ok') return null;
        
        $ip_data = $data[$ip_address] ?? [];
        
        return [
            'quality_score' => max(0, 100 - intval($ip_data['risk'] ?? 0)),
            'fraud_score' => intval($ip_data['risk'] ?? 0),
            'country_code' => $ip_data['country'] ?? '',
            'country_name' => $ip_data['country'] ?? '',
            'region' => $ip_data['region'] ?? '',
            'city' => $ip_data['city'] ?? '',
            'isp' => $ip_data['isp'] ?? '',
            'organization' => $ip_data['organisation'] ?? '',
            'is_vpn' => ($ip_data['proxy'] ?? 'no') === 'yes',
            'is_proxy' => ($ip_data['proxy'] ?? 'no') === 'yes',
            'is_tor' => false, // ProxyCheck doesn't specifically detect Tor
            'is_crawler' => false,
            'is_mobile' => false,
            'connection_type' => $ip_data['type'] ?? '',
            'abuse_velocity' => '',
            'timezone' => '',
            'api_response' => $ip_data
        ];
    }
    
    /**
     * إجراء طلب API
     */
    private function makeAPIRequest($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'CPA-System/1.0');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200 && $response !== false) {
            return $response;
        }
        
        return null;
    }
    
    /**
     * تخزين النتيجة في الكاش
     */
    private function cacheResult($ip_address, $result) {
        try {
            $cache_hours = intval($this->settings['ip_quality_cache_hours']);
            $expires_at = date('Y-m-d H:i:s', strtotime("+{$cache_hours} hours"));
            
            $query = "INSERT INTO ip_quality_cache (
                ip_address, quality_score, fraud_score, country_code, country_name,
                region, city, isp, organization, is_vpn, is_proxy, is_tor,
                is_crawler, is_mobile, connection_type, abuse_velocity, timezone,
                api_response, expires_at
            ) VALUES (
                :ip, :quality_score, :fraud_score, :country_code, :country_name,
                :region, :city, :isp, :organization, :is_vpn, :is_proxy, :is_tor,
                :is_crawler, :is_mobile, :connection_type, :abuse_velocity, :timezone,
                :api_response, :expires_at
            ) ON DUPLICATE KEY UPDATE
                quality_score = VALUES(quality_score),
                fraud_score = VALUES(fraud_score),
                country_code = VALUES(country_code),
                country_name = VALUES(country_name),
                region = VALUES(region),
                city = VALUES(city),
                isp = VALUES(isp),
                organization = VALUES(organization),
                is_vpn = VALUES(is_vpn),
                is_proxy = VALUES(is_proxy),
                is_tor = VALUES(is_tor),
                is_crawler = VALUES(is_crawler),
                is_mobile = VALUES(is_mobile),
                connection_type = VALUES(connection_type),
                abuse_velocity = VALUES(abuse_velocity),
                timezone = VALUES(timezone),
                api_response = VALUES(api_response),
                last_checked = CURRENT_TIMESTAMP,
                expires_at = VALUES(expires_at)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':ip', $ip_address);
            $stmt->bindParam(':quality_score', $result['quality_score']);
            $stmt->bindParam(':fraud_score', $result['fraud_score']);
            $stmt->bindParam(':country_code', $result['country_code']);
            $stmt->bindParam(':country_name', $result['country_name']);
            $stmt->bindParam(':region', $result['region']);
            $stmt->bindParam(':city', $result['city']);
            $stmt->bindParam(':isp', $result['isp']);
            $stmt->bindParam(':organization', $result['organization']);
            $stmt->bindParam(':is_vpn', $result['is_vpn'], PDO::PARAM_BOOL);
            $stmt->bindParam(':is_proxy', $result['is_proxy'], PDO::PARAM_BOOL);
            $stmt->bindParam(':is_tor', $result['is_tor'], PDO::PARAM_BOOL);
            $stmt->bindParam(':is_crawler', $result['is_crawler'], PDO::PARAM_BOOL);
            $stmt->bindParam(':is_mobile', $result['is_mobile'], PDO::PARAM_BOOL);
            $stmt->bindParam(':connection_type', $result['connection_type']);
            $stmt->bindParam(':abuse_velocity', $result['abuse_velocity']);
            $stmt->bindParam(':timezone', $result['timezone']);
            $stmt->bindParam(':api_response', json_encode($result['api_response']));
            $stmt->bindParam(':expires_at', $expires_at);
            
            $stmt->execute();
            
        } catch (PDOException $e) {
            logError("خطأ في تخزين نتيجة فحص IP: " . $e->getMessage());
        }
    }
    
    /**
     * تنسيق النتيجة
     */
    private function formatResult($data) {
        return [
            'quality_score' => intval($data['quality_score']),
            'fraud_score' => intval($data['fraud_score']),
            'country_code' => $data['country_code'],
            'country_name' => $data['country_name'],
            'region' => $data['region'],
            'city' => $data['city'],
            'isp' => $data['isp'],
            'organization' => $data['organization'],
            'is_vpn' => (bool)$data['is_vpn'],
            'is_proxy' => (bool)$data['is_proxy'],
            'is_tor' => (bool)$data['is_tor'],
            'is_crawler' => (bool)$data['is_crawler'],
            'is_mobile' => (bool)$data['is_mobile'],
            'connection_type' => $data['connection_type'],
            'abuse_velocity' => $data['abuse_velocity'],
            'timezone' => $data['timezone'],
            'last_checked' => $data['last_checked'],
            'is_cached' => true
        ];
    }
    
    /**
     * النتيجة الافتراضية عند عدم توفر البيانات
     */
    private function getDefaultResult($ip_address) {
        return [
            'quality_score' => 50,
            'fraud_score' => 50,
            'country_code' => '',
            'country_name' => 'غير معروف',
            'region' => '',
            'city' => '',
            'isp' => '',
            'organization' => '',
            'is_vpn' => false,
            'is_proxy' => false,
            'is_tor' => false,
            'is_crawler' => false,
            'is_mobile' => false,
            'connection_type' => '',
            'abuse_velocity' => '',
            'timezone' => '',
            'last_checked' => date('Y-m-d H:i:s'),
            'is_cached' => false,
            'is_default' => true
        ];
    }
    
    /**
     * التحقق من جودة IP حسب الإعدادات
     */
    public function isIPAllowed($ip_address) {
        if (!$this->isEnabled()) {
            return true;
        }
        
        $result = $this->checkIPQuality($ip_address);
        
        // فحص الحد الأدنى للدرجة
        $min_score = intval($this->settings['ip_quality_min_score']);
        if ($result['quality_score'] < $min_score) {
            return false;
        }
        
        // فحص VPN
        if ($this->settings['ip_quality_block_vpn'] === '1' && $result['is_vpn']) {
            return false;
        }
        
        // فحص Tor
        if ($this->settings['ip_quality_block_tor'] === '1' && $result['is_tor']) {
            return false;
        }
        
        return true;
    }
    
    /**
     * تنظيف الكاش المنتهي الصلاحية
     */
    public function cleanExpiredCache() {
        try {
            $query = "DELETE FROM ip_quality_cache WHERE expires_at <= NOW()";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->rowCount();
            
        } catch (PDOException $e) {
            logError("خطأ في تنظيف كاش جودة IP: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * الحصول على إحصائيات الكاش
     */
    public function getCacheStats() {
        try {
            $stats_query = "SELECT 
                COUNT(*) as total_cached,
                COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active_cached,
                AVG(quality_score) as avg_quality_score,
                COUNT(CASE WHEN is_vpn = 1 THEN 1 END) as vpn_count,
                COUNT(CASE WHEN is_proxy = 1 THEN 1 END) as proxy_count,
                COUNT(CASE WHEN is_tor = 1 THEN 1 END) as tor_count
                FROM ip_quality_cache";
            
            $stmt = $this->db->prepare($stats_query);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            logError("خطأ في إحصائيات كاش جودة IP: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * تحديث الإعدادات
     */
    public function updateSettings($new_settings) {
        try {
            foreach ($new_settings as $key => $value) {
                if (strpos($key, 'ip_quality_') === 0) {
                    $update_query = "UPDATE settings SET setting_value = :value 
                                    WHERE setting_key = :key";
                    $update_stmt = $this->db->prepare($update_query);
                    $update_stmt->bindParam(':value', $value);
                    $update_stmt->bindParam(':key', $key);
                    $update_stmt->execute();
                }
            }
            
            // إعادة تحميل الإعدادات
            $this->loadSettings();
            return true;
            
        } catch (PDOException $e) {
            logError("خطأ في تحديث إعدادات جودة IP: " . $e->getMessage());
            return false;
        }
    }
}
?>
