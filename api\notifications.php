<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

$database = new Database();
$notificationManager = new NotificationManager($database);
$user_id = $_SESSION['user_id'];

// تحديد نوع الطلب
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

if ($method === 'GET') {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_notifications':
            $limit = intval($_GET['limit'] ?? 10);
            $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
            
            $notifications = $notificationManager->getUserNotifications($user_id, $limit, $unread_only);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications
            ]);
            break;
            
        case 'get_unread_count':
            $count = $notificationManager->getUnreadCount($user_id);
            
            echo json_encode([
                'success' => true,
                'count' => $count
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'إجراء غير صحيح']);
    }
    
} elseif ($method === 'POST') {
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'mark_as_read':
            $notification_id = intval($input['notification_id'] ?? 0);
            
            if ($notification_id > 0) {
                $success = $notificationManager->markAsRead($user_id, $notification_id);
                
                echo json_encode([
                    'success' => $success,
                    'message' => $success ? 'تم تحديد الإشعار كمقروء' : 'فشل في تحديث الإشعار'
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'معرف الإشعار مطلوب']);
            }
            break;
            
        case 'dismiss':
            $notification_id = intval($input['notification_id'] ?? 0);
            
            if ($notification_id > 0) {
                $success = $notificationManager->dismissNotification($user_id, $notification_id);
                
                echo json_encode([
                    'success' => $success,
                    'message' => $success ? 'تم إخفاء الإشعار' : 'فشل في إخفاء الإشعار'
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'معرف الإشعار مطلوب']);
            }
            break;
            
        case 'mark_all_as_read':
            try {
                $db = $database->getConnection();
                
                // تحديد جميع الإشعارات غير المقروءة كمقروءة
                $query = "UPDATE user_notifications 
                          SET is_read = 1, read_at = NOW() 
                          WHERE user_id = :user_id AND is_read = 0";
                
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $success = $stmt->execute();
                
                echo json_encode([
                    'success' => $success,
                    'message' => $success ? 'تم تحديد جميع الإشعارات كمقروءة' : 'فشل في تحديث الإشعارات'
                ]);
                
            } catch (PDOException $e) {
                logError("خطأ في تحديد جميع الإشعارات كمقروءة: " . $e->getMessage());
                http_response_code(500);
                echo json_encode(['error' => 'خطأ في الخادم']);
            }
            break;
            
        case 'get_new_notifications':
            // للتحقق من الإشعارات الجديدة (للاستخدام مع AJAX polling)
            $last_check = $input['last_check'] ?? date('Y-m-d H:i:s', strtotime('-1 minute'));
            
            try {
                $db = $database->getConnection();
                
                $query = "SELECT n.*, un.is_read, un.is_dismissed
                          FROM notifications n
                          INNER JOIN user_notifications un ON n.id = un.notification_id
                          WHERE un.user_id = :user_id 
                          AND n.is_active = 1
                          AND n.created_at > :last_check
                          AND (n.start_date IS NULL OR n.start_date <= NOW())
                          AND (n.end_date IS NULL OR n.end_date >= NOW())
                          ORDER BY n.created_at DESC";
                
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':last_check', $last_check);
                $stmt->execute();
                
                $new_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'notifications' => $new_notifications,
                    'count' => count($new_notifications)
                ]);
                
            } catch (PDOException $e) {
                logError("خطأ في جلب الإشعارات الجديدة: " . $e->getMessage());
                http_response_code(500);
                echo json_encode(['error' => 'خطأ في الخادم']);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'إجراء غير صحيح']);
    }
    
} else {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مدعومة']);
}
?>
