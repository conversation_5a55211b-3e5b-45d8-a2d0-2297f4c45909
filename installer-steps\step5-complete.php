<div class="card border-success">
    <div class="card-header bg-success text-white text-center">
        <h3><i class="fas fa-check-circle me-2"></i>تم التثبيت بنجاح!</h3>
    </div>
    <div class="card-body">
        <div class="text-center mb-4">
            <div class="success-animation mb-3">
                <i class="fas fa-rocket fa-4x text-success"></i>
            </div>
            <h4 class="text-success">🎉 مبروك! تم تثبيت النظام بنجاح</h4>
            <p class="text-muted">نظام CPA Marketing جاهز للاستخدام الآن</p>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                        <h6>معلومات المدير</h6>
                        <p class="mb-1"><strong>اسم المستخدم:</strong> <?php echo $_SESSION['admin_username'] ?? 'admin'; ?></p>
                        <p class="mb-1"><strong>البريد:</strong> <?php echo $_SESSION['admin_email'] ?? '<EMAIL>'; ?></p>
                        <p class="mb-0"><strong>الدور:</strong> مدير النظام</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-cog fa-2x text-info mb-2"></i>
                        <h6>معلومات النظام</h6>
                        <p class="mb-1"><strong>الإصدار:</strong> v2.0</p>
                        <p class="mb-1"><strong>قاعدة البيانات:</strong> متصلة</p>
                        <p class="mb-0"><strong>الحالة:</strong> جاهز للعمل</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>الخطوات التالية:</h6>
            <ol class="mb-0">
                <li>سجل دخول كمدير وتصفح لوحة الإدارة</li>
                <li>أضف أول عرض CPA للنظام</li>
                <li>كوّن إعدادات الحماية والإشعارات</li>
                <li>اختبر النظام مع مستخدم تجريبي</li>
                <li>ابدأ في دعوة المستخدمين الحقيقيين</li>
            </ol>
        </div>
        
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذيرات أمنية مهمة:</h6>
            <ul class="mb-0">
                <li>احذف ملف <code>installer.php</code> من الخادم فوراً</li>
                <li>احذف مجلد <code>installer-steps/</code> بالكامل</li>
                <li>غيّر كلمة مرور المدير إلى كلمة أكثر تعقيداً</li>
                <li>فعّل SSL Certificate للموقع</li>
                <li>راجع إعدادات الأمان في لوحة الإدارة</li>
            </ul>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="d-grid">
                    <a href="index.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                    </a>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="d-grid">
                    <a href="auth/login.php" class="btn btn-success btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="d-grid">
                    <a href="admin/dashboard.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-tachometer-alt me-2"></i>لوحة الإدارة
                    </a>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-book me-2"></i>الوثائق والأدلة</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><a href="README-v2.md" target="_blank"><i class="fas fa-file-alt me-2"></i>دليل النظام</a></li>
                            <li><a href="INSTALLATION-GUIDE.md" target="_blank"><i class="fas fa-wrench me-2"></i>دليل التثبيت</a></li>
                            <li><a href="system-check.php" target="_blank"><i class="fas fa-stethoscope me-2"></i>فحص النظام</a></li>
                            <li><a href="admin/settings/system-report.php"><i class="fas fa-chart-line me-2"></i>تقرير النظام</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-rocket me-2"></i>الميزات الجاهزة</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>نظام إشعارات متقدم</li>
                            <li><i class="fas fa-check text-success me-2"></i>حماية IP ذكية</li>
                            <li><i class="fas fa-check text-success me-2"></i>Offerwall متكامل</li>
                            <li><i class="fas fa-check text-success me-2"></i>أدوات البريد المؤقت</li>
                            <li><i class="fas fa-check text-success me-2"></i>تتبع التحويلات</li>
                            <li><i class="fas fa-check text-success me-2"></i>لوحات إدارة منفصلة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <hr>
        
        <div class="text-center">
            <h6 class="text-muted">شكراً لاختيارك نظام CPA Marketing المتكامل</h6>
            <p class="text-muted mb-0">
                الإصدار 2.0 | تم التثبيت في: <?php echo date('Y-m-d H:i:s'); ?>
            </p>
        </div>
    </div>
</div>

<style>
.success-animation {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}
</style>

<script>
// إضافة تأثيرات بصرية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الكونفيتي
    createConfetti();
    
    // تحديث الوقت كل ثانية
    updateTime();
    setInterval(updateTime, 1000);
});

function createConfetti() {
    // إنشاء تأثير الكونفيتي البسيط
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'];
    
    for (let i = 0; i < 50; i++) {
        setTimeout(() => {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = Math.random() * 100 + 'vw';
            confetti.style.top = '-10px';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.borderRadius = '50%';
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '9999';
            confetti.style.animation = 'fall 3s linear forwards';
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.remove();
            }, 3000);
        }, i * 100);
    }
}

function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA');
    // يمكن إضافة عرض الوقت في مكان ما إذا أردت
}

// إضافة CSS للكونفيتي
const style = document.createElement('style');
style.textContent = `
    @keyframes fall {
        to {
            transform: translateY(100vh) rotate(360deg);
        }
    }
`;
document.head.appendChild(style);
</script>
