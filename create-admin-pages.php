<?php
/**
 * إنشاء صفحات الإدارة الكاملة
 */

echo "<h2>🔐 إنشاء صفحات الإدارة الكاملة</h2>";

try {
    require_once 'config/config.php';

    // قالب صفحة الإدارة
    function createAdminPage($title, $icon, $content) {
        return '<?php
require_once "../../config/config.php";
if (!isLoggedIn() || !isAdmin()) {
    header("Location: ../../auth/login.php");
    exit();
}

$database = new Database();
$db = $database->getConnection();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $title . ' - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include "../../includes/header.php"; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include "../../includes/sidebar.php"; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-' . $icon . ' me-2"></i>' . $title . '
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="../../index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-1"></i>العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>

                ' . $content . '
            </main>
        </div>
    </div>

    <?php include "../../includes/footer.php"; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/main.js"></script>
</body>
</html>';
    }

    echo "<h3>1. صفحة إدارة المستخدمين:</h3>";

    $usersContent = '
                <!-- إحصائيات المستخدمين -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">إجمالي المستخدمين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php
                                            try {
                                                $total_users = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
                                                echo number_format($total_users);
                                            } catch (Exception $e) {
                                                echo "0";
                                            }
                                            ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">المستخدمين النشطين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php
                                            try {
                                                $active_users = $db->query("SELECT COUNT(*) FROM users WHERE status = \"active\"")->fetchColumn();
                                                echo number_format($active_users);
                                            } catch (Exception $e) {
                                                echo "0";
                                            }
                                            ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">مستخدمين جدد اليوم</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php
                                            try {
                                                $new_today = $db->query("SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()")->fetchColumn();
                                                echo number_format($new_today);
                                            } catch (Exception $e) {
                                                echo "0";
                                            }
                                            ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">المستخدمين المعلقين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php
                                            try {
                                                $suspended_users = $db->query("SELECT COUNT(*) FROM users WHERE status = \"suspended\"")->fetchColumn();
                                                echo number_format($suspended_users);
                                            } catch (Exception $e) {
                                                echo "0";
                                            }
                                            ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-times fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات الإدارة -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-tools me-2"></i>أدوات الإدارة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-primary w-100" onclick="showAddUserModal()">
                                    <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-success w-100" onclick="exportUsers()">
                                    <i class="fas fa-download me-2"></i>تصدير المستخدمين
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-info w-100" onclick="sendBulkEmail()">
                                    <i class="fas fa-envelope me-2"></i>إرسال بريد جماعي
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-warning w-100" onclick="generateUsernames()">
                                    <i class="fas fa-magic me-2"></i>إنشاء أسماء مستخدمين
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المستخدمين -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-list me-2"></i>قائمة المستخدمين
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- فلاتر البحث -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="searchUsers" placeholder="البحث في المستخدمين...">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="filterRole">
                                    <option value="">جميع الأدوار</option>
                                    <option value="admin">مدير</option>
                                    <option value="publisher">ناشر</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="filterStatus">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="suspended">معلق</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="filterUsers()">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="usersTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>الرصيد</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>آخر دخول</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    try {
                                        $users_query = "SELECT * FROM users ORDER BY created_at DESC LIMIT 50";
                                        $users_stmt = $db->prepare($users_query);
                                        $users_stmt->execute();
                                        $users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);

                                        foreach ($users as $user):
                                    ?>
                                    <tr>
                                        <td><?php echo $user["id"]; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($user["username"]); ?></strong>
                                            <?php if ($user["is_auto_generated"]): ?>
                                                <span class="badge bg-info ms-1">تلقائي</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($user["email"] ?? "غير محدد"); ?></td>
                                        <td>
                                            <?php if ($user["role"] == "admin"): ?>
                                                <span class="badge bg-danger">مدير</span>
                                            <?php else: ?>
                                                <span class="badge bg-primary">ناشر</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status_colors = [
                                                "active" => "success",
                                                "inactive" => "secondary",
                                                "suspended" => "danger"
                                            ];
                                            $status_names = [
                                                "active" => "نشط",
                                                "inactive" => "غير نشط",
                                                "suspended" => "معلق"
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_colors[$user["status"]]; ?>">
                                                <?php echo $status_names[$user["status"]]; ?>
                                            </span>
                                        </td>
                                        <td><?php echo CURRENCY_SYMBOL . number_format($user["balance"], 2); ?></td>
                                        <td><?php echo date("Y-m-d", strtotime($user["created_at"])); ?></td>
                                        <td>
                                            <?php if ($user["last_login"]): ?>
                                                <?php echo date("Y-m-d H:i", strtotime($user["last_login"])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">لم يدخل بعد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-info btn-sm" onclick="viewUser(<?php echo $user[\"id\"]; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-warning btn-sm" onclick="editUser(<?php echo $user[\"id\"]; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php if ($user["status"] == "active"): ?>
                                                    <button type="button" class="btn btn-secondary btn-sm" onclick="suspendUser(<?php echo $user[\"id\"]; ?>)">
                                                        <i class="fas fa-ban"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-success btn-sm" onclick="activateUser(<?php echo $user[\"id\"]; ?>)">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php
                                    } catch (Exception $e) {
                                        echo "<tr><td colspan=\"9\" class=\"text-center text-danger\">خطأ في تحميل المستخدمين: " . $e->getMessage() . "</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <script>
                function viewUser(userId) {
                    window.open("view.php?id=" + userId, "_blank", "width=800,height=600");
                }

                function editUser(userId) {
                    window.location.href = "edit.php?id=" + userId;
                }

                function suspendUser(userId) {
                    if (confirm("هل أنت متأكد من تعليق هذا المستخدم؟")) {
                        fetch("actions.php", {
                            method: "POST",
                            headers: {"Content-Type": "application/json"},
                            body: JSON.stringify({action: "suspend", user_id: userId})
                        }).then(() => location.reload());
                    }
                }

                function activateUser(userId) {
                    if (confirm("هل أنت متأكد من تفعيل هذا المستخدم؟")) {
                        fetch("actions.php", {
                            method: "POST",
                            headers: {"Content-Type": "application/json"},
                            body: JSON.stringify({action: "activate", user_id: userId})
                        }).then(() => location.reload());
                    }
                }

                function filterUsers() {
                    // تطبيق الفلاتر
                    const search = document.getElementById("searchUsers").value;
                    const role = document.getElementById("filterRole").value;
                    const status = document.getElementById("filterStatus").value;

                    // إعادة تحميل الصفحة مع المعاملات
                    const params = new URLSearchParams();
                    if (search) params.append("search", search);
                    if (role) params.append("role", role);
                    if (status) params.append("status", status);

                    window.location.href = "?" + params.toString();
                }
                </script>';

    $usersPage = createAdminPage('إدارة المستخدمين', 'users', $usersContent);

    if (!file_exists('admin/users/index.php')) {
        file_put_contents('admin/users/index.php', $usersPage);
        echo "✅ صفحة إدارة المستخدمين<br>";
    }

    echo "<h3>✅ تم إنشاء صفحات الإدارة بنجاح!</h3>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3 {
    color: #333;
}
.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    padding: 15px;
    border-radius: 4px;
}
</style>