-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS cpa_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE cpa_system;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role ENUM('admin', 'publisher') DEFAULT 'publisher',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    balance DECIMAL(10,2) DEFAULT 0.00,
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    phone VARCHAR(20),
    country VARCHAR(50),
    payment_method ENUM('paypal', 'bank_transfer', 'payoneer') DEFAULT 'paypal',
    payment_details TEXT,
    api_key VARCHAR(64) UNIQUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الشبكات (CPALead, MaxBounty, إلخ)
CREATE TABLE networks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    website VARCHAR(255),
    api_endpoint VARCHAR(255),
    api_key VARCHAR(255),
    api_secret VARCHAR(255),
    postback_url VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    commission_rate DECIMAL(5,2) DEFAULT 0.00,
    payment_terms TEXT,
    contact_info TEXT,
    logo VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول العروض
CREATE TABLE offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    network_id INT NOT NULL,
    external_id VARCHAR(100),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    preview_url VARCHAR(500),
    payout DECIMAL(10,2) NOT NULL,
    type ENUM('cpa', 'cpl', 'cps', 'cpi', 'cpc') DEFAULT 'cpa',
    category VARCHAR(100),
    countries TEXT,
    allowed_traffic TEXT,
    restrictions TEXT,
    status ENUM('active', 'inactive', 'paused') DEFAULT 'active',
    cap_daily INT DEFAULT 0,
    cap_monthly INT DEFAULT 0,
    conversion_flow TEXT,
    tracking_url VARCHAR(500),
    image VARCHAR(255),
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (network_id) REFERENCES networks(id) ON DELETE CASCADE
);

-- جدول روابط التتبع
CREATE TABLE tracking_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    tracking_id VARCHAR(32) UNIQUE NOT NULL,
    original_url VARCHAR(500) NOT NULL,
    tracking_url VARCHAR(500) NOT NULL,
    sub_id VARCHAR(100),
    source VARCHAR(100),
    campaign VARCHAR(100),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE
);

-- جدول النقرات
CREATE TABLE clicks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_id VARCHAR(32) NOT NULL,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referer VARCHAR(500),
    country VARCHAR(50),
    city VARCHAR(100),
    device_type ENUM('desktop', 'mobile', 'tablet'),
    browser VARCHAR(50),
    os VARCHAR(50),
    sub_id VARCHAR(100),
    source VARCHAR(100),
    campaign VARCHAR(100),
    is_unique BOOLEAN DEFAULT TRUE,
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    INDEX idx_tracking_id (tracking_id),
    INDEX idx_user_offer (user_id, offer_id),
    INDEX idx_clicked_at (clicked_at)
);

-- جدول التحويلات
CREATE TABLE conversions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_id VARCHAR(32) NOT NULL,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    click_id INT,
    transaction_id VARCHAR(100),
    payout DECIMAL(10,2) NOT NULL,
    revenue DECIMAL(10,2),
    status ENUM('pending', 'approved', 'rejected', 'reversed') DEFAULT 'pending',
    ip_address VARCHAR(45),
    country VARCHAR(50),
    sub_id VARCHAR(100),
    source VARCHAR(100),
    campaign VARCHAR(100),
    conversion_data TEXT,
    approved_at TIMESTAMP NULL,
    converted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (click_id) REFERENCES clicks(id) ON DELETE SET NULL,
    INDEX idx_tracking_id (tracking_id),
    INDEX idx_user_offer (user_id, offer_id),
    INDEX idx_status (status),
    INDEX idx_converted_at (converted_at)
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    method ENUM('paypal', 'bank_transfer', 'payoneer') NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    transaction_id VARCHAR(100),
    payment_details TEXT,
    notes TEXT,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_requested_at (requested_at)
);

-- جدول الإحصائيات اليومية
CREATE TABLE daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    date DATE NOT NULL,
    clicks INT DEFAULT 0,
    unique_clicks INT DEFAULT 0,
    conversions INT DEFAULT 0,
    earnings DECIMAL(10,2) DEFAULT 0.00,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_offer_date (user_id, offer_id, date),
    INDEX idx_date (date),
    INDEX idx_user_date (user_id, date)
);

-- جدول سجل النشاطات
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_action (user_id, action),
    INDEX idx_created_at (created_at)
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج البيانات الأساسية
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', 'admin');

INSERT INTO settings (setting_key, setting_value, description, type) VALUES
('site_name', 'CPA Marketing System', 'اسم الموقع', 'string'),
('site_url', 'http://localhost/cpa', 'رابط الموقع', 'string'),
('min_payout', '50', 'الحد الأدنى للسحب', 'number'),
('commission_rate', '80', 'نسبة العمولة للناشرين (%)', 'number'),
('email_notifications', '1', 'تفعيل الإشعارات بالبريد الإلكتروني', 'boolean'),
('auto_approve_conversions', '0', 'الموافقة التلقائية على التحويلات', 'boolean');

INSERT INTO networks (name, description, website, status) VALUES
('CPALead', 'شبكة CPA رائدة مع عروض عالية الجودة', 'https://cpalead.com', 'active'),
('MaxBounty', 'شبكة CPA موثوقة مع مدفوعات سريعة', 'https://maxbounty.com', 'active'),
('PeerFly', 'شبكة CPA مع عروض متنوعة', 'https://peerfly.com', 'active'),
('ClickDealer', 'شبكة CPA عالمية', 'https://clickdealer.com', 'active');
