-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS cpa_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE cpa_system;

-- جدول المستخدمين (محدث للنظام المبسط)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) DEFAULT NULL,
    first_name VARCHAR(50) DEFAULT NULL,
    last_name VARCHAR(50) DEFAULT NULL,
    role ENUM('admin', 'publisher') DEFAULT 'publisher',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    balance DECIMAL(10,2) DEFAULT 0.00,
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    phone VARCHAR(20),
    country VARCHAR(50),
    payment_method ENUM('paypal', 'bank_transfer', 'payoneer') DEFAULT 'paypal',
    payment_details TEXT,
    api_key VARCHAR(64) UNIQUE,
    is_auto_generated BOOLEAN DEFAULT FALSE,
    login_count INT DEFAULT 0,
    ip_address VARCHAR(45),
    user_agent TEXT,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_is_auto_generated (is_auto_generated)
);

-- جدول الشبكات (CPALead, MaxBounty, إلخ)
CREATE TABLE networks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    website VARCHAR(255),
    api_endpoint VARCHAR(255),
    api_key VARCHAR(255),
    api_secret VARCHAR(255),
    postback_url VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    commission_rate DECIMAL(5,2) DEFAULT 0.00,
    payment_terms TEXT,
    contact_info TEXT,
    logo VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول العروض
CREATE TABLE offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    network_id INT NOT NULL,
    external_id VARCHAR(100),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    preview_url VARCHAR(500),
    payout DECIMAL(10,2) NOT NULL,
    type ENUM('cpa', 'cpl', 'cps', 'cpi', 'cpc') DEFAULT 'cpa',
    category VARCHAR(100),
    countries TEXT,
    allowed_traffic TEXT,
    restrictions TEXT,
    status ENUM('active', 'inactive', 'paused') DEFAULT 'active',
    cap_daily INT DEFAULT 0,
    cap_monthly INT DEFAULT 0,
    conversion_flow TEXT,
    tracking_url VARCHAR(500),
    external_offer_url TEXT, -- رابط العرض الخارجي
    external_image_url TEXT, -- رابط صورة خارجي
    image VARCHAR(255),
    is_external BOOLEAN DEFAULT FALSE, -- هل العرض خارجي
    external_tracking BOOLEAN DEFAULT FALSE, -- هل يستخدم تتبع خارجي
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (network_id) REFERENCES networks(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_is_external (is_external),
    INDEX idx_created_at (created_at)
);

-- جدول روابط التتبع
CREATE TABLE tracking_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    tracking_id VARCHAR(32) UNIQUE NOT NULL,
    original_url VARCHAR(500) NOT NULL,
    tracking_url VARCHAR(500) NOT NULL,
    sub_id VARCHAR(100),
    source VARCHAR(100),
    campaign VARCHAR(100),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE
);

-- جدول النقرات (محدث لدعم CPALead)
CREATE TABLE clicks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_id VARCHAR(32) NOT NULL,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referer VARCHAR(500),
    country VARCHAR(50),
    city VARCHAR(100),
    device_type ENUM('desktop', 'mobile', 'tablet'),
    browser VARCHAR(50),
    os VARCHAR(50),
    sub_id VARCHAR(100),
    sub_id2 VARCHAR(100),
    sub_id3 VARCHAR(100),
    source VARCHAR(100),
    campaign VARCHAR(100),
    idfa VARCHAR(100), -- Apple IDFA
    gaid VARCHAR(100), -- Google Advertising ID
    is_unique BOOLEAN DEFAULT TRUE,
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    INDEX idx_tracking_id (tracking_id),
    INDEX idx_user_offer (user_id, offer_id),
    INDEX idx_clicked_at (clicked_at)
);

-- جدول التحويلات (محدث لدعم CPALead)
CREATE TABLE conversions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_id VARCHAR(32) NOT NULL,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    click_id INT,
    transaction_id VARCHAR(100),
    lead_id VARCHAR(100), -- CPALead Lead ID
    campaign_id VARCHAR(100), -- CPALead Campaign ID
    campaign_name VARCHAR(255), -- CPALead Campaign Name
    gateway_id VARCHAR(100), -- CPALead Gateway ID
    payout DECIMAL(10,2) NOT NULL,
    revenue DECIMAL(10,2),
    virtual_currency DECIMAL(10,2) DEFAULT 0,
    status ENUM('pending', 'approved', 'rejected', 'reversed') DEFAULT 'pending',
    ip_address VARCHAR(45),
    country VARCHAR(50),
    country_iso VARCHAR(2), -- ISO Country Code
    sub_id VARCHAR(100),
    sub_id2 VARCHAR(100),
    sub_id3 VARCHAR(100),
    source VARCHAR(100),
    campaign VARCHAR(100),
    idfa VARCHAR(100), -- Apple IDFA
    gaid VARCHAR(100), -- Google Advertising ID
    postback_password VARCHAR(255), -- Postback Password
    conversion_data TEXT,
    approved_at TIMESTAMP NULL,
    converted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (click_id) REFERENCES clicks(id) ON DELETE SET NULL,
    INDEX idx_tracking_id (tracking_id),
    INDEX idx_user_offer (user_id, offer_id),
    INDEX idx_status (status),
    INDEX idx_converted_at (converted_at),
    INDEX idx_lead_id (lead_id),
    INDEX idx_transaction_id (transaction_id)
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    method ENUM('paypal', 'bank_transfer', 'payoneer') NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    transaction_id VARCHAR(100),
    payment_details TEXT,
    notes TEXT,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_requested_at (requested_at)
);

-- جدول الإحصائيات اليومية
CREATE TABLE daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    date DATE NOT NULL,
    clicks INT DEFAULT 0,
    unique_clicks INT DEFAULT 0,
    conversions INT DEFAULT 0,
    earnings DECIMAL(10,2) DEFAULT 0.00,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_offer_date (user_id, offer_id, date),
    INDEX idx_date (date),
    INDEX idx_user_date (user_id, date)
);

-- جدول سجل النشاطات
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_action (user_id, action),
    INDEX idx_created_at (created_at)
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج البيانات الأساسية
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', 'admin');

INSERT INTO settings (setting_key, setting_value, description, type) VALUES
('site_name', 'CPA Marketing System', 'اسم الموقع', 'string'),
('site_url', 'http://localhost/cpa', 'رابط الموقع', 'string'),
('min_payout', '50', 'الحد الأدنى للسحب', 'number'),
('commission_rate', '80', 'نسبة العمولة للناشرين (%)', 'number'),
('email_notifications', '1', 'تفعيل الإشعارات بالبريد الإلكتروني', 'boolean'),
('auto_approve_conversions', '0', 'الموافقة التلقائية على التحويلات', 'boolean'),
('ip_protection_enabled', '1', 'تفعيل حماية IP للعروض', 'boolean'),
('ip_protection_days', '7', 'عدد أيام حماية IP (العرض يختفي لهذه المدة)', 'number'),
('ip_protection_per_offer', '1', 'حماية IP لكل عرض منفصل', 'boolean'),
('ip_protection_global', '0', 'حماية IP عامة لجميع العروض', 'boolean'),
('ip_protection_whitelist', '', 'IPs مستثناة من الحماية (مفصولة بفاصلة)', 'string'),
('ip_protection_reset_hour', '0', 'ساعة إعادة تعيين الحماية يومياً (0-23)', 'number'),
('ip_quality_enabled', '1', 'تفعيل فحص جودة IP', 'boolean'),
('ip_quality_api_key', '', 'مفتاح API لخدمة فحص جودة IP', 'string'),
('ip_quality_service', 'ipqualityscore', 'خدمة فحص جودة IP (ipqualityscore, proxycheck)', 'string'),
('ip_quality_min_score', '75', 'الحد الأدنى لدرجة جودة IP (0-100)', 'number'),
('ip_quality_block_vpn', '1', 'حظر VPN والبروكسي', 'boolean'),
('ip_quality_block_tor', '1', 'حظر شبكة Tor', 'boolean'),
('ip_quality_cache_hours', '24', 'مدة تخزين نتائج فحص IP (ساعات)', 'number');

INSERT INTO networks (name, description, website, status) VALUES
('CPALead', 'شبكة CPA رائدة مع عروض عالية الجودة', 'https://cpalead.com', 'active'),
('MaxBounty', 'شبكة CPA موثوقة مع مدفوعات سريعة', 'https://maxbounty.com', 'active'),
('PeerFly', 'شبكة CPA مع عروض متنوعة', 'https://peerfly.com', 'active'),
('ClickDealer', 'شبكة CPA عالمية', 'https://clickdealer.com', 'active');

-- جدول أحداث Offerwall
CREATE TABLE offerwall_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_name VARCHAR(50) NOT NULL,
    subid VARCHAR(100),
    event_data TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_event (user_id, event_name),
    INDEX idx_created_at (created_at)
);

-- جدول إحصائيات Offerwall
CREATE TABLE offerwall_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    date DATE NOT NULL,
    views INT DEFAULT 0,
    completions INT DEFAULT 0,
    earnings DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, date),
    INDEX idx_date (date)
);

-- جدول حماية IP للعروض
CREATE TABLE ip_protection (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    offer_id INT,
    user_id INT,
    protection_type ENUM('offer', 'global') DEFAULT 'offer',
    blocked_until DATETIME NOT NULL,
    click_count INT DEFAULT 1,
    last_click TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_ip_offer (ip_address, offer_id),
    INDEX idx_ip_blocked_until (ip_address, blocked_until),
    INDEX idx_blocked_until (blocked_until),
    INDEX idx_created_at (created_at)
);

-- جدول فحص جودة IP
CREATE TABLE ip_quality_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL UNIQUE,
    quality_score INT DEFAULT 0,
    fraud_score INT DEFAULT 0,
    country_code VARCHAR(2),
    country_name VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    isp VARCHAR(255),
    organization VARCHAR(255),
    is_vpn BOOLEAN DEFAULT FALSE,
    is_proxy BOOLEAN DEFAULT FALSE,
    is_tor BOOLEAN DEFAULT FALSE,
    is_crawler BOOLEAN DEFAULT FALSE,
    is_mobile BOOLEAN DEFAULT FALSE,
    connection_type VARCHAR(50),
    abuse_velocity VARCHAR(20),
    timezone VARCHAR(50),
    api_response JSON,
    last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_ip_address (ip_address),
    INDEX idx_expires_at (expires_at),
    INDEX idx_quality_score (quality_score),
    INDEX idx_last_checked (last_checked)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'danger', 'offer', 'system') DEFAULT 'info',
    target_audience ENUM('all', 'active', 'inactive', 'new', 'custom') DEFAULT 'all',
    target_users JSON,
    offer_id INT,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    is_active BOOLEAN DEFAULT TRUE,
    show_popup BOOLEAN DEFAULT FALSE,
    show_banner BOOLEAN DEFAULT TRUE,
    show_sidebar BOOLEAN DEFAULT TRUE,
    auto_hide BOOLEAN DEFAULT TRUE,
    hide_after_seconds INT DEFAULT 10,
    start_date DATETIME,
    end_date DATETIME,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_type (type),
    INDEX idx_target_audience (target_audience),
    INDEX idx_is_active (is_active),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date)
);

-- جدول حالة قراءة الإشعارات للمستخدمين
CREATE TABLE user_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notification_id INT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    dismissed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_notification (user_id, notification_id),
    INDEX idx_user_id (user_id),
    INDEX idx_notification_id (notification_id),
    INDEX idx_is_read (is_read)
);

-- جدول إعدادات الإشعارات
CREATE TABLE notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    setting_type ENUM('boolean', 'integer', 'string', 'json') DEFAULT 'string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
);

-- إدراج إعدادات الإشعارات الافتراضية
INSERT INTO notification_settings (setting_key, setting_value, description, setting_type) VALUES
('notifications_enabled', '1', 'تفعيل نظام الإشعارات', 'boolean'),
('popup_notifications', '1', 'إظهار الإشعارات المنبثقة', 'boolean'),
('banner_notifications', '1', 'إظهار إشعارات البانر', 'boolean'),
('sidebar_notifications', '1', 'إظهار الإشعارات في الشريط الجانبي', 'boolean'),
('auto_hide_notifications', '1', 'إخفاء الإشعارات تلقائياً', 'boolean'),
('notification_hide_delay', '10', 'مدة إخفاء الإشعارات (ثواني)', 'integer'),
('max_notifications_display', '5', 'عدد الإشعارات المعروضة', 'integer'),
('notification_sound', '1', 'تشغيل صوت الإشعارات', 'boolean'),
('offer_notifications', '1', 'إشعارات العروض الجديدة', 'boolean'),
('system_notifications', '1', 'إشعارات النظام', 'boolean');
