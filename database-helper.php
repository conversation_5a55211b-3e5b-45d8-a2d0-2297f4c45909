<?php
/**
 * مساعد إعداد قاعدة البيانات للاستضافة المجانية
 */

// بدء الجلسة
session_start();

// معلومات قاعدة البيانات المعروفة للاستضافة المجانية
$known_configs = [
    'infinityfree' => [
        'host' => 'sql303.infinityfree.com',
        'username_pattern' => 'if0_XXXXXXXX',
        'database_pattern' => 'if0_XXXXXXXX_DBNAME',
        'port' => 3306
    ],
    '000webhost' => [
        'host' => 'localhost',
        'username_pattern' => 'id_XXXXXXXX_USERNAME',
        'database_pattern' => 'id_XXXXXXXX_DBNAME',
        'port' => 3306
    ]
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'detect_config') {
        $username = $_POST['username'] ?? '';
        $hosting_type = $_POST['hosting_type'] ?? '';
        
        $response = [];
        
        if (isset($known_configs[$hosting_type])) {
            $config = $known_configs[$hosting_type];
            
            // توليد اقتراحات بناءً على نوع الاستضافة
            if ($hosting_type === 'infinityfree' && preg_match('/^if0_\d+$/', $username)) {
                $response = [
                    'success' => true,
                    'suggestions' => [
                        'host' => $config['host'],
                        'username' => $username,
                        'database' => $username . '_cpa',
                        'notes' => [
                            'استخدم نفس اسم المستخدم للاتصال',
                            'اسم قاعدة البيانات يجب أن يبدأ بـ ' . $username,
                            'تأكد من إنشاء قاعدة البيانات من cPanel أولاً'
                        ]
                    ]
                ];
            } else {
                $response = [
                    'success' => false,
                    'error' => 'تنسيق اسم المستخدم غير صحيح لهذا النوع من الاستضافة'
                ];
            }
        } else {
            $response = [
                'success' => false,
                'error' => 'نوع استضافة غير مدعوم'
            ];
        }
        
        header('Content-Type: application/json');
        echo json_encode($response);
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مساعد إعداد قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-tools me-2"></i>مساعد إعداد قاعدة البيانات</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- InfinityFree -->
                            <div class="col-md-6 mb-4">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5><i class="fas fa-infinity me-2"></i>InfinityFree</h5>
                                    </div>
                                    <div class="card-body">
                                        <h6>خطوات الإعداد:</h6>
                                        <ol class="small">
                                            <li>اذهب إلى cPanel</li>
                                            <li>اختر "MySQL Databases"</li>
                                            <li>أنشئ قاعدة بيانات جديدة</li>
                                            <li>استخدم البيانات التالية:</li>
                                        </ol>
                                        
                                        <div class="bg-light p-3 rounded">
                                            <strong>مثال للبيانات:</strong><br>
                                            <code>Host: sql303.infinityfree.com</code><br>
                                            <code>Username: if0_39395085</code><br>
                                            <code>Database: if0_39395085_cpa</code><br>
                                            <code>Password: [كلمة المرور الخاصة بك]</code>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <input type="text" class="form-control mb-2" id="if_username" 
                                                   placeholder="اسم المستخدم (مثل: if0_39395085)" 
                                                   value="if0_39395085">
                                            <button class="btn btn-primary btn-sm" onclick="generateInfinityFreeConfig()">
                                                <i class="fas fa-magic me-1"></i>توليد الإعدادات
                                            </button>
                                        </div>
                                        
                                        <div id="if-result" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 000webhost -->
                            <div class="col-md-6 mb-4">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5><i class="fas fa-globe me-2"></i>000webhost</h5>
                                    </div>
                                    <div class="card-body">
                                        <h6>خطوات الإعداد:</h6>
                                        <ol class="small">
                                            <li>اذهب إلى Control Panel</li>
                                            <li>اختر "Database"</li>
                                            <li>أنشئ قاعدة بيانات MySQL</li>
                                            <li>استخدم البيانات المعطاة</li>
                                        </ol>
                                        
                                        <div class="bg-light p-3 rounded">
                                            <strong>مثال للبيانات:</strong><br>
                                            <code>Host: localhost</code><br>
                                            <code>Username: id12345_user</code><br>
                                            <code>Database: id12345_cpa</code><br>
                                            <code>Password: [كلمة المرور]</code>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- نصائح عامة -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>نصائح مهمة:</h6>
                            <ul class="mb-0">
                                <li><strong>للاستضافة المجانية:</strong> يجب إنشاء قاعدة البيانات من لوحة التحكم أولاً</li>
                                <li><strong>أسماء قواعد البيانات:</strong> تحتوي عادة على بادئة (prefix) خاصة بحسابك</li>
                                <li><strong>كلمة المرور:</strong> استخدم كلمة مرور قوية ولا تتركها فارغة</li>
                                <li><strong>اختبار الاتصال:</strong> استخدم زر "اختبار الاتصال" للتأكد من صحة البيانات</li>
                            </ul>
                        </div>
                        
                        <!-- أخطاء شائعة -->
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>أخطاء شائعة وحلولها:</h6>
                            <ul class="mb-0">
                                <li><strong>"Access denied":</strong> تحقق من اسم المستخدم وكلمة المرور</li>
                                <li><strong>"Unknown database":</strong> تأكد من إنشاء قاعدة البيانات أولاً</li>
                                <li><strong>"Connection refused":</strong> تحقق من عنوان الخادم (Host)</li>
                                <li><strong>"Too many connections":</strong> انتظر قليلاً وأعد المحاولة</li>
                            </ul>
                        </div>
                        
                        <div class="text-center">
                            <a href="installer.php?step=2" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للتثبيت
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function generateInfinityFreeConfig() {
        const username = document.getElementById('if_username').value;
        const resultDiv = document.getElementById('if-result');
        
        if (!username || !username.startsWith('if0_')) {
            resultDiv.innerHTML = '<div class="alert alert-danger">يرجى إدخال اسم مستخدم صحيح يبدأ بـ if0_</div>';
            return;
        }
        
        const config = {
            host: 'sql303.infinityfree.com',
            username: username,
            database: username + '_cpa',
            notes: [
                'استخدم هذه البيانات في خطوة إعداد قاعدة البيانات',
                'تأكد من إنشاء قاعدة البيانات من cPanel أولاً',
                'اسم قاعدة البيانات يجب أن يبدأ بـ ' + username
            ]
        };
        
        let html = '<div class="alert alert-success">';
        html += '<h6><i class="fas fa-check me-2"></i>الإعدادات المقترحة:</h6>';
        html += '<div class="bg-white p-2 rounded border">';
        html += '<strong>Host:</strong> <code>' + config.host + '</code><br>';
        html += '<strong>Username:</strong> <code>' + config.username + '</code><br>';
        html += '<strong>Database:</strong> <code>' + config.database + '</code><br>';
        html += '<strong>Password:</strong> <code>[كلمة المرور الخاصة بك]</code>';
        html += '</div>';
        html += '<ul class="mt-2 mb-0">';
        config.notes.forEach(note => {
            html += '<li>' + note + '</li>';
        });
        html += '</ul>';
        html += '</div>';
        
        resultDiv.innerHTML = html;
    }
    </script>
</body>
</html>
