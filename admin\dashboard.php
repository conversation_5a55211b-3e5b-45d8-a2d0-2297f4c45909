<?php
require_once '../config/config.php';

// التحقق من صلاحيات الإدارة
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../auth/login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();

// جلب الإحصائيات العامة
$stats_query = "SELECT 
    (SELECT COUNT(*) FROM users WHERE role = 'publisher') as total_publishers,
    (SELECT COUNT(*) FROM offers WHERE status = 'active') as active_offers,
    (SELECT COUNT(*) FROM networks WHERE status = 'active') as active_networks,
    (SELECT COALESCE(SUM(clicks), 0) FROM daily_stats WHERE date >= CURDATE() - INTERVAL 30 DAY) as monthly_clicks,
    (SELECT COALESCE(SUM(conversions), 0) FROM daily_stats WHERE date >= CURDATE() - INTERVAL 30 DAY) as monthly_conversions,
    (SELECT COALESCE(SUM(earnings), 0) FROM daily_stats WHERE date >= CURDATE() - INTERVAL 30 DAY) as monthly_earnings,
    (SELECT COUNT(*) FROM payments WHERE status = 'pending') as pending_payments";

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// جلب أحدث النشاطات
$activities_query = "SELECT al.*, u.username, u.first_name, u.last_name 
                     FROM activity_logs al 
                     LEFT JOIN users u ON al.user_id = u.id 
                     ORDER BY al.created_at DESC 
                     LIMIT 10";

$activities_stmt = $db->prepare($activities_query);
$activities_stmt->execute();
$activities = $activities_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب أفضل الناشرين
$top_publishers_query = "SELECT u.id, u.username, u.first_name, u.last_name, 
                         COALESCE(SUM(ds.earnings), 0) as total_earnings,
                         COALESCE(SUM(ds.clicks), 0) as total_clicks,
                         COALESCE(SUM(ds.conversions), 0) as total_conversions
                         FROM users u 
                         LEFT JOIN daily_stats ds ON u.id = ds.user_id 
                         WHERE u.role = 'publisher' AND u.status = 'active'
                         GROUP BY u.id 
                         ORDER BY total_earnings DESC 
                         LIMIT 5";

$top_publishers_stmt = $db->prepare($top_publishers_query);
$top_publishers_stmt->execute();
$top_publishers = $top_publishers_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب أفضل العروض
$top_offers_query = "SELECT o.id, o.title, n.name as network_name, o.payout,
                     COALESCE(SUM(ds.clicks), 0) as total_clicks,
                     COALESCE(SUM(ds.conversions), 0) as total_conversions,
                     COALESCE(SUM(ds.earnings), 0) as total_earnings
                     FROM offers o 
                     JOIN networks n ON o.network_id = n.id
                     LEFT JOIN daily_stats ds ON o.id = ds.offer_id 
                     WHERE o.status = 'active'
                     GROUP BY o.id 
                     ORDER BY total_earnings DESC 
                     LIMIT 5";

$top_offers_stmt = $db->prepare($top_offers_query);
$top_offers_stmt->execute();
$top_offers = $top_offers_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة الإدارة</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData('dashboard', 'pdf')">
                                <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData('dashboard', 'excel')">
                                <i class="fas fa-file-excel me-1"></i>تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي الناشرين
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_publishers']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            العروض النشطة
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['active_offers']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            النقرات الشهرية
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['monthly_clicks']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            الأرباح الشهرية
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo CURRENCY_SYMBOL . number_format($stats['monthly_earnings'], 2); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row mb-4">
                    <div class="col-xl-8 col-lg-7">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">الأداء الشهري</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-area">
                                    <canvas id="monthlyChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-lg-5">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">توزيع الشبكات</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-pie pt-4 pb-2">
                                    <canvas id="networksChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الجداول -->
                <div class="row">
                    <!-- أفضل الناشرين -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">أفضل الناشرين</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>الناشر</th>
                                                <th>النقرات</th>
                                                <th>التحويلات</th>
                                                <th>الأرباح</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_publishers as $publisher): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($publisher['first_name'] . ' ' . $publisher['last_name']); ?></strong><br>
                                                    <small class="text-muted">@<?php echo htmlspecialchars($publisher['username']); ?></small>
                                                </td>
                                                <td><?php echo number_format($publisher['total_clicks']); ?></td>
                                                <td><?php echo number_format($publisher['total_conversions']); ?></td>
                                                <td><?php echo CURRENCY_SYMBOL . number_format($publisher['total_earnings'], 2); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center">
                                    <a href="users/" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>عرض جميع الناشرين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أفضل العروض -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">أفضل العروض</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>العرض</th>
                                                <th>النقرات</th>
                                                <th>التحويلات</th>
                                                <th>الأرباح</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($top_offers as $offer): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($offer['title']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($offer['network_name']); ?></small>
                                                </td>
                                                <td><?php echo number_format($offer['total_clicks']); ?></td>
                                                <td><?php echo number_format($offer['total_conversions']); ?></td>
                                                <td><?php echo CURRENCY_SYMBOL . number_format($offer['total_earnings'], 2); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center">
                                    <a href="offers/" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>عرض جميع العروض
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أحدث النشاطات -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أحدث النشاطات</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>النشاط</th>
                                        <th>الوصف</th>
                                        <th>التوقيت</th>
                                        <th>IP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($activities as $activity): ?>
                                    <tr>
                                        <td>
                                            <?php if ($activity['username']): ?>
                                                <strong><?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?></strong><br>
                                                <small class="text-muted">@<?php echo htmlspecialchars($activity['username']); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">مستخدم محذوف</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo htmlspecialchars($activity['action']); ?></span>
                                        </td>
                                        <td><?php echo htmlspecialchars($activity['description']); ?></td>
                                        <td>
                                            <small><?php echo date('Y-m-d H:i:s', strtotime($activity['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo htmlspecialchars($activity['ip_address']); ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center">
                            <a href="logs/" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>عرض جميع النشاطات
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // رسم بياني للأداء الشهري
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'النقرات',
                    data: [1200, 1900, 3000, 5000, 2000, 3000],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }, {
                    label: 'التحويلات',
                    data: [120, 190, 300, 500, 200, 300],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'الأداء الشهري'
                    }
                }
            }
        });

        // رسم بياني لتوزيع الشبكات
        const networksCtx = document.getElementById('networksChart').getContext('2d');
        new Chart(networksCtx, {
            type: 'doughnut',
            data: {
                labels: ['CPALead', 'MaxBounty', 'PeerFly', 'ClickDealer'],
                datasets: [{
                    data: [40, 30, 20, 10],
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 205, 86)',
                        'rgb(75, 192, 192)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'توزيع الشبكات'
                    }
                }
            }
        });
    </script>
</body>
</html>
