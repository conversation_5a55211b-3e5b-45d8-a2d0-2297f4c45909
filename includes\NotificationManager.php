<?php
/**
 * فئة إدارة الإشعارات
 */
class NotificationManager {
    private $db;
    private $settings;
    
    public function __construct($database) {
        $this->db = $database->getConnection();
        $this->loadSettings();
    }
    
    /**
     * تحميل إعدادات الإشعارات
     */
    private function loadSettings() {
        try {
            $query = "SELECT setting_key, setting_value, setting_type FROM notification_settings";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            $this->settings = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $value = $row['setting_value'];
                
                // تحويل القيم حسب النوع
                switch ($row['setting_type']) {
                    case 'boolean':
                        $value = (bool) $value;
                        break;
                    case 'integer':
                        $value = (int) $value;
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }
                
                $this->settings[$row['setting_key']] = $value;
            }
            
        } catch (PDOException $e) {
            logError("خطأ في تحميل إعدادات الإشعارات: " . $e->getMessage());
            $this->settings = $this->getDefaultSettings();
        }
    }
    
    /**
     * الحصول على الإعدادات الافتراضية
     */
    private function getDefaultSettings() {
        return [
            'notifications_enabled' => true,
            'popup_notifications' => true,
            'banner_notifications' => true,
            'sidebar_notifications' => true,
            'auto_hide_notifications' => true,
            'notification_hide_delay' => 10,
            'max_notifications_display' => 5,
            'notification_sound' => true,
            'offer_notifications' => true,
            'system_notifications' => true
        ];
    }
    
    /**
     * إنشاء إشعار جديد
     */
    public function createNotification($data) {
        try {
            $query = "INSERT INTO notifications (
                        title, message, type, target_audience, target_users, offer_id, 
                        priority, show_popup, show_banner, show_sidebar, auto_hide, 
                        hide_after_seconds, start_date, end_date, created_by
                      ) VALUES (
                        :title, :message, :type, :target_audience, :target_users, :offer_id,
                        :priority, :show_popup, :show_banner, :show_sidebar, :auto_hide,
                        :hide_after_seconds, :start_date, :end_date, :created_by
                      )";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':message', $data['message']);
            $stmt->bindParam(':type', $data['type']);
            $stmt->bindParam(':target_audience', $data['target_audience']);
            $stmt->bindParam(':target_users', $data['target_users']);
            $stmt->bindParam(':offer_id', $data['offer_id']);
            $stmt->bindParam(':priority', $data['priority']);
            $stmt->bindParam(':show_popup', $data['show_popup']);
            $stmt->bindParam(':show_banner', $data['show_banner']);
            $stmt->bindParam(':show_sidebar', $data['show_sidebar']);
            $stmt->bindParam(':auto_hide', $data['auto_hide']);
            $stmt->bindParam(':hide_after_seconds', $data['hide_after_seconds']);
            $stmt->bindParam(':start_date', $data['start_date']);
            $stmt->bindParam(':end_date', $data['end_date']);
            $stmt->bindParam(':created_by', $data['created_by']);
            
            if ($stmt->execute()) {
                $notification_id = $this->db->lastInsertId();
                $this->assignNotificationToUsers($notification_id, $data['target_audience'], $data['target_users']);
                return $notification_id;
            }
            
            return false;
            
        } catch (PDOException $e) {
            logError("خطأ في إنشاء الإشعار: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تعيين الإشعار للمستخدمين المستهدفين
     */
    private function assignNotificationToUsers($notification_id, $target_audience, $target_users = null) {
        try {
            $user_ids = [];
            
            switch ($target_audience) {
                case 'all':
                    $query = "SELECT id FROM users WHERE status = 'active'";
                    break;
                case 'active':
                    $query = "SELECT id FROM users WHERE status = 'active' AND login_count > 0";
                    break;
                case 'inactive':
                    $query = "SELECT id FROM users WHERE status = 'active' AND login_count = 0";
                    break;
                case 'new':
                    $query = "SELECT id FROM users WHERE status = 'active' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                    break;
                case 'custom':
                    if ($target_users) {
                        $user_ids = json_decode($target_users, true) ?: [];
                    }
                    break;
            }
            
            if ($target_audience !== 'custom') {
                $stmt = $this->db->prepare($query);
                $stmt->execute();
                $user_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
            }
            
            // إدراج سجلات user_notifications
            if (!empty($user_ids)) {
                $insert_query = "INSERT INTO user_notifications (user_id, notification_id) VALUES ";
                $values = [];
                $params = [];
                
                foreach ($user_ids as $index => $user_id) {
                    $values[] = "(:user_id_{$index}, :notification_id_{$index})";
                    $params["user_id_{$index}"] = $user_id;
                    $params["notification_id_{$index}"] = $notification_id;
                }
                
                $insert_query .= implode(', ', $values);
                $insert_stmt = $this->db->prepare($insert_query);
                
                foreach ($params as $key => $value) {
                    $insert_stmt->bindValue(":{$key}", $value);
                }
                
                $insert_stmt->execute();
            }
            
        } catch (PDOException $e) {
            logError("خطأ في تعيين الإشعار للمستخدمين: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على إشعارات المستخدم
     */
    public function getUserNotifications($user_id, $limit = null, $unread_only = false) {
        try {
            $limit_clause = $limit ? "LIMIT " . intval($limit) : "";
            $unread_clause = $unread_only ? "AND un.is_read = 0" : "";
            
            $query = "SELECT n.*, un.is_read, un.is_dismissed, un.read_at, un.dismissed_at,
                             o.title as offer_title, o.payout as offer_payout
                      FROM notifications n
                      INNER JOIN user_notifications un ON n.id = un.notification_id
                      LEFT JOIN offers o ON n.offer_id = o.id
                      WHERE un.user_id = :user_id 
                      AND n.is_active = 1
                      AND (n.start_date IS NULL OR n.start_date <= NOW())
                      AND (n.end_date IS NULL OR n.end_date >= NOW())
                      {$unread_clause}
                      ORDER BY n.priority DESC, n.created_at DESC
                      {$limit_clause}";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            logError("خطأ في جلب إشعارات المستخدم: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * عدد الإشعارات غير المقروءة
     */
    public function getUnreadCount($user_id) {
        try {
            $query = "SELECT COUNT(*) 
                      FROM notifications n
                      INNER JOIN user_notifications un ON n.id = un.notification_id
                      WHERE un.user_id = :user_id 
                      AND un.is_read = 0
                      AND n.is_active = 1
                      AND (n.start_date IS NULL OR n.start_date <= NOW())
                      AND (n.end_date IS NULL OR n.end_date >= NOW())";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
            return $stmt->fetchColumn();
            
        } catch (PDOException $e) {
            logError("خطأ في عدد الإشعارات غير المقروءة: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * تحديد الإشعار كمقروء
     */
    public function markAsRead($user_id, $notification_id) {
        try {
            $query = "UPDATE user_notifications 
                      SET is_read = 1, read_at = NOW() 
                      WHERE user_id = :user_id AND notification_id = :notification_id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':notification_id', $notification_id);
            
            return $stmt->execute();
            
        } catch (PDOException $e) {
            logError("خطأ في تحديد الإشعار كمقروء: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إخفاء الإشعار
     */
    public function dismissNotification($user_id, $notification_id) {
        try {
            $query = "UPDATE user_notifications 
                      SET is_dismissed = 1, dismissed_at = NOW() 
                      WHERE user_id = :user_id AND notification_id = :notification_id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':notification_id', $notification_id);
            
            return $stmt->execute();
            
        } catch (PDOException $e) {
            logError("خطأ في إخفاء الإشعار: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث إعدادات الإشعارات
     */
    public function updateSettings($settings) {
        try {
            $this->db->beginTransaction();
            
            foreach ($settings as $key => $value) {
                $query = "UPDATE notification_settings 
                          SET setting_value = :value, updated_at = NOW() 
                          WHERE setting_key = :key";
                
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':key', $key);
                $stmt->bindParam(':value', $value);
                $stmt->execute();
            }
            
            $this->db->commit();
            $this->loadSettings(); // إعادة تحميل الإعدادات
            
            return true;
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            logError("خطأ في تحديث إعدادات الإشعارات: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إعداد معين
     */
    public function getSetting($key, $default = null) {
        return $this->settings[$key] ?? $default;
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public function getAllSettings() {
        return $this->settings;
    }
    
    /**
     * إنشاء إشعار عرض جديد
     */
    public function createOfferNotification($offer_id, $created_by) {
        try {
            // جلب بيانات العرض
            $offer_query = "SELECT title, payout, countries FROM offers WHERE id = :offer_id";
            $offer_stmt = $this->db->prepare($offer_query);
            $offer_stmt->bindParam(':offer_id', $offer_id);
            $offer_stmt->execute();
            $offer = $offer_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$offer) {
                return false;
            }

            $notification_data = [
                'title' => 'عرض جديد متاح!',
                'message' => "عرض جديد: {$offer['title']} - العمولة: " . CURRENCY_SYMBOL . number_format($offer['payout'], 2),
                'type' => 'offer',
                'target_audience' => 'active',
                'target_users' => null,
                'offer_id' => $offer_id,
                'priority' => 'high',
                'show_popup' => $this->getSetting('popup_notifications', true),
                'show_banner' => $this->getSetting('banner_notifications', true),
                'show_sidebar' => $this->getSetting('sidebar_notifications', true),
                'auto_hide' => $this->getSetting('auto_hide_notifications', true),
                'hide_after_seconds' => $this->getSetting('notification_hide_delay', 10),
                'start_date' => date('Y-m-d H:i:s'),
                'end_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
                'created_by' => $created_by
            ];

            return $this->createNotification($notification_data);

        } catch (PDOException $e) {
            logError("خطأ في إنشاء إشعار العرض: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء إشعار إكمال التحويل
     */
    public function createConversionNotification($user_id, $offer_data, $earnings, $transaction_id) {
        try {
            // إشعار للمستخدم
            $user_notification_data = [
                'title' => '🎉 مبروك! تم إكمال العرض بنجاح',
                'message' => "تم إكمال العرض: {$offer_data['title']} وحصلت على عمولة " . CURRENCY_SYMBOL . number_format($earnings, 2),
                'type' => 'success',
                'target_audience' => 'custom',
                'target_users' => json_encode([$user_id]),
                'offer_id' => $offer_data['id'],
                'priority' => 'high',
                'show_popup' => true,
                'show_banner' => true,
                'show_sidebar' => true,
                'auto_hide' => false,
                'hide_after_seconds' => 0,
                'start_date' => date('Y-m-d H:i:s'),
                'end_date' => date('Y-m-d H:i:s', strtotime('+30 days')),
                'created_by' => 1
            ];

            $user_notification_id = $this->createNotification($user_notification_data);

            // إشعار للمدير
            $admin_notification_data = [
                'title' => '💰 تحويل جديد في النظام',
                'message' => "المستخدم {$offer_data['username']} أكمل العرض: {$offer_data['title']} - العمولة: " . CURRENCY_SYMBOL . number_format($earnings, 2),
                'type' => 'info',
                'target_audience' => 'custom',
                'target_users' => json_encode([1]), // المدير
                'offer_id' => $offer_data['id'],
                'priority' => 'medium',
                'show_popup' => true,
                'show_banner' => false,
                'show_sidebar' => true,
                'auto_hide' => true,
                'hide_after_seconds' => 10,
                'start_date' => date('Y-m-d H:i:s'),
                'end_date' => date('Y-m-d H:i:s', strtotime('+7 days')),
                'created_by' => 1
            ];

            $admin_notification_id = $this->createNotification($admin_notification_data);

            return ['user' => $user_notification_id, 'admin' => $admin_notification_id];

        } catch (PDOException $e) {
            logError("خطأ في إنشاء إشعار التحويل: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء إشعار رفض التحويل
     */
    public function createRejectedConversionNotification($user_id, $offer_data, $reason = '') {
        try {
            $message = "تم رفض التحويل للعرض: {$offer_data['title']}";
            if ($reason) {
                $message .= " - السبب: {$reason}";
            }

            $notification_data = [
                'title' => '❌ تم رفض التحويل',
                'message' => $message,
                'type' => 'danger',
                'target_audience' => 'custom',
                'target_users' => json_encode([$user_id]),
                'offer_id' => $offer_data['id'],
                'priority' => 'high',
                'show_popup' => true,
                'show_banner' => true,
                'show_sidebar' => true,
                'auto_hide' => false,
                'hide_after_seconds' => 0,
                'start_date' => date('Y-m-d H:i:s'),
                'end_date' => date('Y-m-d H:i:s', strtotime('+30 days')),
                'created_by' => 1
            ];

            return $this->createNotification($notification_data);

        } catch (PDOException $e) {
            logError("خطأ في إنشاء إشعار رفض التحويل: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء إشعار تحديث حالة التحويل
     */
    public function createConversionStatusUpdateNotification($user_id, $offer_data, $old_status, $new_status, $earnings = 0) {
        try {
            $status_messages = [
                'pending' => 'في الانتظار',
                'approved' => 'معتمد',
                'rejected' => 'مرفوض',
                'hold' => 'معلق'
            ];

            $old_status_text = $status_messages[$old_status] ?? $old_status;
            $new_status_text = $status_messages[$new_status] ?? $new_status;

            $message = "تم تحديث حالة التحويل للعرض: {$offer_data['title']} من ({$old_status_text}) إلى ({$new_status_text})";

            if ($new_status === 'approved' && $earnings > 0) {
                $message .= " - العمولة: " . CURRENCY_SYMBOL . number_format($earnings, 2);
            }

            $type = 'info';
            $title = '📝 تحديث حالة التحويل';

            if ($new_status === 'approved') {
                $type = 'success';
                $title = '✅ تم اعتماد التحويل';
            } elseif ($new_status === 'rejected') {
                $type = 'danger';
                $title = '❌ تم رفض التحويل';
            }

            $notification_data = [
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'target_audience' => 'custom',
                'target_users' => json_encode([$user_id]),
                'offer_id' => $offer_data['id'],
                'priority' => 'medium',
                'show_popup' => true,
                'show_banner' => true,
                'show_sidebar' => true,
                'auto_hide' => $type === 'info',
                'hide_after_seconds' => $type === 'info' ? 10 : 0,
                'start_date' => date('Y-m-d H:i:s'),
                'end_date' => date('Y-m-d H:i:s', strtotime('+15 days')),
                'created_by' => 1
            ];

            return $this->createNotification($notification_data);

        } catch (PDOException $e) {
            logError("خطأ في إنشاء إشعار تحديث حالة التحويل: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إحصائيات الإشعارات
     */
    public function getNotificationStats() {
        try {
            $stats_query = "SELECT 
                              COUNT(*) as total_notifications,
                              COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_notifications,
                              COUNT(CASE WHEN type = 'offer' THEN 1 END) as offer_notifications,
                              COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as notifications_today,
                              COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as notifications_week
                            FROM notifications";
            
            $stats_stmt = $this->db->prepare($stats_query);
            $stats_stmt->execute();
            $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
            
            // إحصائيات قراءة الإشعارات
            $read_stats_query = "SELECT 
                                   COUNT(*) as total_user_notifications,
                                   COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_notifications,
                                   COUNT(CASE WHEN is_dismissed = 1 THEN 1 END) as dismissed_notifications
                                 FROM user_notifications";
            
            $read_stats_stmt = $this->db->prepare($read_stats_query);
            $read_stats_stmt->execute();
            $read_stats = $read_stats_stmt->fetch(PDO::FETCH_ASSOC);
            
            return array_merge($stats, $read_stats);
            
        } catch (PDOException $e) {
            logError("خطأ في إحصائيات الإشعارات: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * حذف الإشعارات المنتهية الصلاحية
     */
    public function cleanExpiredNotifications() {
        try {
            $query = "DELETE FROM notifications 
                      WHERE end_date IS NOT NULL 
                      AND end_date < NOW()
                      AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->rowCount();
            
        } catch (PDOException $e) {
            logError("خطأ في حذف الإشعارات المنتهية: " . $e->getMessage());
            return 0;
        }
    }
}
?>
