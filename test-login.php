<?php
/**
 * اختبار نظام تسجيل الدخول
 */

require_once 'config/config.php';

echo "<h2>🔐 اختبار نظام تسجيل الدخول</h2>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h3>1. اختبار الاتصال بقاعدة البيانات:</h3>";
    echo "✅ تم الاتصال بنجاح!<br><br>";
    
    echo "<h3>2. فحص جدول المستخدمين:</h3>";
    
    // فحص وجود جدول المستخدمين
    $check_table = $db->query("SHOW TABLES LIKE 'users'");
    if ($check_table->rowCount() > 0) {
        echo "✅ جدول المستخدمين موجود<br>";
        
        // فحص بنية الجدول
        $columns = $db->query("DESCRIBE users")->fetchAll(PDO::FETCH_ASSOC);
        echo "📋 أعمدة الجدول:<br>";
        foreach ($columns as $column) {
            echo "- {$column['Field']} ({$column['Type']})<br>";
        }
        echo "<br>";
        
        // فحص المستخدمين الموجودين
        $users = $db->query("SELECT id, username, role, status, created_at FROM users")->fetchAll(PDO::FETCH_ASSOC);
        echo "<h4>👥 المستخدمين الموجودين (" . count($users) . "):</h4>";
        
        if (count($users) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الدور</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['role']}</td>";
                echo "<td>{$user['status']}</td>";
                echo "<td>{$user['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        } else {
            echo "⚠️ لا يوجد مستخدمين في النظام<br><br>";
        }
        
    } else {
        echo "❌ جدول المستخدمين غير موجود<br><br>";
    }
    
    echo "<h3>3. اختبار إنشاء مستخدم تجريبي:</h3>";
    
    // التحقق من وجود مستخدم تجريبي
    $test_user_check = $db->prepare("SELECT id FROM users WHERE username = 'test'");
    $test_user_check->execute();
    
    if ($test_user_check->rowCount() == 0) {
        // إنشاء مستخدم تجريبي
        $test_password = password_hash('test', PASSWORD_DEFAULT);
        $insert_test = $db->prepare("INSERT INTO users (username, password, role, status) VALUES ('test', ?, 'publisher', 'active')");
        $insert_test->execute([$test_password]);
        echo "✅ تم إنشاء مستخدم تجريبي: username=test, password=test<br>";
    } else {
        echo "✅ المستخدم التجريبي موجود بالفعل: username=test, password=test<br>";
    }
    
    echo "<h3>4. اختبار تسجيل الدخول:</h3>";
    
    // محاكاة تسجيل دخول
    $login_username = 'test';
    $login_password = 'test';
    
    $login_query = "SELECT id, username, password, role, status FROM users WHERE username = :username";
    $login_stmt = $db->prepare($login_query);
    $login_stmt->bindParam(':username', $login_username);
    $login_stmt->execute();
    
    if ($login_stmt->rowCount() === 1) {
        $user = $login_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user['status'] === 'active') {
            if (password_verify($login_password, $user['password'])) {
                echo "✅ تسجيل الدخول نجح للمستخدم: {$user['username']}<br>";
                echo "🔑 معرف المستخدم: {$user['id']}<br>";
                echo "👤 الدور: {$user['role']}<br>";
            } else {
                echo "❌ كلمة المرور غير صحيحة<br>";
            }
        } else {
            echo "❌ الحساب غير نشط<br>";
        }
    } else {
        echo "❌ المستخدم غير موجود<br>";
    }
    
    echo "<hr>";
    echo "<h3>🎯 روابط الاختبار:</h3>";
    echo "<p><a href='auth/login.php' target='_blank'>🔐 صفحة تسجيل الدخول</a></p>";
    echo "<p><a href='auth/quick-register.php' target='_blank'>⚡ التسجيل السريع</a></p>";
    echo "<p><a href='index.php' target='_blank'>🏠 الصفحة الرئيسية</a></p>";
    
    echo "<h4>📝 بيانات الاختبار:</h4>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo "<strong>اسم المستخدم:</strong> test<br>";
    echo "<strong>كلمة المرور:</strong> test<br>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;'>";
    echo "<strong>رسالة الخطأ:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>رقم الخطأ:</strong> " . $e->getCode() . "<br>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3, h4 {
    color: #333;
}
table {
    background: white;
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    background: #007bff;
    color: white;
}
a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
}
a:hover {
    text-decoration: underline;
}
</style>
